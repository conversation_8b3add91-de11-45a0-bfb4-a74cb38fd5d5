package com.get.salecenter.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.annotion.IgnoreRemind;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.GetDateUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.start.constant.AppConstant;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestHeaderHandler;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.vo.InstitutionCourseVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.remindercenter.dto.MailDto;
import com.get.salecenter.dao.sale.PayablePlanMapper;
import com.get.salecenter.dao.sale.ReceivablePlanMapper;
import com.get.salecenter.dao.sale.StudentMapper;
import com.get.salecenter.dao.sale.StudentOfferItemDeferEntranceTimeMapper;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.dao.sale.StudentOfferItemStepMapper;
import com.get.salecenter.dao.sale.StudentOfferMapper;
import com.get.salecenter.dao.sale.StudentOfferNoticeMapper;
import com.get.salecenter.dao.sale.StudentProjectRoleMapper;
import com.get.salecenter.dao.sale.StudentProjectRoleStaffMapper;
import com.get.salecenter.dto.*;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.StudentProjectRoleStaffVo;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.EventSummary;
import com.get.salecenter.entity.PayablePlan;
import com.get.salecenter.entity.ReceivablePlan;
import com.get.salecenter.entity.Student;
import com.get.salecenter.entity.StudentOffer;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.entity.StudentOfferItemStep;
import com.get.salecenter.entity.StudentOfferNotice;
import com.get.salecenter.entity.StudentProjectRole;
import com.get.salecenter.entity.StudentProjectRoleStaff;
import com.get.salecenter.service.AsyncReminderService;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.IEventSummaryService;
import com.get.salecenter.service.IPayablePlanService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.get.core.tool.utils.DateUtil.now;

/**
 * @author: Hardy
 * @create: 2022/7/1 10:46
 * @verison: 1.0
 * @description:
 */
@Service
@Slf4j
public class AsyncReminderServiceImpl implements AsyncReminderService {

    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private StudentOfferMapper studentOfferMapper;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IEventSummaryService eventSummaryService;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IPayablePlanService payablePlanService;
    @Resource
    private StudentMapper studentMapper;
    @Resource
    private IAgentService agentService;
    @Resource
    private ReceivablePlanMapper receivablePlanMapper;
    @Resource
    private PayablePlanMapper payablePlanMapper;
    @Resource
    private StudentOfferItemStepMapper studentOfferItemStepMapper;
    @Resource
    private StudentProjectRoleMapper studentProjectRoleMapper;
    @Resource
    private StudentOfferItemDeferEntranceTimeMapper studentOfferItemDeferEntranceTimeMapper;
    @Resource
    private StudentProjectRoleStaffMapper studentProjectRoleStaffMapper;
    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;
    @Resource
    private StudentOfferNoticeMapper studentOfferNoticeMapper;
    @Resource
    private UtilService utilService;

    @Value("${domainName}")
    private String domainName;

    @Async("saleTaskExecutor")
    @Override
    public void doAddReminders(Map<String, String> headerMap, EventBillReminderDto eventBillReminderVo,String title) {
        Set<Long> staffIds = eventBillReminderVo.getFkStaffIds();
        if (GeneralTool.isEmpty(staffIds)){
            return;
        }
        RequestHeaderHandler.setHeaderMap(headerMap);

//        Map<String, String> map = new HashMap<>(12);
        Map<String, String> map = getTemplateFieldMap(eventBillReminderVo);

        String institutionProviderName = null;
        Result<String> institutionProviderNameResult = institutionCenterClient.getInstitutionProviderName(eventBillReminderVo.getFkInstitutionProviderId());
        if (institutionProviderNameResult.isSuccess()&& GeneralTool.isNotEmpty(institutionProviderNameResult.getData())){
            institutionProviderName = institutionProviderNameResult.getData();
        }
        if (GeneralTool.isNotEmpty(institutionProviderName)){
            map.put("fkInstitutionProviderName",institutionProviderName);
        }else {
            map.put("fkInstitutionProviderName","");
        }

        String fkAreaCountryName = null;
        if (GeneralTool.isNotEmpty(eventBillReminderVo.getFkAreaCountryIdList())){
            Map<Long, String> countryNameMap = institutionCenterClient.getCountryNamesByIds(new HashSet<>(eventBillReminderVo.getFkAreaCountryIdList())).getData();
            StringJoiner stringJoiner = new StringJoiner(",");
            for (Long countryId : eventBillReminderVo.getFkAreaCountryIdList()) {
                if (GeneralTool.isNotEmpty(countryNameMap)&&GeneralTool.isNotEmpty(countryNameMap.get(countryId))){
                    stringJoiner.add(countryNameMap.get(countryId));
                }
            }
            fkAreaCountryName = stringJoiner.toString();
        }
        if (GeneralTool.isNotEmpty(fkAreaCountryName)){
            map.put("fkAreaCountryName",fkAreaCountryName);
        }else {
            map.put("fkAreaCountryName","");
        }

        Set<String> nums = new HashSet<>(2);
        nums.add(eventBillReminderVo.getFkCurrencyTypeNumEvent());
        nums.add(eventBillReminderVo.getFkCurrencyTypeNumInvoice());
        Map<String, String> currencyNameMap = financeCenterClient.getCurrencyNamesByNums(nums).getData();

        if (GeneralTool.isNotEmpty(currencyNameMap)){
            if (GeneralTool.isNotEmpty(currencyNameMap.get(eventBillReminderVo.getFkCurrencyTypeNumEvent()))){
                map.put("fkCurrencyTypeNumEventName",currencyNameMap.get(eventBillReminderVo.getFkCurrencyTypeNumEvent()));
            }else {
                map.put("fkCurrencyTypeNumEventName","");
            }
            if (GeneralTool.isNotEmpty(currencyNameMap.get(eventBillReminderVo.getFkCurrencyTypeNumInvoice()))){
                map.put("fkCurrencyTypeNumInvoiceName",currencyNameMap.get(eventBillReminderVo.getFkCurrencyTypeNumInvoice()));
            }else {
                map.put("fkCurrencyTypeNumInvoiceName","");
            }
        }

        //获取所有公司
        Map<Long, String> companyMap = getCompanyMap();
        if(GeneralTool.isNotEmpty(eventBillReminderVo.getFkCompanyId())){
            String companyName = companyMap.get(eventBillReminderVo.getFkCompanyId());
            map.put("companyName",companyName);
        }
        String fkEventSummaryName = null;
        if (GeneralTool.isNotEmpty(eventBillReminderVo.getFkEventSummaryIdList())){
            LambdaQueryWrapper<EventSummary> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.in(EventSummary::getId,eventBillReminderVo.getFkEventSummaryIdList());
            List<EventSummary> eventSummaries = eventSummaryService.getEventSummariesByCondition(lambdaQueryWrapper);

            if (GeneralTool.isNotEmpty(eventSummaries)){
                StringJoiner stringJoiner = new StringJoiner(",");
                for (EventSummary eventSummary : eventSummaries) {
                    stringJoiner.add(eventSummary.getEventSummary());
                }
                fkEventSummaryName = stringJoiner.toString();

            }

        }

        if (GeneralTool.isNotEmpty(fkEventSummaryName)){
            map.put("fkEventSummaryName",fkEventSummaryName);
        }else {
            map.put("fkEventSummaryName","");
        }

        if(GeneralTool.isNotEmpty(staffIds)){
            map.put("staffIdList",staffIds.toString());
        }else {
            map.put("staffIdList","");
        }

        String taskRemark = JSON.toJSONString(map);
        taskRemark = "&lt;div style=\"display:none;\"&gt;"+taskRemark+"&lt;/div&gt;";

//        String taskRemark = MyStringUtils.getReminderTemplate(map, SaleCenterConstant.EVENT_BILL_REMINDER_CONTENT);
        String summary = eventBillReminderVo.getInvoiceSummary();
        ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
        String domain = configVo.getValue1();
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(eventBillReminderVo.getFkCompanyId());
        String text = null;
        if (!versionValue2.equals("en")) {
            text = title+summary+"，活动汇总费用计划";
        }else {
            text = title+summary+"，Activity Summary Cost Plan";
        }
        List<EmailSenderQueue> list = new ArrayList<>();
        if (GeneralTool.isNotEmpty(staffIds)){

            EmailSenderQueue  emailSenderQueue = new EmailSenderQueue();
            emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.EVENT_FEE_PLAN_CHANGE_REMINDER.getEmailTemplateKey());
            emailSenderQueue.setEmailTitle(text);
            emailSenderQueue.setEmailParameter(taskRemark);
            emailSenderQueue.setFkTableName(TableEnum.SALE_EVENT_BILL.key);
            emailSenderQueue.setFkTableId(eventBillReminderVo.getFkEventBillId());
            emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
            emailSenderQueue.setOperationTime(new Date());
            list.add(emailSenderQueue);
//            List<RemindTaskDto> remindTaskVos = new ArrayList<>();
//            for (Long fkStaffIdNotice : staffIds) {
//                //summary+"，活动汇总费用计划【已发起/已更新/已作废】
//                //发送提醒通知
//                RemindTaskDto remindTaskVo = new RemindTaskDto();
//                //TODO EVENT_BILL_NOTICE
//                remindTaskVo.setTaskTitle(title+summary+"，活动汇总费用计划");
//                remindTaskVo.setTaskRemark(taskRemark);
//                //邮件方式发送
//                remindTaskVo.setRemindMethod("1");
//                //默认设置执行中
//                remindTaskVo.setStatus(1);
//                //默认背景颜色
//                remindTaskVo.setFkRemindEventTypeKey("EVENT_BILL_NOTICE");
//                remindTaskVo.setTaskBgColor("#3788d8");
//                remindTaskVo.setFkStaffId(fkStaffIdNotice);
//                remindTaskVo.setStartTime(new Date());
//                remindTaskVo.setAdvanceDays("0");
//                remindTaskVo.setFkTableName(TableEnum.SALE_EVENT_BILL.key);
//                remindTaskVo.setFkTableId(eventBillReminderVo.getFkEventBillId());
//                remindTaskVo.setFkDbName(ProjectKeyEnum.REMINDER_CENTER.key);
//                String link = domain+"/sales-center_event_activity-cost_collection-plan-details/"+eventBillReminderVo.getFkEventBillId();
//                //暂时注释邮件链接
//                remindTaskVo.setTaskLink(link);
//                remindTaskVos.add(remindTaskVo);
            }

            try {
                //log.info("==========================================={}",remindTaskVos.get(0).getTaskRemark());
                //reminderCenterClient.batchAdd(remindTaskVos);
                log.info("==========================================={}",taskRemark);
               reminderCenterClient.batchAddEmailQueue(list);
            }catch (Exception e){
                log.error("添加发起通知失败：",e);
            }

    }

    @Async("saleTaskExecutor")
    @Override
    public void doAddEventIncentiveReminders(Map<String, String> headerMap, UserInfo user, EventIncentiveReminderDto eventIncentiveReminderVo, String title) {
        log.info("=================发送活动推广活动提醒开始：id={}==========================",eventIncentiveReminderVo.getFkEventIncentiveId());

        RequestHeaderHandler.setHeaderMap(headerMap);
        Long staffId = user.getStaffId();

        Map<String, String> map = getTemplateFieldMap(eventIncentiveReminderVo);

        String institutionProviderName = null;
        Result<String> institutionProviderNameResult = institutionCenterClient.getInstitutionProviderName(eventIncentiveReminderVo.getFkInstitutionProviderId());
        if (institutionProviderNameResult.isSuccess()&& GeneralTool.isNotEmpty(institutionProviderNameResult.getData())){
            institutionProviderName = institutionProviderNameResult.getData();
        }
        if (GeneralTool.isNotEmpty(institutionProviderName)){
            map.put("fkInstitutionProviderName",institutionProviderName);
        }else {
            map.put("fkInstitutionProviderName","");
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        if (GeneralTool.isNotEmpty(eventIncentiveReminderVo.getEventStartTime())) {
            map.put("startTime", sdf.format(eventIncentiveReminderVo.getEventStartTime()));
        } else {
            map.put("startTime", "");
        }

        String fkAreaCountryName = null;
        if (GeneralTool.isNotEmpty(eventIncentiveReminderVo.getFkAreaCountryIdList())){
            Map<Long, String> countryNameMap = institutionCenterClient.getCountryNamesByIds(new HashSet<>(eventIncentiveReminderVo.getFkAreaCountryIdList())).getData();
            StringJoiner stringJoiner = new StringJoiner(",");
            for (Long countryId : eventIncentiveReminderVo.getFkAreaCountryIdList()) {
                if (GeneralTool.isNotEmpty(countryNameMap)&&GeneralTool.isNotEmpty(countryNameMap.get(countryId))){
                    stringJoiner.add(countryNameMap.get(countryId));
                }
            }
            fkAreaCountryName = stringJoiner.toString();
        }
        if (GeneralTool.isNotEmpty(fkAreaCountryName)){
            map.put("fkAreaCountryName",fkAreaCountryName);
        }else {
            map.put("fkAreaCountryName","");
        }
        if (GeneralTool.isNotEmpty(staffId)) {
            map.put("staffId",staffId.toString());
        }else {
            map.put("staffId","");
        }

        String taskRemark = JSON.toJSONString(map);
       // taskRemark = "&lt;div style=\"display:none;\"&gt;"+taskRemark+"&lt;/div&gt;";

        //List<RemindTaskDto> remindTaskVos = new ArrayList<>();

        String domain = "";
//        Properties props = System.getProperties();
//        String profile = props.getProperty("spring.profiles.active");
//        if (GeneralTool.isNotEmpty(profile)) {
//            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.IAE_CODE)|| profile.equals(AppConstant.TW_CODE)) {
//                ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
//                if (GeneralTool.isNotEmpty(configDto)){
//                    String configJson = configDto.getValue2();
//                    JSONObject configJsonObject = JSON.parseObject(configJson);
//                    if (SecureUtil.getFkCompanyId().equals(3L)){
//                        domain = configJsonObject.getString("IAE");
//                    }else {
//                        domain = configJsonObject.getString("OTHER");
//                    }
//                }
//            }else {
//                Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
//                if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
//                    domain = domainNameResult.getData();
//                }
//            }
//        }
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.FILE_SRC_PREFIX.key, 2).getData();
        String configValue2 = companyConfigMap.get(SecureUtil.getFkCompanyId());
        if (GeneralTool.isNotEmpty(configValue2)){
            domain = configValue2;
        } else {
            Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
            if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
                domain = domainNameResult.getData();
            }
        }
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(user.getFkCompanyId());
        String text = null;
        if (!versionValue2.equals("en")) {
            text = "【奖励推广活动核对提醒】" + eventIncentiveReminderVo.getEventTitle();
        }else {
            text =  "[Reward promotion activity verification reminder]" + eventIncentiveReminderVo.getEventTitle();
        }

        List<EmailSenderQueue> list = new ArrayList<>();
        EmailSenderQueue  emailSenderQueue = new EmailSenderQueue();
        emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.REWARD_PROMOTION_ACTIVITY_REMINDER.getEmailTemplateKey());
        emailSenderQueue.setEmailTitle(text);
        emailSenderQueue.setEmailParameter(taskRemark);
        emailSenderQueue.setFkTableName(TableEnum.SALE_EVENT_INCENTIVE.key);
        emailSenderQueue.setFkTableId(eventIncentiveReminderVo.getFkEventIncentiveId());
        emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
        emailSenderQueue.setOperationTime(GetDateUtil.getAdvanceDateByDay(eventIncentiveReminderVo.getSuggestCheckTime(),1L));
        list.add(emailSenderQueue);

        //发送提醒通知
        List<RemindTaskDto> remindTaskVos = new ArrayList<>();
        RemindTaskDto remindTaskVo = new RemindTaskDto();
        remindTaskVo.setTaskTitle(title);
        remindTaskVo.setTaskRemark(taskRemark);
        //邮件方式发送
        remindTaskVo.setRemindMethod("1");
        //默认设置执行中
        remindTaskVo.setStatus(1);
        //默认背景颜色
        remindTaskVo.setFkRemindEventTypeKey(EmailTemplateEnum.REWARD_PROMOTION_ACTIVITY_REMINDER.getEmailTemplateKey());
        remindTaskVo.setTaskBgColor("#3788d8");
        remindTaskVo.setFkStaffId(staffId);
        remindTaskVo.setStartTime(GetDateUtil.getAdvanceDateByDay(eventIncentiveReminderVo.getSuggestCheckTime(),1L));
        remindTaskVo.setAdvanceDays("1");
        remindTaskVo.setFkTableName(TableEnum.SALE_EVENT_INCENTIVE.key);
        remindTaskVo.setFkTableId(eventIncentiveReminderVo.getFkEventIncentiveId());
        remindTaskVo.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
        remindTaskVos.add(remindTaskVo);
        reminderCenterClient.batchAddTask(remindTaskVos);
////        String link = domain+"/sales-center/event/activity-reward-promotion/activity-reward-promotion-details/"+eventIncentiveReminderVo.getFkEventIncentiveId();
//        //暂时注释邮件链接
//        //remindTaskVo.setTaskLink(link);
//        remindTaskVos.add(remindTaskVo);
        try {
//            reminderCenterClient.batchAdd(remindTaskVos);
//            log.info("=================发送活动推广活动提醒结束=========================={}",remindTaskVos.get(0).getTaskRemark());
            log.info("=================发送活动推广活动提醒结束=========================={}",taskRemark);
            reminderCenterClient.batchAddEmailQueue(list);
        }catch (Exception e){
            log.error("添加发起通知失败：",e);
        }

    }

    private String getRPStatus(List<PayablePlan> planList,List<ReceivablePlan> receivablePlans){
        if (GeneralTool.isNotEmpty(planList) && GeneralTool.isNotEmpty(receivablePlans))
            return "已创建应收应付";
        if (GeneralTool.isEmpty(planList) && GeneralTool.isNotEmpty(receivablePlans))
            return "有应收无应付";
        if (GeneralTool.isEmpty(planList) && GeneralTool.isEmpty(receivablePlans))
            return "未创建应收应付";
        return "";
    }

    /**
     * Author Cream   （废弃）
     * Description : //渠道变更发送邮件，附带申请计划link
     * Date 2023/7/27 14:39
     * Params:
     * Return
     */
//    @Async("saleTaskExecutor")
//    @Override
//    public void sendOfferItemEmail(EventOfferPlanDto offerPlanVo, StudentOfferItemDto offerItemDto, Map<String, String> headerMap, Long staffId, Long fkCompanyId) {
//        RequestHeaderHandler.setHeaderMap(headerMap);
//        List<PayablePlan> planList = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery()
//                .eq(PayablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_OFFER_ITEM.key).eq(PayablePlan::getFkTypeTargetId, offerItemDto.getId()));
//        List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectList(Wrappers.<ReceivablePlan>lambdaQuery()
//                .eq(ReceivablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_OFFER_ITEM.key).eq(ReceivablePlan::getFkTypeTargetId, offerItemDto.getId()));
//        Long offerItemStepId = offerItemDto.getFkStudentOfferItemStepId();
//        if (offerItemStepId!=null && (offerItemStepId==5 || offerItemStepId == 6 || offerItemStepId==8)) {
//            Long channelId = offerItemDto.getFkInstitutionChannelId();
//            Long providerId = offerItemDto.getFkInstitutionProviderId();
//            Long newPid = offerPlanVo.getFkInstitutionProviderId();
//            Long newCid = offerPlanVo.getFkInstitutionChannelId();
//            if ((newCid != null && !newCid.equals(channelId))
//                    || (newPid != null && !newPid.equals(providerId))) {
//                Set<Long> s1 = new HashSet<>(2);
//                s1.add(channelId);
//                s1.add(newCid);
//                Set<Long> s2 = new HashSet<>(2);
//                s2.add(providerId);
//                s2.add(newPid);
//                Result<Map<Long, String>> r1 = institutionCenterClient.getChannelByIds(s1);
//                Result<Map<Long, String>> r2 = institutionCenterClient.getInstitutionProviderNamesByIds(s2);
//                if (r1.isSuccess() && r2.isSuccess()) {
//                    Map<Long, String> data = r1.getData();
//                    Map<Long, String> r2Data = r2.getData();
//                    StringBuilder b1 = new StringBuilder();
//                    if (data.containsKey(channelId)) {
//                        b1.append(data.get(channelId)).append("（旧渠道）");
//                    } else {
//                        b1.append("无渠道（旧渠道）");
//                    }
//                    b1.append("===>").append(StringUtils.isBlank(data.get(newCid)) ? "无渠道" : data.get(newCid)).append("（新渠道）");
//                    StringBuilder b2 = new StringBuilder();
//                    if (r2Data.containsKey(providerId)) {
//                        b2.append(r2Data.get(providerId)).append("（旧提供商）");
//                    } else {
//                        b2.append("无提供商（旧提供商）");
//                    }
//                    b2.append("===>").append(StringUtils.isBlank(r2Data.get(newPid)) ? "无提供商" : r2Data.get(newPid)).append("（新提供商）");
//                    offerPlanVo.setChannelInfo(b1.toString());
//                    offerPlanVo.setProviderInfo(b2.toString());
//                    StudentOfferItemStep offerItemStep = studentOfferItemStepMapper.selectById(offerItemStepId);
//                    offerPlanVo.setCourseStatus(offerItemStep.getStepName()+"\t ，"+getRPStatus(planList,receivablePlans));
////                    ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
////                    String domain = configDto.getValue1();
//                    String domain = "";
//                    ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
//                    if (GeneralTool.isNotEmpty(configDto)){
//                        String configJson = configDto.getValue2();
//                        JSONObject configJsonObject = JSON.parseObject(configJson);
//                        if (fkCompanyId.equals(3L)){
//                            domain = configJsonObject.getString("IAE");
//                        }else {
//                            domain = configJsonObject.getString("OTHER");
//                        }
//                    }
//                    String link = domain + "/sales-center/student-management/student-offer-item-detail/" + offerItemDto.getId();
//                    offerPlanVo.setTaskLink(link);
//                    CommonUtil<EventOfferPlanDto> commonUtil = new CommonUtil<>();
//                    Map<String, String> templateFieldMap = commonUtil.getTemplateFieldMap(offerPlanVo);
//                    templateFieldMap.put("openingTime", new SimpleDateFormat("yyyy-MM-dd").format(offerItemDto.getDeferOpeningTime()));
//                    String taskRemark = JSON.toJSONString(templateFieldMap);
//                    String title = "【申请渠道变更】" + "," + offerPlanVo.getFkStudentName() + "," + offerPlanVo.getFkAgentName() + "," + offerPlanVo.getFkInstitutionName();
//                    reminderCenterClient.sendEmail(title, "OFFER_ITEM_NOTICE", staffId, taskRemark);
//                }
//            }
//        }
//    }

    /**
     * Author Cream
     * Description : //申请计划信息变更通知佣金部门
     * Date 2023/3/30 12:49
     * Params:
     * Return
     */
    @Async("saleTaskExecutor")
    @Override
    public void sendOfferItemCommissionNotice(EventOfferPlanDto offerPlanVo, StudentOfferItemVo offerItemDto, Map<String, String> headerMap, Long staffId, String locale, boolean isDefer) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        List<PayablePlan> planList = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery()
                .eq(PayablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_OFFER_ITEM.key).eq(PayablePlan::getFkTypeTargetId, offerItemDto.getId()));
        List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectList(Wrappers.<ReceivablePlan>lambdaQuery()
                .eq(ReceivablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_OFFER_ITEM.key).eq(ReceivablePlan::getFkTypeTargetId, offerItemDto.getId()));
        if ((!planList.isEmpty() || !receivablePlans.isEmpty()) && !offerPlanVo.getIsFollowHidden()) {
            Long nChannelId = offerPlanVo.getFkInstitutionChannelId();
            Long oChannelId = offerItemDto.getFkInstitutionChannelId();
            Long nProviderId = offerPlanVo.getFkInstitutionProviderId();
            Long oProviderId = offerItemDto.getFkInstitutionProviderId();
            Long nInstitutionId = offerPlanVo.getFkInstitutionId();
            Long oInstitutionId = offerItemDto.getFkInstitutionId();
            Long nCourseId = offerPlanVo.getFkInstitutionCourseId();
            Long oCourseId = offerItemDto.getFkInstitutionCourseId();
            BigDecimal nDuration = offerPlanVo.getDuration();
            BigDecimal oDuration = offerItemDto.getDuration();
            Integer nDurationType = offerPlanVo.getDurationType();
            Integer oDurationType = offerItemDto.getDurationType();
            Date nOpeningTime = offerPlanVo.getDeferOpeningTime();
            Date oOpeningTime = offerItemDto.getDeferOpeningTime();
            Date nClosingTime = offerPlanVo.getClosingTime();
            Date oClosingTime = offerItemDto.getClosingTime();
            boolean courseFlag=false;
            String nCustomName = offerPlanVo.getOldCourseCustomName();
            String oldCourseCustomName = offerItemDto.getOldCourseCustomName();
            if (GeneralTool.isNotEmpty(nCourseId)) {
                if (nCourseId==-1 && oCourseId==-1 && StringUtils.isNotBlank(nCustomName)){
                    courseFlag = !nCustomName.equals(oldCourseCustomName);
                }else {
                    courseFlag = !nCourseId.equals(oCourseId);
                }
            }
            String duration="";
            String oDurationString = "";
            boolean durationFlag = false;
            Integer durationType = offerItemDto.getDurationType();
            if (GeneralTool.isNotEmpty(nDurationType) && !nDurationType.equals(oDurationType)) {
                durationType = nDurationType;
                durationFlag = true;
            }
            BigDecimal itemDtoDuration = offerItemDto.getDuration();
            if (GeneralTool.isNotEmpty(nDuration) && !nDuration.equals(oDuration)) {
                itemDtoDuration = nDuration;
                durationFlag = true;
            }
            if ((GeneralTool.isNotEmpty(nProviderId) && !nProviderId.equals(oProviderId))||
                    (GeneralTool.isNotEmpty(nInstitutionId) && !nInstitutionId.equals(oInstitutionId))||
                    courseFlag||
                    (GeneralTool.isNotEmpty(nChannelId) && !nChannelId.equals(oChannelId))||
                    (GeneralTool.isNotEmpty(nDuration) && !nDuration.equals(oDuration))||
                    (GeneralTool.isNotEmpty(nDurationType) && !nDurationType.equals(oDurationType))||
                    (GeneralTool.isNotEmpty(nOpeningTime) && !nOpeningTime.equals(oOpeningTime))|| durationFlag ||
                    (GeneralTool.isNotEmpty(nClosingTime) && !nClosingTime.equals(oClosingTime))
                    ) {
                Long fkStudentOfferItemStepId = offerItemDto.getFkStudentOfferItemStepId();
                StudentOfferItemStep studentOfferItemStep = studentOfferItemStepMapper.selectById(fkStudentOfferItemStepId);

                Map<Long, String> companyConfigValue1Map = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_EDIT_OFFER_ITEM_AFTER_ARAP.key, 1).getData();
                Map<Long, String> companyConfigValue2Map = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_EDIT_OFFER_ITEM_AFTER_ARAP.key, 2).getData();
                Student student = studentMapper.selectById(offerItemDto.getFkStudentId());
                Long fkCompanyId = student.getFkCompanyId();
                String value1 = companyConfigValue1Map.get(fkCompanyId);
                String value2 = companyConfigValue2Map.get(fkCompanyId);
                List<String> loginIdList = new ArrayList<>(JSON.parseArray(value2, String.class));
                StaffVo staff = null;
                if (value1.equals("1") || value1.equals("2")) {
                    Long fkAreaCountryId = offerItemDto.getFkAreaCountryId();
                    String uk = "commission.uk";
                    String aud = "commission.au";
                    String other = "commission";
                    //GEA财务
                    String specialist = "specialist";
                    if (loginIdList.contains(specialist)) {
                        if((GeneralTool.isNotEmpty(nProviderId) && !nProviderId.equals(oProviderId))||
                                courseFlag||durationFlag||
                                (GeneralTool.isNotEmpty(nInstitutionId) && !nInstitutionId.equals(oInstitutionId))||
                                (GeneralTool.isNotEmpty(nChannelId) && !nChannelId.equals(oChannelId))||
                                (GeneralTool.isNotEmpty(nOpeningTime) && !nOpeningTime.equals(oOpeningTime))){
                            //获取财务
                            staff = permissionCenterClient.getStaffByLoginId(specialist);
                        }
                    } else {
                        if (fkAreaCountryId == 6 || fkAreaCountryId == 8) {
                            if (loginIdList.contains(aud)) {
                                staff = permissionCenterClient.getStaffByLoginId(aud);
                            }
                        } else if (fkAreaCountryId == 7) {
                            if (loginIdList.contains(uk)) {
                                staff = permissionCenterClient.getStaffByLoginId(uk);
                            }
                        } else {
                            staff = permissionCenterClient.getStaffByLoginId(other);
                        }
                    }
                }

//                Result<ConfigVo> configByKey = permissionCenterClient.getConfigByKey(ProjectKeyEnum.REMINDER_EMAIL_EDIT_OFFER_ITEM_AFTER_ARAP.key);
//                if (configByKey.isSuccess() && GeneralTool.isNotEmpty(configByKey.getData())) {
//                    ConfigVo data = configByKey.getData();
//                    String value1 = data.getValue1();
//                    String value2 = data.getValue2();
//                    if (StringUtils.isNotBlank(value1) && StringUtils.isNotBlank(value2)) {
//                        SwitchConfigEntity switchConfigEntity = JSON.parseObject(JSON.toJSONString(JSON.parse(value1)), SwitchConfigEntity.class);
//                        EmailConfigEntity emailConfigEntity = JSON.parseObject(JSON.toJSONString(JSON.parse(value2)), EmailConfigEntity.class);
//                        StaffVo staff = null;
//                        Student student = studentMapper.selectById(offerItemDto.getFkStudentId());
//                        Long fkCompanyId = student.getFkCompanyId();
//                        Long fkAreaCountryId = offerItemDto.getFkAreaCountryId();
//                        String uk = "commission.uk";
//                        String aud = "commission.au";
//                        String other = "commission";
//                        String specialist = "specialist";
//                        if (3 == fkCompanyId) {
//                            if (switchConfigEntity.isIae()) {
//                                List<String> iae = emailConfigEntity.getIae();
//                                if (iae.isEmpty()) {
//                                    return;
//                                }
//                                if (fkAreaCountryId == 6 || fkAreaCountryId == 8) {
//                                    if (iae.contains(aud)) {
//                                        staff = permissionCenterClient.getStaffByLoginId(aud);
//                                    }
//                                } else if (fkAreaCountryId == 7) {
//                                    if (iae.contains(uk)) {
//                                        staff = permissionCenterClient.getStaffByLoginId(uk);
//                                    }
//                                } else {
//                                    if (iae.contains(other)) {
//                                        staff = permissionCenterClient.getStaffByLoginId(other);
//                                    }
//                                }
//                            }
//                        } else if (fkCompanyId == 2) {
//                            if (switchConfigEntity.isOther()) {
//                                List<String> gea = emailConfigEntity.getGea();
//                                if (gea.isEmpty()) {
//                                    return;
//                                }
//                                if((GeneralTool.isNotEmpty(nProviderId) && !nProviderId.equals(oProviderId))||
//                                        courseFlag||durationFlag||
//                                        (GeneralTool.isNotEmpty(nInstitutionId) && !nInstitutionId.equals(oInstitutionId))||
//                                        (GeneralTool.isNotEmpty(nChannelId) && !nChannelId.equals(oChannelId))||
//                                        (GeneralTool.isNotEmpty(nOpeningTime) && !nOpeningTime.equals(oOpeningTime))){
//                                    if (gea.contains(specialist)) {
//                                        //获取财务
//                                        staff = permissionCenterClient.getStaffByLoginId(specialist);
//                                    }
//                                }
//                            }
//                        }

                        Agent agent = agentService.getAgentById(offerItemDto.getFkAgentId());
                        Set<Long> staffIds = new HashSet<>();
                        staffIds.add(staffId);
                        staffIds.add(offerItemDto.getFkStaffId());
                        List<StaffVo> staffByIds = permissionCenterClient.getStaffByIds(staffIds);
                        Map<Long, String> collect = staffByIds.stream().collect(Collectors.toMap(StaffVo::getId, StaffVo::getFullName));
                        List<StudentProjectRoleStaffVo> studentProjectRoleStaffDtoList = studentProjectRoleStaffMapper.selectProjectStaffById(TableEnum.SALE_STUDENT_OFFER.key, offerItemDto.getFkStudentOfferId());
                        StrBuilder projectName = new StrBuilder();
                        for (StudentProjectRoleStaffVo staffDto : studentProjectRoleStaffDtoList) {
                            projectName.append("【").append(staffDto.getRoleName()).append("】 ").append(staffDto.getStaffName()).append("，");
                        }
                        projectName.del(projectName.length()-1,projectName.length());
                        Set<Long> providerIds = new HashSet<>();
                        providerIds.add(nProviderId);
                        providerIds.add(oProviderId);
                        Map<Long, String> providerMap = institutionCenterClient.getInstitutionProviderNamesByIds(providerIds).getData();
                        Set<Long> channelIds = new HashSet<>();
                        channelIds.add(nChannelId);
                        channelIds.add(oChannelId);
                        Map<Long, String> channelMap = institutionCenterClient.getInstitutionProviderChannelByIds(channelIds).getData();
                        Set<Long> institutionIds = new HashSet<>();
                        institutionIds.add(nInstitutionId);
                        institutionIds.add(oInstitutionId);
                        Map<Long, String> institutionMap = institutionCenterClient.getInstitutionNamesByIds(institutionIds).getData();
                        Set<Long> courseIds = new HashSet<>();
                        courseIds.add(nCourseId);
                        courseIds.add(oCourseId);
                        Map<Long, String> courseMap = institutionCenterClient.getCourseNameByIds(courseIds).getData();
                        List<Map<String, String>>  list = new ArrayList<>();
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        String studentName = student.getName() + "（" + student.getLastName() + "  " + student.getFirstName() + "）";
                        String countryName = institutionCenterClient.getCountryNameById(offerItemDto.getFkAreaCountryId()).getData();
                        if (null != staff) {

                            if (GeneralTool.isNotEmpty(durationType) && GeneralTool.isNotEmpty(itemDtoDuration)) {
                                duration = "（" + itemDtoDuration + ProjectExtraEnum.getValueByKey(durationType, ProjectExtraEnum.DURATION_TYPE, locale) + "）";
                            }
                            if (GeneralTool.isNotEmpty(oDurationType) && GeneralTool.isNotEmpty(oDuration)) {
                                oDurationString = "（" + oDuration + ProjectExtraEnum.getValueByKey(oDurationType, ProjectExtraEnum.DURATION_TYPE, locale) + "）";
                            }
                            //TODO OFFER_ITEM_COMMISSION_NOTICE
                            Map<String, String> map = new HashMap<>();
                            map.put("email", staff.getEmail());
                            map.put("roleName", staff.getFullName());
                            map.put("studentName", studentName);
                            map.put("agentName", agent.getName());
                            map.put("bdName", collect.get(offerItemDto.getFkStaffId()));
                            map.put("projectRole", projectName.toString());
                            map.put("countryName", countryName);
                            map.put("stepName", studentOfferItemStep.getStepName());
                            String newChannelName = channelMap.get(nChannelId);
                            map.put("providerName", "【" + newChannelName + "】" + "（" + providerMap.get(nProviderId) + "）");
                            if (GeneralTool.isNotEmpty(nClosingTime)) {
                                map.put("closingTime",sdf.format(nClosingTime));
                                if (!nClosingTime.equals(oClosingTime) && GeneralTool.isNotEmpty(oClosingTime)) {
                                    map.put("oldClosingTime", "（原" + sdf.format(oClosingTime) + "）");
                                } else {
                                    map.put("oldClosingTime", "");
                                }
                            }else {
                                map.put("closingTime", "");
                                if (GeneralTool.isNotEmpty(oClosingTime)) {
                                    if (!oClosingTime.equals(nClosingTime)) {
                                        map.put("oldClosingTime", "（原" + sdf.format(oClosingTime) + "）");
                                    } else {
                                        map.put("oldClosingTime", "");
                                    }
                                }else {
                                    map.put("oldClosingTime", "");
                                }
                            }
                            String oldChannelName = channelMap.get(oChannelId);
                            if (!nProviderId.equals(oProviderId) || (GeneralTool.isNotEmpty(nChannelId) && !nChannelId.equals(oChannelId))) {
                                map.put("oldProviderName", "（原" + "【" + oldChannelName + "】" + "（" + providerMap.get(oProviderId) + "）" + "）");
                            } else {
                                map.put("oldProviderName", "");
                            }

                            map.put("institutionName", institutionMap.get(nInstitutionId));
                            if (!nInstitutionId.equals(oInstitutionId)) {
                                map.put("oldInstitutionName", "（原" + institutionMap.get(oInstitutionId)+ "）");
                            } else {
                                map.put("oldInstitutionName", "");
                            }
                            if (nCourseId == -1) {
                                map.put("courseName", nCustomName + duration);
                            } else {
                                map.put("courseName", courseMap.get(nCourseId) + duration);
                            }
                            List<StudentOfferItem> studentOfferItems1 = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery().eq(StudentOfferItem::getFkParentStudentOfferItemId, offerItemDto.getId()));
                            if (GeneralTool.isNotEmpty(studentOfferItems1)){
                                for (StudentOfferItem studentOfferItem : studentOfferItems1) {
                                    if (!studentOfferItem.getIsFollowHidden() && studentOfferItem.getIsFollow()){
                                        String name=studentOfferItem.getOldCourseCustomName();
                                        if (GeneralTool.isNotEmpty(studentOfferItem.getFkInstitutionCourseId()) && studentOfferItem.getFkInstitutionCourseId()!=-1){
                                            Result<InstitutionCourseVo> courseById = institutionCenterClient.getCourseById(studentOfferItem.getFkInstitutionCourseId());
                                            if (GeneralTool.isNotEmpty(courseById.getData())){
                                                name= courseById.getData().getName();
                                            }
                                        }
                                        if (GeneralTool.isNotEmpty(offerItemDto.getChildRenStudentOfferItemDtos())){
                                            for (ChildRenStudentOfferItemVo childRenStudentOfferItemDto : offerItemDto.getChildRenStudentOfferItemDtos()) {
                                                if (name.equals(childRenStudentOfferItemDto.getFkCourseName())) {
                                                    if (!childRenStudentOfferItemDto.getIsFollowHidden()){
                                                        map.put("nFollowUpCourseName", (GeneralTool.isEmpty(map.get("nFollowUpCourseName")) ? "" : " + ") + name);
                                                    }
                                                }else {
                                                    map.put("oFollowUpCourseName", (GeneralTool.isEmpty(map.get("oFollowUpCourseName")) ? "" : " + ") + name);
                                                }
                                            }
                                        }else{
                                            map.put("followUpCourseName",(GeneralTool.isEmpty(map.get("followUpCourseName")) ? "" : " + ")+name);
                                        }
                                    }
                                }
                            }
                            if(GeneralTool.isNotEmpty(map.get("followUpCourseName"))){
                                if (GeneralTool.isNotEmpty(map.get("courseName"))){
                                    map.put("followUpCourseName"," + "+map.get("followUpCourseName"));
                                }
                            }else{
                                map.put("followUpCourseName","");
                            }

                            if (GeneralTool.isNotEmpty(map.get("nFollowUpCourseName"))){
                                if (GeneralTool.isNotEmpty(map.get("followUpCourseName"))){
                                    map.put("followUpCourseName",map.get("followUpCourseName")+" + ");
                                }
                            }else{
                                map.put("nFollowUpCourseName","");
                            }

                            if (!nCourseId.equals(oCourseId) || (StringUtils.isNotBlank(nCustomName)&&!nCustomName.equals(oldCourseCustomName)) || durationFlag) {
                                if (oCourseId == -1) {
                                    map.put("oldCourseName", "（原" + oldCourseCustomName + oDurationString + "）");
                                } else {
                                    map.put("oldCourseName", "（原" + courseMap.get(oCourseId) + oDurationString + "）");
                                }
                            } else {
                                map.put("oldCourseName", "");
                            }
                            if (GeneralTool.isNotEmpty(map.get("oFollowUpCourseName"))){
                                if (GeneralTool.isNotEmpty(map.get("oldCourseName"))){
                                    map.put("oFollowUpCourseName"," + "+map.get("oFollowUpCourseName"));
                                }
                            }else{
                                map.put("oFollowUpCourseName","");
                            }
                            if (isDefer && offerPlanVo.getIsDeferEntrance()) {
                                map.put("openTime", sdf.format(nOpeningTime) + "（延迟入学）");
                            }else {
                                map.put("openTime", sdf.format(nOpeningTime));
                            }
                            if (!nOpeningTime.equals(oOpeningTime)) {
                                String isDeferStr = "";
                                if (offerItemDto.getIsDeferEntrance()) {
                                    isDeferStr = "（延迟入学）";
                                }
                                map.put("oldOpenTime", "（原" + sdf.format(oOpeningTime) + isDeferStr + "）");
                            } else {
                                map.put("oldOpenTime", "");
                            }
                            String newInstitution = institutionMap.get(nInstitutionId);
                            String remarkStr = "【系统提醒】【申请计划发生变化】学生：" + studentName + "，" + "【" + countryName + "】" + newInstitution + "，" + courseMap.get(nCourseId);
                            remarkStr += duration;
                            map.put("title", remarkStr);
                            map.put("remarkStr", collect.get(staffId) + "，于" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "，编辑修改");
                            list.add(map);
                     if (GeneralTool.isNotEmpty(list)) {
                            //reminderCenterClient.batchSendEmail(list, ProjectKeyEnum.OFFER_ITEM_COMMISSION_NOTICE.key);
                         List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();
                         EmailSenderQueue  emailSenderQueue = new EmailSenderQueue();
                         emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.OFFER_ITEM_COMMISSION_NOTICE.getEmailTemplateKey());
                         emailSenderQueue.setEmailParameter(JSON.toJSONString(map));
                         emailSenderQueue.setFkTableName(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
                         emailSenderQueue.setFkTableId(offerItemDto.getId());
                         emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
                         emailSenderQueue.setOperationTime(now());
                         emailSenderQueueList.add(emailSenderQueue);
                         Result<Boolean> bolleanResult =reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
                         if (!bolleanResult.isSuccess()) {
                             throw new GetServiceException(LocaleMessageUtils.getMessage(bolleanResult.getMessage()));
                         }
                        }
                        }
//                        if (GeneralTool.isNotEmpty(list)) {
//                            reminderCenterClient.batchSendEmail(list, ProjectKeyEnum.OFFER_ITEM_COMMISSION_NOTICE.key);
//                        }
//                    }
//                }
            }
        }
    }

    @Override
    public void sendEventBillEmail(Map<String, String> headerMap, EventBillReminderDto eventBillReminderVo, String title,StaffInfo staffInfo) {
        Set<Long> staffIds = eventBillReminderVo.getFkStaffIds();
        if (GeneralTool.isEmpty(staffIds)){
            return;
        }
        RequestHeaderHandler.setHeaderMap(headerMap);
        Boolean sendFlag = Boolean.FALSE;
        Properties props = System.getProperties();
        String profile = props.getProperty("spring.profiles.active");
        if (GeneralTool.isNotEmpty(profile)) {
            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.IAE_CODE)|| profile.equals(AppConstant.TW_CODE)) {
                sendFlag = Boolean.TRUE;
            }
        }


        String domain = "";
//        if (GeneralTool.isNotEmpty(profile)) {
//            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.IAE_CODE)|| profile.equals(AppConstant.TW_CODE)) {
//                ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
//                if (GeneralTool.isNotEmpty(configDto)){
//                    String configJson = configDto.getValue2();
//                    JSONObject configJsonObject = JSON.parseObject(configJson);
//                    if (SecureUtil.getFkCompanyId().equals(3L)){
//                        domain = configJsonObject.getString("IAE");
//                    }else {
//                        domain = configJsonObject.getString("OTHER");
//                    }
//                }
//            }else {
//                Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
//                if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
//                    domain = domainNameResult.getData();
//                }
//            }
//        }
//        ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
//        String domain = configDto.getValue1();
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.FILE_SRC_PREFIX.key, 2).getData();
        String configValue2 = companyConfigMap.get(SecureUtil.getFkCompanyId());
        if (GeneralTool.isNotEmpty(configValue2)){
            domain = configValue2;
        } else {
            Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
            if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
                domain = domainNameResult.getData();
            }
        }

//        String link = domain+"/sales-center_event_activity-cost_collection-plan-details/"+eventBillReminderVo.getFkEventBillId();


//        eventBillReminderVo.setTaskLink(link);
//        Map<String, String> map = new HashMap<>(12);
        Map<String, String> map = getTemplateFieldMap(eventBillReminderVo);

        String institutionProviderName = null;
        Result<String> institutionProviderNameResult = institutionCenterClient.getInstitutionProviderName(eventBillReminderVo.getFkInstitutionProviderId());
        if (institutionProviderNameResult.isSuccess()&& GeneralTool.isNotEmpty(institutionProviderNameResult.getData())){
            institutionProviderName = institutionProviderNameResult.getData();
        }
        if (GeneralTool.isNotEmpty(institutionProviderName)){
            map.put("fkInstitutionProviderName",institutionProviderName);
        }else {
            map.put("fkInstitutionProviderName","");
        }

        String fkAreaCountryName = null;
        if (GeneralTool.isNotEmpty(eventBillReminderVo.getFkAreaCountryIdList())){
            Map<Long, String> countryNameMap = institutionCenterClient.getCountryNamesByIds(new HashSet<>(eventBillReminderVo.getFkAreaCountryIdList())).getData();
            StringJoiner stringJoiner = new StringJoiner(",");
            for (Long countryId : eventBillReminderVo.getFkAreaCountryIdList()) {
                if (GeneralTool.isNotEmpty(countryNameMap)&&GeneralTool.isNotEmpty(countryNameMap.get(countryId))){
                    stringJoiner.add(countryNameMap.get(countryId));
                }
            }
            fkAreaCountryName = stringJoiner.toString();
        }
        if (GeneralTool.isNotEmpty(fkAreaCountryName)){
            map.put("fkAreaCountryName",fkAreaCountryName);
        }else {
            map.put("fkAreaCountryName","");
        }

        Set<String> nums = new HashSet<>(3);
        nums.add(eventBillReminderVo.getFkCurrencyTypeNumEvent());
        nums.add(eventBillReminderVo.getFkCurrencyTypeNumInvoice());
        nums.add(eventBillReminderVo.getActualReceivableAmountCurrencyNum());
        String actualReceivableAmountCurrency = "";
        Map<String, String> currencyNameMap = financeCenterClient.getCurrencyNamesByNums(nums).getData();

        if (GeneralTool.isNotEmpty(currencyNameMap)){
            if (GeneralTool.isNotEmpty(currencyNameMap.get(eventBillReminderVo.getFkCurrencyTypeNumEvent()))){
                map.put("fkCurrencyTypeNumEventName",currencyNameMap.get(eventBillReminderVo.getFkCurrencyTypeNumEvent()));
            }else {
                map.put("fkCurrencyTypeNumEventName","");
            }
            if (GeneralTool.isNotEmpty(currencyNameMap.get(eventBillReminderVo.getFkCurrencyTypeNumInvoice()))){
                map.put("fkCurrencyTypeNumInvoiceName",currencyNameMap.get(eventBillReminderVo.getFkCurrencyTypeNumInvoice()));
            }else {
                map.put("fkCurrencyTypeNumInvoiceName","");
            }
            //获取所有公司
            Map<Long, String> companyMap = getCompanyMap();
            if(GeneralTool.isNotEmpty(eventBillReminderVo.getFkCompanyId())){
                String companyName = companyMap.get(eventBillReminderVo.getFkCompanyId());
                map.put("companyName",companyName);
                map.put("companyId",eventBillReminderVo.getFkCompanyId().toString());
            }

            if (GeneralTool.isNotEmpty(currencyNameMap.get(eventBillReminderVo.getActualReceivableAmountCurrencyNum()))){
                actualReceivableAmountCurrency = currencyNameMap.get(eventBillReminderVo.getActualReceivableAmountCurrencyNum());
            }
        }

        if (GeneralTool.isNotEmpty(eventBillReminderVo.getTotalActualReceivableAmount())){
            //TODO OFFER_ITEM_COMMISSION_NOTICE
            map.put("totalActualReceivableAmountTitle","总到账金额：");
            map.put("totalActualReceivableAmount",eventBillReminderVo.getTotalActualReceivableAmount().toString()+actualReceivableAmountCurrency);
        }else {
            map.put("totalActualReceivableAmountTitle","");
            map.put("totalActualReceivableAmount","");
        }
        if (GeneralTool.isNotEmpty(eventBillReminderVo.getActualReceivableAmount())){
            map.put("actualReceivableAmountTitle","到账金额：");
            map.put("actualReceivableAmount",eventBillReminderVo.getActualReceivableAmount().toString()+actualReceivableAmountCurrency);
        }else {
            map.put("actualReceivableAmountTitle","");
            map.put("actualReceivableAmount","");
        }


        String fkEventSummaryName = null;
        if (GeneralTool.isNotEmpty(eventBillReminderVo.getFkEventSummaryIdList())){
            LambdaQueryWrapper<EventSummary> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.in(EventSummary::getId,eventBillReminderVo.getFkEventSummaryIdList());
            List<EventSummary> eventSummaries = eventSummaryService.getEventSummariesByCondition(lambdaQueryWrapper);

            if (GeneralTool.isNotEmpty(eventSummaries)){
                StringJoiner stringJoiner = new StringJoiner(",");
                for (EventSummary eventSummary : eventSummaries) {
                    stringJoiner.add(eventSummary.getEventSummary());
                }
                fkEventSummaryName = stringJoiner.toString();

            }

        }

        if (GeneralTool.isNotEmpty(fkEventSummaryName)){
            map.put("fkEventSummaryName",fkEventSummaryName);
        }else {
            map.put("fkEventSummaryName","");
        }
        if(GeneralTool.isNotEmpty(staffIds)){
            map.put("staffIdList",staffIds.toString());
        }else {
            map.put("staffIdList","");
        }

        map.put("startingTime",new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        map.put("sponsor",staffInfo.getFullName());


//        String taskRemark = JSON.toJSONString(map);
//
//        taskRemark = "&lt;div style=\"display:none;\"&gt;"+taskRemark+"&lt;/div&gt;";

//        String taskRemark = MyStringUtils.getReminderTemplate(map, SaleCenterConstant.EVENT_BILL_REMINDER_CONTENT);

        String summary = "";
        if (GeneralTool.isNotEmpty(institutionProviderName)){
            summary = summary + institutionProviderName + ",";
        }
        summary = summary + eventBillReminderVo.getInvoiceSummary();
        List<EmailSenderQueue> list = new ArrayList<>();
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(eventBillReminderVo.getFkCompanyId());
        String text = null;
        if (!versionValue2.equals("en")) {
            text = title+summary+"，活动汇总费用计划";
        }else {
            if(title.equals("【已发起】")){
                    title = "Initiated";
            } else if (title.equals("【已更新】")) {
                    title = "Updated";
            } else if (title.equals("【已作废】")) {
                    title = "Cancelled";
            } else if (title.equals("【财务单据已作废】")) {
                    title = "Financial Document Cancelled";
            } else if (title.equals("【活动费用修改】")) {
                    title = "Event Fee Modification";
            }
            text = title+summary+"，Activity Summary Cost Plan";
        }
        String taskRemark = JSON.toJSONString(map);
        if (GeneralTool.isNotEmpty(staffIds)){
            EmailSenderQueue  emailSenderQueue = new EmailSenderQueue();
            emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.EVENT_FEE_PLAN_CHANGE_REMINDER.getEmailTemplateKey());
            emailSenderQueue.setEmailTitle(text);
            emailSenderQueue.setEmailParameter(taskRemark);
            emailSenderQueue.setFkTableName(TableEnum.SALE_EVENT_BILL.key);
            emailSenderQueue.setFkTableId(eventBillReminderVo.getFkEventBillId());
            emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
            emailSenderQueue.setOperationTime(new Date());
            list.add(emailSenderQueue);
//            Map<Long, String> staffNamesByIds = permissionCenterClient.getStaffNamesByIds(staffIds);
            List<RemindTaskDto> remindTaskVos = new ArrayList<>();
            for (Long fkStaffIdNotice : staffIds) {
//                String staffName = staffNamesByIds.get(fkStaffIdNotice);
//
//                String taskTitle = title+summary+"，活动汇总费用计划";
//                map.put("taskTitle",taskTitle);
//                map.put("staffName",staffName);
//                String taskRemark = JSON.toJSONString(map);
//                if (sendFlag){
//                    //生产情况发给用户
//                    Result<Boolean> result = reminderCenterClient.sendEmailToStaff(taskTitle, "EVENT_BILL_NOTICE", fkStaffIdNotice, taskRemark);
//                    if (!result.isSuccess() || (GeneralTool.isNotEmpty(result.getData()) && !result.getData()) || GeneralTool.isEmpty(result.getData())){
//                        throw new GetServiceException(LocaleMessageUtils.getMessage("remind_emil_send_fail_user")+staffName);
//                    }
//                }else {
//                    //非生产情况发给管理员用于测试
//                    Result<Boolean> result = reminderCenterClient.sendEmailToStaff(taskTitle, "EVENT_BILL_NOTICE", 1L, taskRemark);
//                    if (!result.isSuccess() || (GeneralTool.isNotEmpty(result.getData()) && !result.getData()) || GeneralTool.isEmpty(result.getData())){
//                        throw new GetServiceException(LocaleMessageUtils.getMessage("remind_emil_send_fail_user")+staffName);
//                    }
//                }
//
                //summary+"，活动汇总费用计划【已发起/已更新/已作废】
                //发送提醒通知

                //TODO EVENT_BILL_NOTICE
                RemindTaskDto remindTaskVo = new RemindTaskDto();
                remindTaskVo.setTaskTitle(text);
                remindTaskVo.setTaskRemark(taskRemark);
                //邮件方式发送
                remindTaskVo.setRemindMethod("1");
                //发送结束
                //remindTaskVo.setStatus(3);
//                remindTaskVo.setStatus(1);
                //默认背景颜色
                remindTaskVo.setFkRemindEventTypeKey(EmailTemplateEnum.EVENT_FEE_PLAN_CHANGE_REMINDER.getEmailTemplateKey());
                remindTaskVo.setTaskBgColor("#3788d8");
                remindTaskVo.setFkStaffId(fkStaffIdNotice);
                remindTaskVo.setStartTime(new Date());
                remindTaskVo.setAdvanceDays("0");
                remindTaskVo.setFkTableName(TableEnum.SALE_EVENT_BILL.key);
                remindTaskVo.setFkTableId(eventBillReminderVo.getFkEventBillId());
                remindTaskVo.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
                //暂时注释邮件链接
                //remindTaskVo.setTaskLink(link);
                remindTaskVos.add(remindTaskVo);
            }

            try {
//                log.info("==========================================={}",remindTaskVos.get(0).getTaskRemark());
                reminderCenterClient.batchUpdateTaskNew(remindTaskVos);
//                reminderCenterClient.batchAdd(remindTaskVos);
                log.info("==========================================={}",taskRemark);
                reminderCenterClient.batchAddEmailQueue(list);
            }catch (Exception e){
                log.error("添加发起通知失败：",e);
            }
        }

    }


    /**
     * 获取所有公司
     * @return
     */
    private Map<Long, String> getCompanyMap() {
        Result<List<CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<com.get.permissioncenter.vo.tree.CompanyTreeVo> companyTreeVos = result.getData();
        //初始为5的map
        Map<Long, String> companyMap = new HashMap<>(5);
        if (GeneralTool.isNotEmpty(companyTreeVos)) {
            companyMap = companyTreeVos.stream().collect(Collectors.toMap(com.get.permissioncenter.vo.tree.CompanyTreeVo::getId, com.get.permissioncenter.vo.tree.CompanyTreeVo::getShortName));
        }
        return companyMap;
    }

    @Async("saleTaskExecutor")
    @Override
    public void sendEventBillEmailAsync(Map<String, String> headerMap, EventBillReminderDto eventBillReminderVo, String title, StaffInfo staffInfo) {
        Set<Long> staffIds = eventBillReminderVo.getFkStaffIds();
        if (GeneralTool.isEmpty(staffIds)){
            return;
        }
        RequestHeaderHandler.setHeaderMap(headerMap);
        Boolean sendFlag = Boolean.FALSE;
        Properties props = System.getProperties();
        String profile = props.getProperty("spring.profiles.active");
        if (GeneralTool.isNotEmpty(profile)) {
            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.IAE_CODE)|| profile.equals(AppConstant.TW_CODE)) {
                sendFlag = Boolean.TRUE;
            }
        }

        String domain = "";
//        if (GeneralTool.isNotEmpty(profile)) {
//            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.IAE_CODE)|| profile.equals(AppConstant.TW_CODE)) {
//                ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
//                if (GeneralTool.isNotEmpty(configDto)){
//                    String configJson = configDto.getValue2();
//                    JSONObject configJsonObject = JSON.parseObject(configJson);
//                    if (staffInfo.getFkCompanyId().equals(3L)){
//                        domain = configJsonObject.getString("IAE");
//                    }else {
//                        domain = configJsonObject.getString("OTHER");
//                    }
//                }
//            }else {
//                Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
//                if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
//                    domain = domainNameResult.getData();
//                }
//            }
//        }
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.FILE_SRC_PREFIX.key, 2).getData();
        String configValue2 = companyConfigMap.get(staffInfo.getFkCompanyId());
        if (GeneralTool.isNotEmpty(configValue2)){
            domain = configValue2;
        } else {
            Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
            if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
                domain = domainNameResult.getData();
            }
        }

//        String link = domain+"/sales-center/event/activity-cost/collection-plan-details/"+eventBillReminderVo.getFkEventBillId();

//        eventBillReminderVo.setTaskLink(link);
        Map<String, String> map = getTemplateFieldMap(eventBillReminderVo);

        String institutionProviderName = null;
        Result<String> institutionProviderNameResult = institutionCenterClient.getInstitutionProviderName(eventBillReminderVo.getFkInstitutionProviderId());
        if (institutionProviderNameResult.isSuccess()&& GeneralTool.isNotEmpty(institutionProviderNameResult.getData())){
            institutionProviderName = institutionProviderNameResult.getData();
        }
        if (GeneralTool.isNotEmpty(institutionProviderName)){
            map.put("fkInstitutionProviderName",institutionProviderName);
        }else {
            map.put("fkInstitutionProviderName","");
        }

        String fkAreaCountryName = null;
        if (GeneralTool.isNotEmpty(eventBillReminderVo.getFkAreaCountryIdList())){
            Map<Long, String> countryNameMap = institutionCenterClient.getCountryNamesByIds(new HashSet<>(eventBillReminderVo.getFkAreaCountryIdList())).getData();
            StringJoiner stringJoiner = new StringJoiner(",");
            for (Long countryId : eventBillReminderVo.getFkAreaCountryIdList()) {
                if (GeneralTool.isNotEmpty(countryNameMap)&&GeneralTool.isNotEmpty(countryNameMap.get(countryId))){
                    stringJoiner.add(countryNameMap.get(countryId));
                }
            }
            fkAreaCountryName = stringJoiner.toString();
        }
        if (GeneralTool.isNotEmpty(fkAreaCountryName)){
            map.put("fkAreaCountryName",fkAreaCountryName);
        }else {
            map.put("fkAreaCountryName","");
        }

        Set<String> nums = new HashSet<>(3);
        nums.add(eventBillReminderVo.getFkCurrencyTypeNumEvent());
        nums.add(eventBillReminderVo.getFkCurrencyTypeNumInvoice());
        nums.add(eventBillReminderVo.getActualReceivableAmountCurrencyNum());
        String actualReceivableAmountCurrency = "";
        Map<String, String> currencyNameMap = financeCenterClient.getCurrencyNamesByNums(nums).getData();

        if (GeneralTool.isNotEmpty(currencyNameMap)){
            if (GeneralTool.isNotEmpty(currencyNameMap.get(eventBillReminderVo.getFkCurrencyTypeNumEvent()))){
                map.put("fkCurrencyTypeNumEventName",currencyNameMap.get(eventBillReminderVo.getFkCurrencyTypeNumEvent()));
            }else {
                map.put("fkCurrencyTypeNumEventName","");
            }
            if (GeneralTool.isNotEmpty(currencyNameMap.get(eventBillReminderVo.getFkCurrencyTypeNumInvoice()))){
                map.put("fkCurrencyTypeNumInvoiceName",currencyNameMap.get(eventBillReminderVo.getFkCurrencyTypeNumInvoice()));
            }else {
                map.put("fkCurrencyTypeNumInvoiceName","");
            }
            if (GeneralTool.isNotEmpty(currencyNameMap.get(eventBillReminderVo.getActualReceivableAmountCurrencyNum()))){
                actualReceivableAmountCurrency = currencyNameMap.get(eventBillReminderVo.getActualReceivableAmountCurrencyNum());
            }
        }

        if (GeneralTool.isNotEmpty(eventBillReminderVo.getTotalActualReceivableAmount())){
            //TODO 邮件模板
            map.put("totalActualReceivableAmountTitle","总到账金额：");
            map.put("totalActualReceivableAmount",eventBillReminderVo.getTotalActualReceivableAmount().toString()+actualReceivableAmountCurrency);
        }else {
            map.put("totalActualReceivableAmountTitle","");
            map.put("totalActualReceivableAmount","");
        }
        if (GeneralTool.isNotEmpty(eventBillReminderVo.getActualReceivableAmount())){
            map.put("actualReceivableAmountTitle","到账金额：");
            map.put("actualReceivableAmount",eventBillReminderVo.getActualReceivableAmount().toString()+actualReceivableAmountCurrency);
        }else {
            map.put("actualReceivableAmountTitle","");
            map.put("actualReceivableAmount","");
        }
        //获取所有公司
        Map<Long, String> companyMap = getCompanyMap();
        if(GeneralTool.isNotEmpty(eventBillReminderVo.getFkCompanyId())){
            String companyName = companyMap.get(eventBillReminderVo.getFkCompanyId());
            map.put("companyName",companyName);
            map.put("companyId",eventBillReminderVo.getFkCompanyId().toString());
        }

        String fkEventSummaryName = null;
        if (GeneralTool.isNotEmpty(eventBillReminderVo.getFkEventSummaryIdList())){
            LambdaQueryWrapper<EventSummary> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.in(EventSummary::getId,eventBillReminderVo.getFkEventSummaryIdList());
            List<EventSummary> eventSummaries = eventSummaryService.getEventSummariesByCondition(lambdaQueryWrapper);

            if (GeneralTool.isNotEmpty(eventSummaries)){
                StringJoiner stringJoiner = new StringJoiner(",");
                for (EventSummary eventSummary : eventSummaries) {
                    stringJoiner.add(eventSummary.getEventSummary());
                }
                fkEventSummaryName = stringJoiner.toString();

            }

        }

        if (GeneralTool.isNotEmpty(fkEventSummaryName)){
            map.put("fkEventSummaryName",fkEventSummaryName);
        }else {
            map.put("fkEventSummaryName","");
        }

        // 发起人
        if (GeneralTool.isNotEmpty(eventBillReminderVo.getGmtCreateUser())) {
            map.put("sponsor", eventBillReminderVo.getGmtCreateUser());
        } else {
            map.put("sponsor", "");
        }
        // 发起时间
        if (GeneralTool.isNotEmpty(eventBillReminderVo.getGmtCreate())) {
            map.put("startingTime", DateUtil.format(eventBillReminderVo.getGmtCreate(), "yyyy-MM-dd HH:mm:ss"));
        } else {
            map.put("startingTime", "");
        }
        if(GeneralTool.isNotEmpty(staffIds)){
            map.put("staffIdList",staffIds.toString());
        }else {
            map.put("staffIdList","");
        }
        String summary = eventBillReminderVo.getInvoiceSummary();
        List<EmailSenderQueue> list = new ArrayList<>();
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(eventBillReminderVo.getFkCompanyId());
        String text = null;
        if (!versionValue2.equals("en")) {
            text = title+summary+"，活动汇总费用计划";
        }else {
            if(title.equals("【已到账】")){
                title = "Already received";
            }
            text = title+summary+"，Activity Summary Cost Plan";
        }
        String taskRemark = JSON.toJSONString(map);
        if (GeneralTool.isNotEmpty(staffIds)){
            EmailSenderQueue  emailSenderQueue = new EmailSenderQueue();
            emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.EVENT_FEE_PLAN_CHANGE_REMINDER.getEmailTemplateKey());
            emailSenderQueue.setEmailTitle(text);
            emailSenderQueue.setEmailParameter(taskRemark);
            emailSenderQueue.setFkTableName(TableEnum.SALE_EVENT_BILL.key);
            emailSenderQueue.setFkTableId(eventBillReminderVo.getFkEventBillId());
            emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
            emailSenderQueue.setOperationTime(new Date());
            list.add(emailSenderQueue);
//            Map<Long, String> staffNamesByIds = permissionCenterClient.getStaffNamesByIds(staffIds);
//            List<RemindTaskDto> remindTaskVos = new ArrayList<>();
//            for (Long fkStaffIdNotice : staffIds) {
//                String staffName = staffNamesByIds.get(fkStaffIdNotice);
//
//                String taskTitle = title+summary+"，活动汇总费用计划";
//                map.put("taskTitle",taskTitle);
//                map.put("staffName",staffName);
//                String taskRemark = JSON.toJSONString(map);
//                if (sendFlag){
//                    //生产情况发给用户
//                    Result<Boolean> result = reminderCenterClient.sendEmailToStaff(taskTitle, "EVENT_BILL_NOTICE", fkStaffIdNotice, taskRemark);
//                    if (!result.isSuccess() || (GeneralTool.isNotEmpty(result.getData()) && !result.getData()) || GeneralTool.isEmpty(result.getData())){
//                        throw new GetServiceException(LocaleMessageUtils.getMessage("remind_emil_send_fail_user")+staffName);
//                    }
//                }else {
//                    //非生产情况发给管理员用于测试
//                    Result<Boolean> result = reminderCenterClient.sendEmailToStaff(taskTitle, "EVENT_BILL_NOTICE", 1L, taskRemark);
//                    if (!result.isSuccess() || (GeneralTool.isNotEmpty(result.getData()) && !result.getData()) || GeneralTool.isEmpty(result.getData())){
//                        throw new GetServiceException(LocaleMessageUtils.getMessage("remind_emil_send_fail_user")+staffName);
//                    }
//                }
//
//                //summary+"，活动汇总费用计划【已发起/已更新/已作废】
//                //发送提醒通知
//                //TODO EVENT_BILL_NOTICE
//                RemindTaskDto remindTaskVo = new RemindTaskDto();
//                remindTaskVo.setTaskTitle(title+summary+"，活动汇总费用计划");
//                remindTaskVo.setTaskRemark("");
//                //邮件方式发送
//                remindTaskVo.setRemindMethod("1");
//                //发送结束
//                remindTaskVo.setStatus(3);
//                //默认背景颜色
//                remindTaskVo.setFkRemindEventTypeKey("EVENT_BILL_NOTICE");
//                remindTaskVo.setTaskBgColor("#3788d8");
//                remindTaskVo.setFkStaffId(fkStaffIdNotice);
//                remindTaskVo.setStartTime(new Date());
//                remindTaskVo.setAdvanceDays("0");
//                remindTaskVo.setFkTableName(TableEnum.SALE_EVENT_BILL.key);
//                remindTaskVo.setFkTableId(eventBillReminderVo.getFkEventBillId());
//                remindTaskVo.setFkDbName(ProjectKeyEnum.REMINDER_CENTER.key);
//                //暂时注释邮件链接
//                //remindTaskVo.setTaskLink(link);
//                remindTaskVos.add(remindTaskVo);
            }

            try {
//                log.info("==========================================={}",remindTaskVos.get(0).getTaskRemark());
//                reminderCenterClient.batchAdd(remindTaskVos);
                log.info("==========================================={}",taskRemark);
                reminderCenterClient.batchAddEmailQueue(list);
            }catch (Exception e){
                log.error("添加发起通知失败：",e);
            }
    }

    private String getProjectRole(List<ProjectRoleStaffDto> projectRoleStaffVos){
        Set<Long> roleIds = projectRoleStaffVos.stream().map(ProjectRoleStaffDto::getFkRoleId).collect(Collectors.toSet());
        Set<Long> staffIds = projectRoleStaffVos.stream().map(ProjectRoleStaffDto::getFkStaffId).collect(Collectors.toSet());
        List<StudentProjectRole> projectRoles = studentProjectRoleMapper.selectBatchIds(roleIds);
        List<StaffVo> staffVos = permissionCenterClient.getStaffByIds(staffIds);
        Map<Long, StudentProjectRole> roleMap = projectRoles.stream().collect(Collectors.toMap(StudentProjectRole::getId, Function.identity()));
        Map<Long, StaffVo> staffDtoMap = staffVos.stream().collect(Collectors.toMap(StaffVo::getId, Function.identity()));
        StringBuilder stringBuilder = new StringBuilder();
        for (ProjectRoleStaffDto roleStaffVo : projectRoleStaffVos) {
            Long fkRoleId = roleStaffVo.getFkRoleId();
            Long fkStaffId = roleStaffVo.getFkStaffId();
            stringBuilder.append("【").append(roleMap.get(fkRoleId).getRoleName()).append("】 ")
                    .append(staffDtoMap.get(fkStaffId).getFullName()).append("，");
        }
        String str = stringBuilder.toString();
        if (str.endsWith("，")) {
            str = str.substring(0, str.lastIndexOf("，"));
        }
        return str;
    }

    @Async("saleTaskExecutor")
    @Override
    public void sendOfferEmail(StudentOfferDto offerVo, Map<String, String> headerMap, Long staffId) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        List<ProjectRoleStaffDto> projectRoleStaffVos = offerVo.getRoleStaffVo();
        if (projectRoleStaffVos.isEmpty()) {
            return;
        }
        //判断是否需要发送邮件
        Student student = studentMapper.selectById(offerVo.getFkStudentId());
        Long fkCompanyId = student.getFkCompanyId();
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_CREATE_STUDENT_OFFER.key, 1).getData();
        String configValue1 = companyConfigMap.get(fkCompanyId);
        if (GeneralTool.isEmpty(configValue1) || configValue1.equals("0")) {
            return;
        }

            String studentName = student.getName() + "（" +student.getLastName() + " "+ student.getFirstName() + "）";
            Long bdId = offerVo.getFkStaffId();
            Agent agent = agentService.getAgentById(offerVo.getFkAgentId());
            String agentName = agent.getName();
            //项目成员
            Set<Long> staffIds = projectRoleStaffVos.stream().map(ProjectRoleStaffDto::getFkStaffId).collect(Collectors.toSet());
            List<StaffVo> staff = permissionCenterClient.getStaffByIds(staffIds);
            //直属上司
//            Set<Long> supIds = staff.stream().map(StaffVo::getFkStaffIdSupervisor).collect(Collectors.toSet());
//            if (GeneralTool.isNotEmpty(supIds)) {
//                List<StaffVo> sups = permissionCenterClient.getStaffByIds(supIds);
//                staff.addAll(sups);
//            }
            String countryName = institutionCenterClient.getCountryNameById(offerVo.getFkAreaCountryId()).getData();
            //BD
            StaffVo bd = permissionCenterClient.getStaffById(bdId).getData();
            String bdName = bd.getName() + "（" + bd.getNameEn() + "）";
            List<Map<String,String>> list = new ArrayList<>();
//            staff.add(bd);
            //方案创建人
//            StaffVo createUser = permissionCenterClient.getStaffById(staffId).getData();
//            staff.add(createUser);
            Map<Long, List<StaffVo>> collect = staff.stream().collect(Collectors.groupingBy(StaffVo::getId));
        //TODO REMINDER_EMAIL_CREATE_STUDENT_OFFER
            String title = "【系统提醒】学生："+ studentName +"，"+countryName+"申请方案项目成员指派";
            String projectRole = getProjectRole(projectRoleStaffVos);
//            for (Map.Entry<Long, List<StaffVo>> entry : collect.entrySet()) {
//                StaffVo staffVo = entry.getValue().get(0);
//                String email = staffVo.getEmail();
//                if (StringUtils.isBlank(email)) {
//                    continue;
//                }
//                Map<String,String> map = new HashMap<>();
//                map.put("roleName", staffVo.getFullName());
//                map.put("studentName",studentName);
//                map.put("agentName",agentName);
//                map.put("projectRole",projectRole);
//                map.put("bdName",bdName);
//                map.put("date", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
//                map.put("countryName",countryName);
//                map.put("email", staffVo.getEmail());
//                map.put("title",title);
//                list.add(map);
//            }

        Map<String,String> map = new HashMap<>();
        map.put("studentName",studentName);
        map.put("agentName",agentName);
        map.put("projectRole",projectRole);
        map.put("bdName",bdName);
        map.put("date", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        map.put("countryName",countryName);
        map.put("title",title);
        List<Long> idList = staff.stream()
                .map(StaffVo::getId)
                .collect(Collectors.toList());
        map.put("staffIdList",idList.toString());
            List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();
            EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
            emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
            emailSenderQueue.setFkTableName(TableEnum.SALE_STUDENT_OFFER.key);
            emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.REMINDER_EMAIL_CREATE_STUDENT_OFFER.getEmailTemplateKey());
            emailSenderQueue.setOperationTime(now());
            emailSenderQueue.setFkTableId(offerVo.getId());
            emailSenderQueue.setEmailParameter(JSON.toJSONString(map));
            emailSenderQueueList.add(emailSenderQueue);
        Result<Boolean> bolleanResult =reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
        if (!bolleanResult.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(bolleanResult.getMessage()));
        }
            //reminderCenterClient.batchSendEmail(list,ProjectKeyEnum.REMINDER_EMAIL_CREATE_STUDENT_OFFER.key);

    }

    @Async("saleTaskExecutor")
    @Override
    public void sendOfferItemStepUpdateEmail(RStudentOfferItemStepDto studentOfferItemStepVo, List<StudentProjectRoleStaffVo> projectRoleStaffs, String studentName,
                                             String agentName, String institutionName, String courseName, StudentOfferItem offerItem,
                                             String loginId, Map<String, String> headerMap, Long staffId) {
        if (offerItem.getIsFollowHidden()) {
            return;
        }
        RequestHeaderHandler.setHeaderMap(headerMap);
        StudentOffer studentOffer = studentOfferMapper.selectOne(Wrappers.<StudentOffer>lambdaQuery().eq(StudentOffer::getId, offerItem.getFkStudentOfferId()));
        String countryName = institutionCenterClient.getCountryNameById(offerItem.getFkAreaCountryId()).getData();
        String providerName = institutionCenterClient.getInstitutionProviderName(offerItem.getFkInstitutionProviderId()).getData();
        Set<Long> staffIds = projectRoleStaffs.stream().map(StudentProjectRoleStaffVo::getFkStaffId).collect(Collectors.toSet());
        List<StaffVo> staff = permissionCenterClient.getStaffByIds(staffIds);
        //直属上司
        Set<Long> supIds = staff.stream().map(StaffVo::getFkStaffIdSupervisor).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(supIds)) {
            List<StaffVo> sups = permissionCenterClient.getStaffByIds(supIds);
            staff.addAll(sups);
        }
        //步骤为收到签证函（CAS/COE/I20）
        if (studentOfferItemStepVo.getFkStudentOfferItemStepId() == 6 && StringUtils.isNotBlank(loginId)) {
            StaffVo otherStaff = permissionCenterClient.getStaffByLoginId(loginId);
            if (otherStaff!=null) {
                staff.add(otherStaff);
            }
        }
        //添加方案创建人
        StaffVo offerStaff = permissionCenterClient.getStaffByLoginId(studentOffer.getGmtCreateUser());
        if (GeneralTool.isNotEmpty(offerStaff)){
            staff.add(offerStaff);
        }
        //添加步骤创建人
        StaffVo createUser = permissionCenterClient.getStaffById(staffId).getData();
//        staff.add(createUser);
        //步骤为申请退押金
        if (studentOfferItemStepVo.getFkStudentOfferItemStepId() == 15) {
            if (!studentOfferItemStepVo.getIsApplyRefundPage() && StringUtils.isNotBlank(loginId)){
                StaffVo otherStaff = permissionCenterClient.getStaffByLoginId(loginId);
                staff.clear();
                //只发送财务部/佣金部
                staff.add(otherStaff);
            }
        }
        //方案bd
        StaffVo bd = permissionCenterClient.getStaffById(studentOffer.getFkStaffId()).getData();
        String bdName = bd.getFullName();
        staff.add(bd);
        StudentOfferItemStep offerItemStep = studentOfferItemStepMapper.selectById(studentOfferItemStepVo.getFkStudentOfferItemStepId());
        String stepName = offerItemStep.getStepName();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date finalOpenTime = offerItem.getDeferOpeningTime();
        String openTime = "";
        if (GeneralTool.isNotEmpty(finalOpenTime)) {
            openTime = sdf.format(finalOpenTime);
            //TODO id=11 邮件模板
            if (GeneralTool.isNotEmpty(offerItem.getIsDeferEntrance()) && offerItem.getIsDeferEntrance()) {
                openTime += "（" + "延时入学" + "）";
            }
        }
        List<Map<String,String>> list = new ArrayList<>();
        Map<Long, List<StaffVo>> collect = staff.stream().collect(Collectors.groupingBy(StaffVo::getId));
        //TODO id=11 邮件模板
        String title = "【系统提醒】" + stepName + "学生：" + studentName + "，" + countryName + institutionName + "，" + courseName;
        List<ProjectRoleStaffDto> projectRoleStaffVos = new ArrayList<>();
        for (StudentProjectRoleStaffVo roleStaff : projectRoleStaffs) {
            ProjectRoleStaffDto roleStaffVo = new ProjectRoleStaffDto();
            roleStaffVo.setFkRoleId(roleStaff.getFkStudentProjectRoleId());
            roleStaffVo.setFkStaffId(roleStaff.getFkStaffId());
            projectRoleStaffVos.add(roleStaffVo);
        }
        List<StudentOfferItem> studentOfferItems1 = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery().eq(StudentOfferItem::getFkParentStudentOfferItemId, offerItem.getId()));
        if (GeneralTool.isNotEmpty(studentOfferItems1)){
            for (StudentOfferItem studentOfferItem : studentOfferItems1) {
                if (!studentOfferItem.getIsFollowHidden() && studentOfferItem.getIsFollow()){
                    Result<InstitutionCourseVo> courseById = institutionCenterClient.getCourseById(studentOfferItem.getFkInstitutionCourseId());
                    if (courseById.getData().getName()!=null){
                        courseName=courseName+" + "+courseById.getData().getName();
                    }
                }
            }
        }
        String projectRole = getProjectRole(projectRoleStaffVos);
        //封装数据
//        for (Map.Entry<Long, List<StaffVo>> entry : collect.entrySet()) {
//            StaffVo dto = entry.getValue().get(0);
//            String email = dto.getEmail();
//            if (StringUtils.isBlank(email)) {
//                continue;
//            }
//            Map<String,String> map = new HashMap<>();
//            map.put("roleName",dto.getFullName());
//            map.put("studentName",studentName);
//            map.put("agentName",agentName);
//            map.put("bdName",bdName);
//            map.put("providerName",providerName);
//            map.put("projectRole",projectRole);
//            map.put("countryName",countryName);
//            map.put("openTime",openTime);
//            map.put("email",dto.getEmail());
//            map.put("institutionName",institutionName);
//            map.put("courseName",courseName);
//            map.put("setter",createUser.getFullName());
//            map.put("date", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
//            map.put("stepName",stepName);
//            map.put("title",title);
//            list.add(map);
//        }
        List<Long> idList = staff.stream()
                .map(StaffVo::getId)
                .collect(Collectors.toList());
        Map<String,String> map = new HashMap<>();
        map.put("studentName",studentName);
        map.put("agentName",agentName);
        map.put("bdName",bdName);
        map.put("providerName",providerName);
        map.put("projectRole",projectRole);
        map.put("countryName",countryName);
        map.put("openTime",openTime);
        map.put("institutionName",institutionName);
        map.put("courseName",courseName);
        map.put("setter",createUser.getFullName());
        map.put("date", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        map.put("stepName",stepName);
        map.put("title",title);
        map.put("staffIdList",idList.toString());
        list.add(map);
        if (GeneralTool.isNotEmpty(list)) {
            //发送邮件提醒项目成员
            List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();
            EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
            emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
            emailSenderQueue.setFkTableName(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
            emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.REMINDER_EMAIL_STEP_ADMITTED.getEmailTemplateKey());
            emailSenderQueue.setOperationTime(now());
            emailSenderQueue.setFkTableId(offerItem.getId());
            emailSenderQueue.setEmailParameter(JSON.toJSONString(map));
            emailSenderQueueList.add(emailSenderQueue);
            Result<Boolean> bolleanResult =reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
            if (!bolleanResult.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(bolleanResult.getMessage()));
            }
            //reminderCenterClient.batchSendEmail(list,ProjectKeyEnum.REMINDER_EMAIL_STEP_ADMITTED.key);
        }
    }

    private <T>Map<String, String> getTemplateFieldMap(T object) {
        Map<String, String> map = new HashMap<>(14);
        SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");

        Field[] fields = ReflectUtil.getFieldsDirectly(object.getClass(), false);
        for (Field field : fields) {
            IgnoreRemind IgnoreRemind = field.getAnnotation(IgnoreRemind.class);
            if (null != IgnoreRemind) {
                continue;
            }
            if (GeneralTool.isNotEmpty(ReflectUtil.getFieldValue(object,field))){
                //如果是date类型
                if ("class java.util.Date".equals(field.getGenericType().toString())){
                    String format = fmt.format(ReflectUtil.getFieldValue(object, field));
                    map.put(field.getName(),format);
                }else {
                    map.put(field.getName(),ReflectUtil.getFieldValue(object,field).toString());
                }
            }else {
                map.put(field.getName(),"");
            }
        }
        return map;
    }


    /**
     * Author Cream
     * Description : //发送佣金结算通知邮件
     * Date 2023/2/28 11:25
     * Params:
     * Return
     */
    @Async
    @Override
    public void sendSettlementCommissionEmail(Long fkTypeTargetId, String commissionNotice, Map<String, String> headerMap,Long fkCompanyId) {
        RequestHeaderHandler.setHeaderMap(headerMap);
//        ConfigVo data = permissionCenterClient.getConfigByKey(TableEnum.SETTLEMENT_COMMISSION_NOTICE.key).getData();
//        String switchInfo = data.getValue1();
//        String email = data.getValue2();
//        if (StringUtils.isBlank(switchInfo) || StringUtils.isBlank(email)) {
//            log.info("缺少config配置信息");
//            return;
//        }
//        SwitchConfigEntity switchConfigEntity = JSON.parseObject(JSON.toJSONString(JSON.parse(switchInfo)), SwitchConfigEntity.class);
//        EmailConfigEntity emailConfigEntity = JSON.parseObject(JSON.toJSONString(JSON.parse(email)), EmailConfigEntity.class);
//        Long iae = 3L;
//        if (fkCompanyId.equals(iae)) {
//            if (switchConfigEntity.isIae()) {
//                List<String> entityIae = emailConfigEntity.getIae();
//                if (entityIae.isEmpty()) {
//                    return;
//                }
//                payablePlanService.sendSettlementCommissionEmail(entityIae, fkTypeTargetId, commissionNotice);
//            }
//        } else {
//            if (switchConfigEntity.isOther()) {
//                List<String> other = emailConfigEntity.getOther();
//                if (other.isEmpty()) {
//                    return;
//                }
//                payablePlanService.sendSettlementCommissionEmail(other, fkTypeTargetId, commissionNotice);
//            }
//        }

        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(TableEnum.SETTLEMENT_COMMISSION_NOTICE.key, 1).getData();
        String value1 = companyConfigMap.get(fkCompanyId);
        companyConfigMap = permissionCenterClient.getCompanyConfigMap(TableEnum.SETTLEMENT_COMMISSION_NOTICE.key, 2).getData();
        String value2 = companyConfigMap.get(fkCompanyId);
        if (value1.equals("1")) {
            List<String> emails = new ArrayList<>(JSON.parseArray(value2, String.class));
            payablePlanService.sendSettlementCommissionEmail(emails, fkTypeTargetId, commissionNotice);
        }


    }

    /**
     * 发送入学失败邮件
     * @param headerMap
     * @param user
     * @param itemId
     */
    @Async("saleTaskExecutor")
    @Override
    public void sendEnrolFailureEmail(Map<String, String> headerMap, UserInfo user, Long itemId,Long otherStaffId,String enrolFailure) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        Long staffId = user.getStaffId();
        /*
            通知项目成员及BD（收件人）
            邮件标题：成功客户列表设置入学失败通知，学生：刘德华，国家：英国
            邮件内容：
            成功客户列表设置入学失败通知
            学生：刘德华，国家：英国
            学校：XXXXXXXXXXXXXXXXX
            课程：XXXXXXXXXXXXXXXXX
            开学时间：XXXXXXXXXXXXXX
            设置入学失败，操作人：XXX，操作时间：2023/07/10 11:42:22
        */
        StudentOfferItem studentOfferItem = studentOfferItemMapper.selectById(itemId);
        if (studentOfferItem.getIsFollowHidden()) {
            return;
        }

        String countryName = institutionCenterClient.getCountryChnNameById(studentOfferItem.getFkAreaCountryId()).getData();
        String studentName = studentMapper.getStudentZhEnName(studentOfferItem.getFkStudentId());
        String institutionName = "";
        if (GeneralTool.isNotEmpty(studentOfferItem.getFkInstitutionId())){
            institutionName = institutionCenterClient.getInstitutionName(studentOfferItem.getFkInstitutionId()).getData();
        }else {
            institutionName = studentOfferItem.getOldInstitutionName();
        }
        String courseName = "";
        if (GeneralTool.isNotEmpty(studentOfferItem.getFkInstitutionCourseId())){
            courseName = institutionCenterClient.getCourseNameById(studentOfferItem.getFkInstitutionCourseId()).getData();
        }else {
            courseName = studentOfferItem.getOldCourseCustomName();
        }
        String openingTime = "";
        SimpleDateFormat openingTimeFormat = new SimpleDateFormat("yyyy/MM/dd");
        if (GeneralTool.isNotEmpty(studentOfferItem.getDeferOpeningTime())){
            openingTime = openingTimeFormat.format(studentOfferItem.getDeferOpeningTime());
        }else {
            openingTime = openingTimeFormat.format(studentOfferItem.getOpeningTime());
        }
        SimpleDateFormat sf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        String operatingTime = sf.format(new Date());
        String operator = permissionCenterClient.getStaffName(staffId).getData();

        Set<Long> staffIds = Sets.newHashSet();
        if (GeneralTool.isNotEmpty(studentOfferItem.getFkStaffId())){
            staffIds.add(studentOfferItem.getFkStaffId());
        }

        if (GeneralTool.isNotEmpty(otherStaffId) && otherStaffId != 0L){
            staffIds.add(otherStaffId);
        }

        List<StudentProjectRoleStaff> studentProjectRoleStaffs = studentProjectRoleStaffMapper.selectList(Wrappers.lambdaQuery(StudentProjectRoleStaff.class)
                .eq(StudentProjectRoleStaff::getFkTableId, studentOfferItem.getFkStudentOfferId())
                .eq(StudentProjectRoleStaff::getFkTableName, TableEnum.SALE_STUDENT_OFFER.key)
                .eq(StudentProjectRoleStaff::getIsActive, true)
        );
        if (GeneralTool.isNotEmpty(studentProjectRoleStaffs)){
            Set<Long> ids = studentProjectRoleStaffs.stream().map(StudentProjectRoleStaff::getFkStaffId).collect(Collectors.toSet());
            staffIds.addAll(ids);

        }

        List<RemindTaskDto> remindTaskVos = Lists.newArrayList();
        //TODO 邮件模板 EVENT_BILL_NOTICE
        String taskTitle = "成功客户列表设置入学失败通知，学生："+studentName+"，国家："+countryName;
        String taskRemark = "<div class=\"desc\">\n" +
                "    <div>学生："+studentName+"，国家："+countryName+"</div>\n" +
                "    <div>学校："+institutionName+"</div>\n" +
                "    <div>课程："+courseName+"</div>\n" +
                "    <div>开学时间："+openingTime+"</div>\n" +
                "    <div>入学失败原因："+enrolFailure+"</div>\n" +
                "    <div>设置入学失败，操作人："+operator+"，操作时间："+operatingTime+"</div>\n" +
                "</div>";

        Map<String, String> map = new HashMap<>();
        map.put("operator", operator);
        map.put("operatingTime", operatingTime);
        map.put("enrolFailure", enrolFailure);

        if (GeneralTool.isNotEmpty(staffIds)){
            for (Long id : staffIds) {
                RemindTaskDto remindTaskVo = new RemindTaskDto();
                remindTaskVo.setTaskTitle(taskTitle);
                remindTaskVo.setTaskRemark(taskRemark);
                //邮件方式发送
                remindTaskVo.setRemindMethod("1");
                //默认执行中
                remindTaskVo.setStatus(1);
                //默认背景颜色
                remindTaskVo.setFkRemindEventTypeKey(ProjectKeyEnum.ENROL_FAILURE_NOTICE.key);
                remindTaskVo.setTaskBgColor("#3788d8");
                remindTaskVo.setFkStaffId(id);
                remindTaskVo.setStartTime(new Date());
                remindTaskVo.setAdvanceDays("0");
                remindTaskVo.setFkTableName(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
                remindTaskVo.setFkTableId(itemId);
                remindTaskVo.setFkDbName(ProjectKeyEnum.REMINDER_CENTER.key);
                remindTaskVos.add(remindTaskVo);
            }

            try {
                log.info("==========================================={}",remindTaskVos.get(0).getTaskRemark());
                log.info("=========="+taskRemark);
                reminderCenterClient.batchAddTask(remindTaskVos);
                List<EmailSenderQueue> emailSenderQueueList = Lists.newArrayList();
                EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                emailSenderQueue.setFkDbName(ProjectKeyEnum.PERMISSION_CENTER.key);
                emailSenderQueue.setFkTableId(itemId);
                emailSenderQueue.setFkTableName(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
                emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.ENROL_FAILURE_NOTICE.getEmailTemplateKey());
                emailSenderQueue.setOperationTime(now());
                emailSenderQueue.setEmailTo(JSON.toJSONString(staffIds));
                emailSenderQueue.setEmailParameter(JSON.toJSONString(map));
                emailSenderQueueList.add(emailSenderQueue);

                Result<Boolean> booleanResult = reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
                if (!booleanResult.isSuccess()) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage(booleanResult.getMessage()));
                }

            }catch (Exception e){
                log.error("添加发起通知失败：",e);
            }
        }


    }


    /**
     * 发送入学失败邮件
     */
    @Async("saleTaskExecutor")
    @Override
    public void customSendStudentOfferNotice(Map<Long,MailDto> mailVoMap, StaffInfo staffInfo, List<Long> rejectEmailAgentIds) {
        if (GeneralTool.isEmpty(mailVoMap)){
            return;
        }
        for (Map.Entry<Long, MailDto> entry : mailVoMap.entrySet()) {
            Long id = entry.getKey();
            MailDto mailVo = entry.getValue();
            StudentOfferNotice studentOfferNotice = studentOfferNoticeMapper.selectById(id);
            if (GeneralTool.isEmpty(studentOfferNotice)){
                continue;
            }
            if (GeneralTool.isNotEmpty(rejectEmailAgentIds)) {
                if (rejectEmailAgentIds.contains(studentOfferNotice.getFkAgentId())) {
                    studentOfferNotice.setStatus(2);
                    utilService.setUpdateInfo(studentOfferNotice);
                    studentOfferNoticeMapper.updateById(studentOfferNotice);
                    continue;
                }
            }

            try {
                //控制发送频率
                //TODO 邮件发送频率控制
                Thread.sleep(1000);
                Boolean aBoolean = reminderCenterClient.customSendMailByBody(mailVo).getData();
                int sendCont = 0;
                //自旋
                while (!aBoolean&&sendCont<3){
                    Thread.sleep(1000);
                    aBoolean = reminderCenterClient.customSendMailByBody(mailVo).getData();
                    sendCont++;
                }
                studentOfferNotice.setGmtModified(new Date());
                studentOfferNotice.setGmtModifiedUser(staffInfo.getLoginId());
                if (aBoolean){
                    studentOfferNotice.setStatus(1);
                }else {
                    studentOfferNotice.setStatus(-1);
                    studentOfferNotice.setRemark("邮件发送失败");
                }
                studentOfferNoticeMapper.updateById(studentOfferNotice);
            }catch (Exception e){
                studentOfferNotice.setStatus(-1);
                studentOfferNotice.setRemark("邮件发送失败");
                studentOfferNoticeMapper.updateById(studentOfferNotice);
                log.error("发送邮件错误",e);
            }
        }
    }


}
