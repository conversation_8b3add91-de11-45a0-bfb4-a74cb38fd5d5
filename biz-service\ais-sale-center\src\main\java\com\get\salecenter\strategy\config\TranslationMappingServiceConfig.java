package com.get.salecenter.strategy.config;

import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.strategy.TranslationBeanStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2024/2/4 10:44
 * @verison: 1.0
 * @description:
 */
@Configuration
public class TranslationMappingServiceConfig {

    @Resource
    private List<TranslationBeanStrategy> translationBeanStrategies;

    @Bean("translationBeanStrategiesMap")
    public Map<String, TranslationBeanStrategy> getTranslationBeanStrategiesMap() {
        if (GeneralTool.isEmpty(translationBeanStrategies)){
            return Collections.emptyMap();
        }
        return translationBeanStrategies.stream().collect(Collectors.toMap(TranslationBeanStrategy::getType, Function.identity()));
    }
}
