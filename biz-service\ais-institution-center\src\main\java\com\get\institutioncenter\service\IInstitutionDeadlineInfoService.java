package com.get.institutioncenter.service;

import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.WeScholarshipAppDto;
import com.get.institutioncenter.vo.InstitutionDeadlineInfoVo;
import com.get.institutioncenter.vo.InstitutionDeadlineInfoVo2;
import com.get.institutioncenter.entity.InstitutionDeadlineInfo;
import com.get.institutioncenter.dto.InstitutionDeadlineInfoDto;
import com.get.institutioncenter.dto.InstitutionDeadlineInfoDto2;
import com.get.institutioncenter.dto.query.InstitutionDeadlineInfoQueryDto;

import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/6 15:44
 */
public interface IInstitutionDeadlineInfoService extends BaseService<InstitutionDeadlineInfo> {
    Long addInstitutionDeadlineInfo(InstitutionDeadlineInfoDto institutionDeadlineInfoDto);

    InstitutionDeadlineInfoVo findInstitutionDeadlineInfoById(Long id);

    void delete(Long id);

    InstitutionDeadlineInfoVo updateInstitutionDeadlineInfo(InstitutionDeadlineInfoDto institutionDeadlineInfoDto);

    List<InstitutionDeadlineInfoVo> datas(InstitutionDeadlineInfoQueryDto data, SearchBean<InstitutionDeadlineInfoQueryDto> page);

    List<InstitutionDeadlineInfoVo2> getWcInstitutionDeadlineInfoDtoDatas(Long fkInstitutionId);
    /**
     * 优先匹配查询
     * @param weScholarshipAppDto
     * @return
     */
    List<InstitutionDeadlineInfoVo> priorityMatchingQuery(WeScholarshipAppDto weScholarshipAppDto);

    List<InstitutionDeadlineInfoVo2> getWcInstitutionDeadlineInfoList(InstitutionDeadlineInfoDto2 data, SearchBean<InstitutionDeadlineInfoDto2> page);
}
