<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.examcenter.mapper.appexam.UserExaminationQuestionScoreMapper">
    <insert id="insert" parameterType="com.get.examcenter.entity.UserExaminationQuestionScore">
    insert into r_user_examination_question_score (id, opt_guid, fk_user_id, fk_examination_paper_id, fk_examination_question_id,
      fk_examination_answer_ids, score, use_time, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{optGuid,jdbcType=VARCHAR}, #{fkUserId,jdbcType=BIGINT},#{fkExaminationPaperId,jdbcType=BIGINT}, #{fkExaminationQuestionId,jdbcType=BIGINT},
      #{fkExaminationAnswerIds,jdbcType=VARCHAR}, #{score,jdbcType=INTEGER}, #{useTime,jdbcType=INTEGER}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.get.examcenter.entity.UserExaminationQuestionScore">
        insert into r_user_examination_question_score
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="optGuid != null">
                opt_guid,
            </if>
            <if test="fkUserId != null">
                fk_user_id,
            </if>
            <if test="fkExaminationPaperId != null">
                fk_examination_paper_id,
            </if>
            <if test="fkExaminationQuestionId != null">
                fk_examination_question_id,
            </if>
            <if test="fkExaminationAnswerIds != null">
                fk_examination_answer_ids,
            </if>
            <if test="score != null">
                score,
            </if>
            <if test="useTime != null">
                use_time,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="optGuid != null">
                #{optGuid,jdbcType=VARCHAR},
            </if>
            <if test="fkUserId != null">
                #{fkUserId,jdbcType=BIGINT},
            </if>
            <if test="fkExaminationPaperId != null">
                #{fkExaminationPaperId,jdbcType=BIGINT},
            </if>
            <if test="fkExaminationQuestionId != null">
                #{fkExaminationQuestionId,jdbcType=BIGINT},
            </if>
            <if test="fkExaminationAnswerIds != null">
                #{fkExaminationAnswerIds,jdbcType=VARCHAR},
            </if>
            <if test="score != null">
                #{score,jdbcType=INTEGER},
            </if>
            <if test="useTime != null">
                #{useTime,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.get.examcenter.entity.UserExaminationQuestionScore">
        update r_user_examination_question_score
        <set>
            <if test="optGuid != null">
                opt_guid = #{optGuid,jdbcType=VARCHAR},
            </if>
            <if test="fkUserId != null">
                fk_user_id = #{fkUserId,jdbcType=BIGINT},
            </if>
            <if test="fkExaminationPaperId != null">
                fk_examination_paper_id = #{fkExaminationPaperId,jdbcType=BIGINT},
            </if>
            <if test="fkExaminationQuestionId != null">
                fk_examination_question_id = #{fkExaminationQuestionId,jdbcType=BIGINT},
            </if>
            <if test="fkExaminationAnswerIds != null">
                fk_examination_answer_ids = #{fkExaminationAnswerIds,jdbcType=VARCHAR},
            </if>
            <if test="score != null">
                score = #{score,jdbcType=INTEGER},
            </if>
            <if test="useTime != null">
                use_time = #{useTime,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.get.examcenter.entity.UserExaminationQuestionScore">
    update r_user_examination_question_score
    set opt_guid = #{optGuid,jdbcType=VARCHAR},
      fk_user_id = #{fkUserId,jdbcType=BIGINT},
      fk_examination_paper_id = #{fkExaminationPaperId,jdbcType=BIGINT},
      fk_examination_question_id = #{fkExaminationQuestionId,jdbcType=BIGINT},
      fk_examination_answer_ids = #{fkExaminationAnswerIds,jdbcType=VARCHAR},
      score = #{score,jdbcType=INTEGER},
      use_time = #{useTime,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <select id="getQuestionByOptGuid" resultType="com.get.examcenter.vo.ExaminationQuestionVo">
    select fk_examination_question_id AS id from r_user_examination_question_score where opt_guid = #{optGuid}
  </select>
    <select id="getScoreAndUseTimeByOptGuid" resultType="com.get.examcenter.vo.UserexaminationPaperScoreVo">
    select IFNULL(sum(score),0) as score,IFNULL(sum(use_time),0) as useTime from r_user_examination_question_score where opt_guid = #{optGuid}
  </select>
    <select id="isExist" resultType="java.lang.Boolean">
select IFNULL(max(id),0) from r_user_examination_question_score where FIND_IN_SET(#{id},fk_examination_answer_ids)
    </select>

    <select id="getUserInfoById" resultType="java.util.Map">
        select * from ais_app_registration_center.m_user where id=#{userId}
    </select>
    <select id="selectAnswers" resultType="com.get.examcenter.entity.UserExaminationQuestionScore">
        <!-- select rueqs.* from r_user_examination_paper_score rueps left join r_user_examination_question_score rueqs
 on rueps.fk_examination_paper_id=rueqs.fk_examination_paper_id
 where 1=1
 and rueps.fk_user_id=#{fkUserId}
 and rueps.fk_user_id=rueqs.fk_user_id
 and rueps.opt_guid=rueqs.opt_guid
 and rueps.fk_examination_paper_id=#{fkExaminationPaperId}
-->
 select * from (
 select * from (select rueqs.fk_examination_answer_ids,rueqs.fk_examination_question_id
 from r_user_examination_paper_score rueps
 left join r_user_examination_question_score rueqs
 on rueps.fk_examination_paper_id=rueqs.fk_examination_paper_id
 where 1=1
 and rueps.fk_user_id=#{fkUserId}
 and rueps.fk_user_id=rueqs.fk_user_id
 and rueps.opt_guid=rueqs.opt_guid
 and rueps.fk_examination_paper_id=#{fkExaminationPaperId}
 and rueps.opt_guid=#{optGuid}
 union
 select null as fk_examination_answer_ids,target_id as fk_examination_question_id from(
       select meqa.target_id from ais_exam_center.m_examination_question_assign meqa where
 fk_examination_paper_id=#{fkExaminationPaperId}
       and EXISTS (select 1 from ais_exam_center.m_examination_question meq where meq.id=meqa.target_id)
       and meqa.target_type=1
       union ALL
       select meq.id target_id from ais_exam_center.m_examination_question meq where meq.fk_question_type_id =
       (select target_id from ais_exam_center.m_examination_question_assign meqa where
 fk_examination_paper_id=#{fkExaminationPaperId}
       and meqa.target_type=0)
       )aa where  EXISTS (select 1 from ais_exam_center.m_examination_question meq where meq.id=aa.target_id)
 )aa group by aa.fk_examination_question_id
 )bb order by fk_examination_question_id
     </select>


 </mapper>