package com.get.aisplatformcenterap.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.get.core.mybatis.base.BaseVoEntity;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ULabelTypeVo extends BaseVoEntity {
    /**
     * 类型名称
     */

    private  String typeName;

    /**
     * 类型Key
     */
    private String typeKey;

    /**
     * 类型描述
     */
    private String remark;

    /**
     * 排序
     */
    private Integer viewOrder;

    /**
     * 标签类型对应的标签
     */
    private List<ULabelVo> labelVoList;
}
