package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.NewsCompany;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface NewsCompanyMapper extends BaseMapper<NewsCompany> {
    int insert(NewsCompany record);

    int insertSelective(NewsCompany record);

    List<Long> getCompanyIds(@Param("fkNewsId") Long fkNewsId);
}