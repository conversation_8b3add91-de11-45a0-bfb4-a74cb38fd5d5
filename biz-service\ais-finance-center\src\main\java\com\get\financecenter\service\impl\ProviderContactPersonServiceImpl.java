package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.ProviderContactPersonMapper;
import com.get.financecenter.vo.ProviderContactPersonVo;
import com.get.financecenter.entity.ProviderContactPerson;
import com.get.financecenter.service.IProviderContactPersonService;
import com.get.financecenter.dto.ProviderContactPersonDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/12/28 11:45
 * @verison: 1.0
 * @description:
 */
@Service
public class ProviderContactPersonServiceImpl extends BaseServiceImpl<ProviderContactPersonMapper, ProviderContactPerson> implements IProviderContactPersonService {
    @Resource
    private ProviderContactPersonMapper providerContactPersonMapper;
    @Resource
    private UtilService utilService;

    @Override
    public ProviderContactPersonVo findProviderContactPersonById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ProviderContactPerson providerContactPerson = providerContactPersonMapper.selectById(id);
        if (GeneralTool.isEmpty(providerContactPerson)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        return BeanCopyUtils.objClone(providerContactPerson, ProviderContactPersonVo::new);
    }

    @Override
    public Long addProviderContactPerson(ProviderContactPersonDto providerContactPersonDto) {
        if (GeneralTool.isEmpty(providerContactPersonDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ProviderContactPerson providerContactPerson = BeanCopyUtils.objClone(providerContactPersonDto, ProviderContactPerson::new);
        String msg = validateAdd(providerContactPersonDto);
        if (GeneralTool.isEmpty(msg)) {
            utilService.updateUserInfoToEntity(providerContactPerson);
            providerContactPersonMapper.insertSelective(providerContactPerson);
        } else {
            throw new GetServiceException(msg);
        }
        return providerContactPerson.getId();
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (providerContactPersonMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        providerContactPersonMapper.deleteById(id);

    }

    @Override
    public ProviderContactPersonVo updateProviderContactPerson(ProviderContactPersonDto providerContactPersonDto) {
        if (providerContactPersonDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ProviderContactPerson result = providerContactPersonMapper.selectById(providerContactPersonDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ProviderContactPerson providerContactPerson = BeanCopyUtils.objClone(providerContactPersonDto, ProviderContactPerson::new);
        String msg = validateUpdate(providerContactPersonDto);
        if (GeneralTool.isEmpty(msg)) {
            utilService.updateUserInfoToEntity(providerContactPerson);
            providerContactPersonMapper.updateById(providerContactPerson);
        } else {
            throw new GetServiceException(msg);
        }
        return findProviderContactPersonById(providerContactPersonDto.getId());
    }

    @Override
    public List<ProviderContactPersonVo> getProviderContactPersons(ProviderContactPersonDto providerContactPersonDto, Page page) {
//        Example example = new Example(ProviderContactPerson::new);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkProviderId", providerContactPersonVo.getFkProviderId());
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<ProviderContactPerson> providerContactPersons = providerContactPersonMapper.selectByExample(example);
//        page.restPage(providerContactPersons);

        LambdaQueryWrapper<ProviderContactPerson> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(providerContactPersonDto.getFkProviderId())) {
            wrapper.eq(ProviderContactPerson::getFkProviderId, providerContactPersonDto.getFkProviderId());
        }
        IPage<ProviderContactPerson> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<ProviderContactPerson> providerContactPersons = pages.getRecords();

        return providerContactPersons.stream().map(providerContactPerson ->
                BeanCopyUtils.objClone(providerContactPerson, ProviderContactPersonVo::new)).collect(Collectors.toList());
    }

    private String validateAdd(ProviderContactPersonDto providerContactPersonDto) {
        //111111留意检查不同criteria及orEqualTo的用法解释
//        Example example = new Example(ProviderContactPerson::new);
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//        criteria.andEqualTo("fkProviderId", providerContactPersonVo.getFkProviderId());
//        criteria1.andEqualTo("mobile", providerContactPersonVo.getMobile());
//        criteria1.orEqualTo("tel", providerContactPersonVo.getTel());
//        criteria1.orEqualTo("email", providerContactPersonVo.getEmail());
//        List<ProviderContactPerson> list = this.providerContactPersonMapper.selectByExample(example);


        LambdaQueryWrapper<ProviderContactPerson> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(providerContactPersonDto.getFkProviderId())) {
            wrapper.eq(ProviderContactPerson::getFkProviderId, providerContactPersonDto.getFkProviderId());
        }
        wrapper.and(wrapper_ ->
                wrapper_.eq(ProviderContactPerson::getMobile, providerContactPersonDto.getMobile()).or()
                        .eq(ProviderContactPerson::getTel, providerContactPersonDto.getTel()).or()
                        .eq(ProviderContactPerson::getEmail, providerContactPersonDto.getEmail()));
        List<ProviderContactPerson> list = this.providerContactPersonMapper.selectList(wrapper);
        StringJoiner stringJoiner = new StringJoiner("，");
        for (ProviderContactPerson providerContactPerson : list) {
            if (providerContactPerson.getMobile().equals(providerContactPersonDto.getMobile())) {
                stringJoiner.add("移动电话已存在");
            }
            if (providerContactPerson.getTel().equals(providerContactPersonDto.getTel())) {
                stringJoiner.add("电话已存在");
            }
            if (providerContactPerson.getEmail().equals(providerContactPersonDto.getEmail())) {
                stringJoiner.add("电邮已存在");
            }
        }
        return stringJoiner.toString();
    }

    private String validateUpdate(ProviderContactPersonDto providerContactPersonDto) {
//        Example example = new Example(ProviderContactPerson::new);
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//        criteria.andEqualTo("fkProviderId", providerContactPersonVo.getFkProviderId());
//        criteria1.andEqualTo("mobile", providerContactPersonVo.getMobile());
//        criteria1.orEqualTo("tel", providerContactPersonVo.getTel());
//        criteria1.orEqualTo("email", providerContactPersonVo.getEmail());
//        List<ProviderContactPerson> list = this.providerContactPersonMapper.selectByExample(example);

        LambdaQueryWrapper<ProviderContactPerson> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(providerContactPersonDto.getFkProviderId())) {
            wrapper.eq(ProviderContactPerson::getFkProviderId, providerContactPersonDto.getFkProviderId());
        }
        wrapper.and(wrapper_ ->
                wrapper_.eq(ProviderContactPerson::getMobile, providerContactPersonDto.getMobile()).or()
                        .eq(ProviderContactPerson::getTel, providerContactPersonDto.getTel()).or()
                        .eq(ProviderContactPerson::getEmail, providerContactPersonDto.getEmail()));
        List<ProviderContactPerson> list = this.providerContactPersonMapper.selectList(wrapper);

        StringJoiner stringJoiner = new StringJoiner("，");
        for (ProviderContactPerson providerContactPerson : list) {
            if (!providerContactPersonDto.getId().equals(providerContactPerson.getId())) {
                if (providerContactPerson.getMobile().equals(providerContactPersonDto.getMobile())) {
                    stringJoiner.add("移动电话已存在");
                }
                if (providerContactPerson.getTel().equals(providerContactPersonDto.getTel())) {
                    stringJoiner.add("电话已存在");
                }
                if (providerContactPerson.getEmail().equals(providerContactPersonDto.getEmail())) {
                    stringJoiner.add("电邮已存在");
                }
            }
        }
        return stringJoiner.toString();
    }
}
