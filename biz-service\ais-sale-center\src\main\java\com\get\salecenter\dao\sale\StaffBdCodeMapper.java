package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.dto.StaffBdCodeDto;
import com.get.salecenter.entity.StaffBdCode;
import com.get.salecenter.vo.BdSelectVo;
import com.get.salecenter.vo.StaffBdCodeVo;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StaffBdCodeMapper extends BaseMapper<StaffBdCode> {

    int insertSelective(StaffBdCode staffBdCode);

    List<BaseSelectEntity> getStaffByBdName(@Param("companyIds")List<Long> companyIds, @Param("bdName")String bdName);

    List<Long> getStaffIdsByFkAreaRegionId(@Param("fkAreaRegionId") Long fkAreaRegionId);

    /**
     * 峰会BD下拉框
     *
     * @Date 14:56 2022/8/9
     * <AUTHOR>
     */
    List<BaseSelectEntity> getAreaRegionSelectByConventionId(@Param("conventionId") Long conventionId, @Param("charFilterList") List<String> charFilterList, @Param("bdCodeFilterList") List<Long> bdCodeFilterList);
    /**
     * 根据代理ids获取bd
     * <AUTHOR>
     * @DateTime 2023/1/11 14:43
     */
    List<StaffBdCodeVo> getBDbyAgentIds(@Param("fkAgentIds") Set<Long> fkAgentIds);

    /**
     * 获取所有公司的bd列表，并排除指定bd
     *
     * @param companyId        公司Id
     * @param charFilterList   通用字符过滤
     * @param bdCodeFilterList BDCode过滤
     * @return
     */
    List<BdSelectVo> getBdSelect(@Param("companyId") Long companyId, @Param("charFilterList") List<String> charFilterList, @Param("bdCodeFilterList") List<Long> bdCodeFilterList);
    List<BaseSelectEntity> getStaff(List<Long> companyIds);

//    List<StaffBdCode> selectStaffBdAll(@Param("staffBdCodeDto") StaffBdCodeDto staffBdCodeDto,@Param(Constants.WRAPPER)LambdaQueryWrapper<StaffBdCode> lambdaQueryWrapper);
    List<StaffBdCode> selectStaffBdCodeAll(IPage<StaffBdCode> iPage,@Param("staffBdCodeDto") StaffBdCodeDto staffBdCodeDto, @Param("companyIdList") List<Long> companyIds, @Param("staffIdList") List<Long> staffIds);
}