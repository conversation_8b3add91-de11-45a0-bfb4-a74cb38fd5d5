package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/17
 * @TIME: 17:26
 * @Description:
 **/
@Data
public class InstitutionPathwayDto extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @NotNull(message = "学校Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionId;
    /**
     * 桥梁学校Id
     */
    @ApiModelProperty(value = "桥梁学校Id")
    @NotNull(message = "桥梁学校Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionIdPathway;
    /**
     * 桥梁学校Id数组
     */
    @ApiModelProperty(value = "桥梁学校Id数组，绑定学校的参数")
    private List<Long> fkInstitutionIdPathways;
    /**
     * 桥梁方向绑定学校Id数组，绑定学校的参数
     *
     * @Date 16:46 2021/7/22
     * <AUTHOR>
     */
    @ApiModelProperty(value = "桥梁方向绑定学校Id数组，绑定学校的参数")
    private List<Long> fkInstitutionIds;
}
