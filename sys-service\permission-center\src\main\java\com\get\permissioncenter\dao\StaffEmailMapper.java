package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.permissioncenter.entity.StaffEmail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StaffEmailMapper extends BaseMapper<StaffEmail> {

    /**
     * 验证同分公司下的员工邮件地址是否有重复
     *
     * @Date 14:52 2022/11/22
     * <AUTHOR>
     */
    List<String> checkStaffEmail(@Param("fkCompanyId") Long fkCompanyId, @Param("email") String email,@Param("staffId")Long staffId);
}