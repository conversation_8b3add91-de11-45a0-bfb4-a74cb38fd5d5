package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.component.TranslationHelp;
import com.get.institutioncenter.dao.AreaCountryInfoMapper;
import com.get.institutioncenter.dto.AreaCountryInfoDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.AreaCountryInfoVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.entity.AreaCountryInfo;
import com.get.institutioncenter.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: Sea
 * @create: 2021/1/13 17:05
 * @verison: 1.0
 * @description:
 */
@Service
public class AreaCountryInfoServiceImpl extends BaseServiceImpl<AreaCountryInfoMapper, AreaCountryInfo> implements IAreaCountryInfoService {
    @Resource
    private AreaCountryInfoMapper areaCountryInfoMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IAreaCountryInfoTypeService areaCountryInfoTypeService;
    @Resource
    private IAreaCountryService areaCountryService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private ITranslationMappingService translationMappingService;
    @Resource
    private GetRedis redisClient;
    @Resource
    private TranslationHelp translationHelp;

    @Override
    public AreaCountryInfoVo findAreaCountryInfoById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AreaCountryInfo areaCountryInfo = areaCountryInfoMapper.selectById(id);
        if (GeneralTool.isEmpty(areaCountryInfo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AreaCountryInfoVo areaCountryInfoVo = BeanCopyUtils.objClone(areaCountryInfo, AreaCountryInfoVo::new);
        //设置返回结果
        areaCountryInfoVo.setCountryName(areaCountryService.getCountryNameById(areaCountryInfoVo.getFkAreaCountryId()));
        areaCountryInfoVo.setAreaCountryInfoTypeName(areaCountryInfoTypeService.getAreaCountryInfoTypeNameById(areaCountryInfoVo.getFkAreaCountryInfoTypeId()));
        areaCountryInfoVo.setPublicLevelName(getPublicLevelName(areaCountryInfoVo));
        areaCountryInfoVo.setFkTableName(TableEnum.INSTITUTION_COUNTRY_INFO.key);
        String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(areaCountryInfoVo) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(Collections.singletonList(areaCountryInfoVo), ProjectKeyEnum.getInitialValue(language));
        }
        return areaCountryInfoVo;
    }

    @Override
    public Long addAreaCountryInfo(AreaCountryInfoDto areaCountryInfoDto) {
        if (GeneralTool.isEmpty(areaCountryInfoDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AreaCountryInfo areaCountryInfo = BeanCopyUtils.objClone(areaCountryInfoDto, AreaCountryInfo::new);
        utilService.updateUserInfoToEntity(areaCountryInfo);
        areaCountryInfoMapper.insertSelective(areaCountryInfo);
        return areaCountryInfo.getId();
    }

    /**
     * 删除国家资讯
     *
     * @param id
     * @
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (areaCountryInfoMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        areaCountryInfoMapper.deleteById(id);
        //删除资讯翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_COUNTRY_INFO.key, id);
    }

    @Override
    public AreaCountryInfoVo updateAreaCountryInfo(AreaCountryInfoDto areaCountryInfoDto) {
        if (areaCountryInfoDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        AreaCountryInfo result = areaCountryInfoMapper.selectById(areaCountryInfoDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AreaCountryInfo areaCountryInfo = BeanCopyUtils.objClone(areaCountryInfoDto, AreaCountryInfo::new);
        utilService.updateUserInfoToEntity(areaCountryInfo);
        areaCountryInfoMapper.updateById(areaCountryInfo);
        return findAreaCountryInfoById(areaCountryInfo.getId());
    }

    @Override
    public List<AreaCountryInfoVo> getAreaCountryInfos(AreaCountryInfoDto areaCountryInfoDto, Page page) {
        LambdaQueryWrapper<AreaCountryInfo> wrapper = new LambdaQueryWrapper();
        wrapper.eq(AreaCountryInfo::getFkAreaCountryId, areaCountryInfoDto.getFkAreaCountryId());
        if (GeneralTool.isNotEmpty(areaCountryInfoDto)) {
            if (GeneralTool.isNotEmpty(areaCountryInfoDto.getFkAreaCountryInfoTypeId())) {
                wrapper.eq(AreaCountryInfo::getFkAreaCountryInfoTypeId, areaCountryInfoDto.getFkAreaCountryInfoTypeId());
            }
            if (GeneralTool.isNotEmpty(areaCountryInfoDto.getTitle())) {
                wrapper.like(AreaCountryInfo::getTitle, areaCountryInfoDto.getTitle());
            }
            if (GeneralTool.isNotEmpty(areaCountryInfoDto.getPublicLevel())) {
                wrapper.like(AreaCountryInfo::getPublicLevel, areaCountryInfoDto.getPublicLevel());
            }
        }
        wrapper.orderByDesc(AreaCountryInfo::getViewOrder);
        //获取分页数据
        IPage<AreaCountryInfo> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<AreaCountryInfo> areaCountryInfos = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<AreaCountryInfoVo> convertDatas = new ArrayList<>();
        for (AreaCountryInfo areaCountryInfo : areaCountryInfos) {
            AreaCountryInfoVo areaCountryInfoVo = BeanCopyUtils.objClone(areaCountryInfo, AreaCountryInfoVo::new);
            //设置返回结果
            areaCountryInfoVo.setCountryName(areaCountryService.getCountryNameById(areaCountryInfoVo.getFkAreaCountryId()));
            areaCountryInfoVo.setAreaCountryInfoTypeName(areaCountryInfoTypeService.getAreaCountryInfoTypeNameById(areaCountryInfoVo.getFkAreaCountryInfoTypeId()));
            areaCountryInfoVo.setPublicLevelName(getPublicLevelName(areaCountryInfoVo));
            convertDatas.add(areaCountryInfoVo);
        }
        String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(convertDatas) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(convertDatas, ProjectKeyEnum.getInitialValue(language));
        }
        return convertDatas;
    }

    @Override
    public List<MediaAndAttachedVo> getItemMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("tableId_null"));
        }
        attachedVo.setFkTableName(TableEnum.INSTITUTION_COUNTRY_INFO.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.INSTITUTION_COUNTRY_INFO.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    /**
     * @return java.util.StringJoiner
     * @Description :公开对象拼接结果
     * @Param [areaCountryInfo]
     * <AUTHOR>
     */
    private String getPublicLevelName(AreaCountryInfoVo areaCountryInfoVo) {
        StringJoiner stringJoiner = new StringJoiner(",");
        for (String publicLevel : areaCountryInfoVo.getPublicLevel().split(",")) {
            stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(publicLevel), ProjectExtraEnum.PUBLIC_OBJECTS));
        }
        return stringJoiner.toString();
    }
}
