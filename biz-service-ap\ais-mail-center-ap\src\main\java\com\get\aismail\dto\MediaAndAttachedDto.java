package com.get.aismail.dto;


import com.get.aismail.entity.MediaAndAttached;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2020/8/4
 * @TIME: 15:01
 * @Description:
 **/
@Data
@ApiModel("附件信息返回类")
public class MediaAndAttachedDto extends MediaAndAttached {
    @ApiModelProperty(value = "文件路径")
    private String filePath;

    /**
     * 源文件名
     */
    @ApiModelProperty(value = "源文件名")
    private String fileNameOrc;
    @ApiModelProperty(value = "文件类型描述")
    private String typeValue;
    /**
     * 文件key
     */
    @ApiModelProperty(value = "文件key")
    private String fileKey;
}
