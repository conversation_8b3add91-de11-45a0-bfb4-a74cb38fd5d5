package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.AreaCityInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: Sea
 * @create: 2021/3/2 11:21
 * @verison: 1.0
 * @description:
 */
@Mapper
public interface AreaCityInfoMapper extends BaseMapper<AreaCityInfo> {
    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(AreaCityInfo record);

    /**
     * @return boolean
     * @Description :校验城市能否删除
     * @Param [areaCityId]
     * <AUTHOR>
     */
    boolean areaCityInfoIsEmpty(@Param("areaCityId") Long areaCityId);
}