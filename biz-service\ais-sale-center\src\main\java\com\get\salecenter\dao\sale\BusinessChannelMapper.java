package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.BusinessChannelVo;
import com.get.salecenter.entity.BusinessChannel;
import com.get.salecenter.dto.BusinessChannelDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface BusinessChannelMapper extends BaseMapper<BusinessChannel> {
    /*  =========path=========
    int insert(BusinessChannel record);

    int insertSelective(BusinessChannel record);*/

    List<BusinessChannelVo> getDatas(IPage<BusinessChannelVo> iPage, @Param("businessChannelDto") BusinessChannelDto businessChannelDto);

    /**
     * @Description：id获取名称
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    String getNameById(Long id);


    List<BusinessChannelVo> getNamesByIds(@Param("ids") Set<Long> ids);


    List<Long> getBusinessId(@Param("fkTypeKey") String fkTypeKey, @Param("targetIds")Set<Long> targetIds);

    Set<Long> getBusinessProviderIdByAccIds(@Param("ids")Set<Long> ids);

    /**
     * 渠道下拉框
     *
     * @param
     * @return
     */
    List<BaseSelectEntity> channelSelect(@Param("tableName") String tableName,@Param("companyId") Long companyId);

    String getFullNameById(Long id);

    /**
     * 获取渠道名称
     *
     * @param ids
     * @return
     */
    List<BusinessChannelVo> getFullNameByIds(@Param("ids") Set<Long> ids);

    List<BaseSelectEntity> getTargetName(@Param("tableName") String tableName, @Param("companyId") Long companyId);


    List<Long> getChannelIds(@Param("tableName") String tableName, @Param("channelName") String channelName);

    List<BusinessChannelVo> getPlanIdsByTableNameAndChannelId(@Param("tableName") String tableName, @Param("channelId") Long channelId
            , @Param("receiptFormId")Long receiptFormId, @Param("fkTypeKey")String fkTypeKey, @Param("offset") Integer offset, @Param("pageSize")Integer pageSize);

    List<BusinessChannelVo> getPlanIdsByChannel(@Param("channelId") Long channelId
            , @Param("receiptFormId")Long receiptFormId, @Param("fkTypeKey")String fkTypeKey, @Param("offset") Integer offset, @Param("pageSize")Integer pageSize);

    List<BaseSelectEntity> getChannelByWork( @Param("companyIds")List<Long> companyIds, @Param("keyWord") String keyWord, @Param("fkType") String fkType);
}