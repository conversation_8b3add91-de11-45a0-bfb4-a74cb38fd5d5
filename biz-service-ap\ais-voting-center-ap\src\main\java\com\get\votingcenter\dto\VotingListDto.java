package com.get.votingcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * @author: Hardy
 * @create: 2021/9/23 11:01
 * @verison: 1.0
 * @description:
 */
@Data
public class VotingListDto extends BaseVoEntity implements Serializable {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 主题名称
     */
    @ApiModelProperty(value = "主题名称")
    @Column(name = "theme_name")
    private String themeName;

}
