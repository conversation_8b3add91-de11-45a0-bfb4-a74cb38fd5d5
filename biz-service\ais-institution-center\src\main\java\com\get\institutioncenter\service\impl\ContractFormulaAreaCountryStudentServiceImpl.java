package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContractFormulaAreaCountryStudentMapper;
import com.get.institutioncenter.entity.ContractFormulaAreaCountryStudent;
import com.get.institutioncenter.service.IContractFormulaAreaCountryStudentService;
import com.get.institutioncenter.dto.ContractFormulaAreaCountryStudentDto;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/3/29 11:34
 * @verison: 1.0
 * @description:
 */
@Service
public class ContractFormulaAreaCountryStudentServiceImpl extends BaseServiceImpl<ContractFormulaAreaCountryStudentMapper, ContractFormulaAreaCountryStudent> implements IContractFormulaAreaCountryStudentService {
    @Resource
    private ContractFormulaAreaCountryStudentMapper contractFormulaAreaCountryStudentMapper;
    @Resource
    private UtilService utilService;

    @Override
    public Long addContractFormulaAreaCountryStudent(ContractFormulaAreaCountryStudentDto contractFormulaAreaCountryVo) {
        if (GeneralTool.isEmpty(contractFormulaAreaCountryVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ContractFormulaAreaCountryStudent contractFormulaAreaCountry = BeanCopyUtils.objClone(contractFormulaAreaCountryVo, ContractFormulaAreaCountryStudent::new);
        utilService.updateUserInfoToEntity(contractFormulaAreaCountry);
        contractFormulaAreaCountryStudentMapper.insertSelective(contractFormulaAreaCountry);
        return contractFormulaAreaCountry.getId();
    }

    @Override
    public void deleteByFkid(Long contractFormulaId) {
        LambdaQueryWrapper<ContractFormulaAreaCountryStudent> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaAreaCountryStudent::getFkContractFormulaId, contractFormulaId);
        contractFormulaAreaCountryStudentMapper.delete(wrapper);
    }

    @Override
    public List<Long> getStudentCountryIdListByFkid(Long contractFormulaId) {
        return contractFormulaAreaCountryStudentMapper.getStudentCountryIdListByFkid(contractFormulaId);
    }

    @Override
    public String getStudentCountryNameByFkid(Long contractFormulaId) {
        List<String> countryNameList = contractFormulaAreaCountryStudentMapper.getStudentCountryNameByFkid(contractFormulaId);
        String result = "";
        if (GeneralTool.isNotEmpty(countryNameList)) {
            result = StringUtils.join(countryNameList, ",");
        }
        return result;
    }
}
