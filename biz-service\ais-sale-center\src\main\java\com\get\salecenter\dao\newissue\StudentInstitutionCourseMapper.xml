<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.newissue.StudentInstitutionCourseMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.NewIssueStudentInstitutionCourse">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_student_id" jdbcType="BIGINT" property="fkStudentId"/>
        <result column="fk_area_country_id" jdbcType="BIGINT" property="fkAreaCountryId"/>
        <result column="fk_institution_id" jdbcType="BIGINT" property="fkInstitutionId"/>
        <result column="fk_institution_faculty_id" jdbcType="BIGINT" property="fkInstitutionFacultyId"/>
        <result column="fk_institution_zone_id" jdbcType="BIGINT" property="fkInstitutionZoneId"/>
        <result column="fk_major_level_id" jdbcType="BIGINT" property="fkMajorLevelId"/>
        <result column="fk_institution_course_id" jdbcType="BIGINT" property="fkInstitutionCourseId"/>
        <result column="institution_course_name" jdbcType="VARCHAR" property="institutionCourseName"/>
        <result column="institution_course_website" jdbcType="VARCHAR" property="institutionCourseWebsite"/>
        <result column="opening_time" jdbcType="DATE" property="openingTime"/>
        <result column="view_order" jdbcType="INTEGER" property="viewOrder"/>
        <result column="is_add_app" jdbcType="BIT" property="isAddApp"/>
        <result column="status_step" jdbcType="INTEGER" property="statusStep"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.get.salecenter.entity.NewIssueStudentInstitutionCourse">
        <result column="info_json" jdbcType="LONGVARCHAR" property="infoJson"/>
    </resultMap>

</mapper>