package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.service.GetService;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.StudentAccommodationVo;
import com.get.salecenter.entity.StudentAccommodation;
import com.get.salecenter.dto.AccommodationSummaryDto;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.StudentAccommodationDto;
import com.get.salecenter.dto.query.AccommodationSummaryQueryDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2022/1/10
 * @TIME: 14:41
 * @Description:
 **/
public interface IStudentAccommodationService extends GetService<StudentAccommodation> {


    /**
     * @return java.lang.Long
     * @Description: 新增留学住宿
     * @Param [studentAccommodationDto]
     * <AUTHOR>
     */
    void addStudentAccommodation(StudentAccommodationDto studentAccommodationDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.BusinessChannelVo>
     * @Description: 查询留学住宿
     * @Param [businessChannelVo, page]
     * <AUTHOR>
     */
    List<StudentAccommodationVo> getStudentAccommodationDtos(StudentAccommodationDto studentAccommodationDto, Page page);


    /**
     * @return com.get.salecenter.vo.BusinessChannelVo
     * @Description: 修改
     * @Param [businessChannelVo]
     * <AUTHOR>
     */
    StudentAccommodationVo updateStudentAccommodation(StudentAccommodationDto studentAccommodationDto);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void deleteStudentAccommodation(Long id);

    /**
     * 查询byID
     *
     * @param id
     * @return
     * @
     */
    StudentAccommodationVo findStudentAccommodationById(Long id);

    /**
     * 关闭
     *
     * @param id
     * @return
     * @
     */
    void closeStudentAccommodationById(Long id);

    /**
     * 保存附件
     *
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addStudentAccommodationMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 获取附件类型
     *
     * @return
     */
    List<Map<String, Object>> findMediaAndAttachedType();

    /**
     * 查询附件
     *
     * @param data
     * @param page
     * @return
     * @
     */
    List<MediaAndAttachedVo> getStudentAccommodationMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentAccommodationVo>
     * @Description: 获取住宿列表数据 （学生详情获取）
     * @Param [studentId, keyWord]
     * <AUTHOR>
     */
    List<StudentAccommodationVo> getStudentAccommodationList(StudentAccommodationDto studentAccommodationDto, Page page);

    /**
     * @return java.lang.Long
     * @Description: 增加或者更新评论
     * @Param [commentDto]
     * <AUTHOR>
     **/
    Long editComment(CommentDto commentDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.CommentVo>
     * @Description: 获取所有评论
     * @Param [commentDto, page]
     * <AUTHOR>
     */
    List<CommentVo> getComments(CommentDto commentDto, Page page);

    List<Map<String, Object>> getAccommodationExpenseType();


    /**
     * @return java.util.List<com.get.salecenter.vo.StudentAccommodationVo>
     * @Description: 获取所有住宿汇总
     * @Param [commentVo, page]
     * <AUTHOR>
     */
    List<StudentAccommodationVo> getStudentAccommodationSummary(AccommodationSummaryQueryDto studentAccommodation, Page page, String[] times);

    /**
     * @return
     * @Description: 创建应收应付
     * @Param [accommodationSummaryDto]
     * <AUTHOR>
     */
    void createARAP(AccommodationSummaryDto accommodationSummaryDto);

    /**
     * 激活
     *
     * @param id
     * @return
     * @
     */
    void activationStudentAccommodationById(Long id);

    void exportExcel(HttpServletResponse response, AccommodationSummaryQueryDto studentAccommodationVo);

    /**
     *  获取留学住宿id
     * @param targetId
     * @return
     */
    Long getAccommodationAgentId(Long targetId);

    /**
     *
     * @param targetId
     * @return
     */
    Long getStudentAccommodationId(Long targetId);

    /**
     * 状态下拉框
     * @return
     */
    List<Map<String, Object>> getAccommodationStatusSelect();

    //根据id获取住宿方案编号
    Map<Long, String> getNumByIds(Set<Long> accommodationIds);
    /**
     * 合并留学住宿数据
     * @param mergedStudentId
     * @param targetStudentId
     */
    void mergeData(Long mergedStudentId, Long targetStudentId);

    StudentAccommodation getStudentAccommodationById(Long id);
}
