package com.get.common.utils;

import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;

public class DataConverter {


    public static BigDecimal bigDecimalNullConvert(BigDecimal val){
        if (Objects.isNull(val)) {
            return BigDecimal.ZERO;
        }
        return val;
    }

    //小写去空格处理
    public static String stringManipulation(String val){
        if (StringUtils.isNotBlank(val)) {
            val = val.replaceAll(" ","").toLowerCase();
        }
        return val;
    }

    public static boolean determineBoolean(Boolean val){
        if (Objects.isNull(val)) {
            return false;
        }
        return val;
    }
}
