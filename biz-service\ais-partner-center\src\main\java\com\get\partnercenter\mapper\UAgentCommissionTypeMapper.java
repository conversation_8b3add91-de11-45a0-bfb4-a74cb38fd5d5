package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.partnercenter.entity.UAgentCommissionTypeEntity;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【u_agent_commission_type】的数据库操作Mapper
* @createDate 2025-03-24 16:53:09
* @Entity com.get.partnercenter.entity.UAgentCommissionType
*/
@Mapper
@DS("pmp2db")
public interface UAgentCommissionTypeMapper extends BaseMapper<UAgentCommissionTypeEntity> {



}




