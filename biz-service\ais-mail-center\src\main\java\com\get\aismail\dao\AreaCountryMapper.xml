<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.aismail.dao.AreaCountryMapper">
  <select id="getAreaCode" resultType="com.get.aismail.dto.AreaCountryDto">
    SELECT
      GROUP_CONCAT( areaCodeValue ) as areaCodeValue,
      area_code
    FROM
      (
        SELECT
          CONCAT( name_chn, ' +', area_code ) AS areaCodeValue,
          area_code
        FROM
          u_area_country
        WHERE
          area_code IS NOT NULL
          AND area_code != ''
        ORDER BY
          view_order DESC
      ) a
    GROUP BY
      a.area_code
  </select>


  <select id="getAllCountryList" resultType="com.get.aismail.dto.AreaCountryDto">
    select id,num,name,name_chn,
    <!--name as fullName-->
    CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName
    from u_area_country  order by view_order desc
  </select>

</mapper>