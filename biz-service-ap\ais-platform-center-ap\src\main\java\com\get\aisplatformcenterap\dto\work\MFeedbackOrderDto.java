package com.get.aisplatformcenterap.dto.work;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class MFeedbackOrderDto {

    @NotNull(message = "工单类型不能为空")
    @ApiModelProperty("工单类型：1我的工单 2全部工单")
    private Integer orderType;





    @ApiModelProperty("公司Id")
    private Long fkCompanyId;
    @ApiModelProperty("平台应用Id")
    private Long fkPlatformId;
    @ApiModelProperty("反馈工单类型Id")
    private Long fkFeedbackOrderTypeId;
    @ApiModelProperty("状态：0待处理/1已回复/2已关闭/3系统自动关闭")
    private Integer status;
    @ApiModelProperty("标题")
    private String title;




    List<Long> staffFollowerIds;
}
