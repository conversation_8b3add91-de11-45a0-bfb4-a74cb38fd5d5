package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AiTravelClaimFormVo {
    @ApiModelProperty(value = "id")
    @JsonIgnore
    private Long id;

    @ApiModelProperty(value = "申请人Id")
    @JsonIgnore
    private Long fkStaffId;

    @ApiModelProperty(value = "币种编号")
    @JsonIgnore
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "票据张数")
    private Integer billCount;

    @ApiModelProperty(value = "状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
    private String status;

//    /**
//     * 公司名称
//     */
//    @ApiModelProperty(value = "公司名称")
//    private String companyName;
//
//    /**
//     * 部门名称
//     */
//    @ApiModelProperty(value = "部门名称")
//    private String departmentName;

    /**
     * 报销人名称
     */
    @ApiModelProperty(value = "报销人名称")
    private String staffName;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyTypeName;

//    /**
//     * 报销单状态 0待签1代办2无
//     */
//    @ApiModelProperty(value = "报销单状态 0待签1代办2无")
//    private Integer expenseClaimFormStatus;
//
//    /**
//     * 任务id
//     */
//    @ApiModelProperty(value = "任务id")
//    private Long taskId;
//    /**
//     * 实例id
//     */
//    @ApiModelProperty(value = "实例id")
//    private Long procInstId;
//
//    /**
//     * 任务版本号
//     */
//    @ApiModelProperty(value = "任务版本号")
//    private Integer taskVersion;

    /**
     * 报销金额总和
     */
    @ApiModelProperty(value = "报销金额总和")
    private BigDecimal amountSum;

    //    /**
//     * 同意按钮状态
//     */
//    @ApiModelProperty(value = "同意按钮状态")
//    private Boolean agreeButtonType;
//
//    /**
//     * 拒绝按钮状态
//     */
//    @ApiModelProperty(value = "拒绝按钮状态")
//    private Boolean refuseButtonType;
//
//    @ApiModelProperty(value = "创建凭证人名字")
//    private String fkStaffIdVouchCreatedName;
    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    /**
     * 表单明细对象集合
     */
    @ApiModelProperty(value = "表单明细对象集合")
    private List<AiTravelClaimFormItemVo> travelClaimFormItemDtoList;

}
