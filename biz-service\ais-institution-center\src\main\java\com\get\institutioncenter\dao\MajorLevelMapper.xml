<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.MajorLevelMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.MajorLevel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="level_name" jdbcType="VARCHAR" property="levelName" />
    <result column="level_name_chn" jdbcType="VARCHAR" property="levelNameChn" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>

  <insert id="insert" parameterType="com.get.institutioncenter.entity.MajorLevel" >
    insert into u_major_level (id, level_name,level_name_chn, view_order,
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{levelName,jdbcType=VARCHAR}, #{levelNameChn,jdbcType=VARCHAR}, #{viewOrder,jdbcType=INTEGER},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.MajorLevel" keyProperty="id" useGeneratedKeys="true">
    insert into u_major_level
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="levelName != null">
        level_name,
      </if>
      <if test="levelNameChn != null">
        level_name_chn,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="levelName != null">
      #{levelName,jdbcType=VARCHAR},
    </if>
      <if test="levelNameChn != null">
        #{levelNameChn,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getMajorLevelNameById" parameterType="java.lang.Long" resultType="java.lang.String">
    select
      i.level_name
    from
      u_major_level i
    where
      i.id = #{id}
  </select>

  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
     IFNULL(max(view_order)+1,0) view_order
    from
     u_major_level

  </select>

  <select id="getMajorLevelSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    select
      id,level_name_chn as nameChn,level_name as name,CASE WHEN IFNULL(level_name_chn,'')='' THEN `level_name` ELSE CONCAT(`level_name`,'（',level_name_chn,'）')
      END
      fullName
    from
     u_major_level
    order by
     view_order
    desc

  </select>
  <select id="getMajorLevelByContractFormula" resultType="java.lang.Long">
       select ml.id from u_major_level ml left join r_contract_formula_major_level cfml
       on ml.id = cfml.fk_major_level_id where cfml.fk_contract_formula_id = #{id}
  </select>

  <select id="getMajorLevelAndGroupSelect" resultType="com.get.institutioncenter.entity.MajorLevel">
    select
      *
    from
      u_major_level
    order by
      view_order
            desc

  </select>
</mapper>