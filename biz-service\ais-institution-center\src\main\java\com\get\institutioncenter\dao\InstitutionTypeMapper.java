package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.entity.InstitutionType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/30 11:13
 * @verison: 1.0
 * @description: 学校类型管理mapper
 */
@Mapper
public interface InstitutionTypeMapper extends BaseMapper<InstitutionType> {
    /**
     * 添加
     *
     * @param record
     * @return
     */
    @Override
    int insert(InstitutionType record);

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(InstitutionType record);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

    /**
     * @return java.lang.String
     * @Description: 获取学校类型名称
     * @Param [id]
     * <AUTHOR>
     **/
    String getInstitutionTypeNameById(@Param("id") Long id);

    /**
     * @return java.lang.String
     * @Description: 获取学校类型key
     * @Param [id]
     * <AUTHOR>
     **/
    String getInstitutionTypeKeyById(@Param("id") Long id);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :学校类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getInstitutionTypeList(@Param("translationFlag") boolean translationFlag, @Param("ids") Set<Long> ids);


    boolean pathwayInstitutionIsEmpty(@Param("id") Long id);


}