package com.get.gateway.common;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.
 * Time: 9:39
 * Date: 2025/7/2
 * Description:异常管理
 */
public class BusinessExceptionUtils {
    /**
     * 创建 BusinessException
     *
     * @param msg
     * @return
     */
    public static BusinessException businessException(String msg) {
        return new BusinessException(null, msg);
    }

    /**
     * 创建 BusinessException
     *
     * @param code
     * @param msg
     * @return
     */
    public static BusinessException businessException(String code, String msg) {
        return new BusinessException(code, msg);
    }

    /**
     * 创建 BusinessException
     *
     * @param code
     * @param msg
     * @param cause
     * @return
     */
    public static BusinessException businessException(String code, String msg, Throwable cause) {
        return new BusinessException(code, msg, cause);
    }
}
