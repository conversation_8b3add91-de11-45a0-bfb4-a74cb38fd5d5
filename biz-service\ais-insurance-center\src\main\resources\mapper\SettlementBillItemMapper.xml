<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.insurancecenter.mapper.SettlementBillItemMapper">


    <select id="selectSettlementBillItemListByBillId"
            resultType="com.get.insurancecenter.vo.commission.SettlementBillItemVo">
        SELECT o.*,
               t.type_key            AS productTypeKey,
               t.type_name           AS productTypeName,
               c.NAME                AS insuranceCompanyName,
               os.status_settlement  as settlementStatus,
               os.fk_payable_plan_id as payablePlanId,
               os.fk_num_opt_batch   as numOptBatch,
               os.id                 as orderSettlementId,
               bi.id                 as settlementBillItemId
        FROM m_insurance_order o
                 INNER JOIN m_insurance_order_settlement os on os.fk_insurance_order_id = o.id
                 INNER JOIN m_settlement_bill_item bi on bi.fk_insurance_order_settlement_id = os.id
                 LEFT JOIN u_product_type t ON t.id = o.fk_product_type_id
                 LEFT JOIN u_insurance_company c ON c.id = o.fk_insurance_company_id
        WHERE bi.fk_settlement_bill_id = #{billId}
    </select>
</mapper>