package com.get.financecenter.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.vo.FCommentVo;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.InsurancePaymentFormVo;
import com.get.financecenter.vo.PaymentFormVo;
import com.get.financecenter.entity.PaymentForm;
import com.get.financecenter.dto.*;
import com.get.salecenter.vo.SelItem;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/12/23
 * @TIME: 14:40
 * @Description:
 **/
public interface IPaymentFormService extends BaseService<PaymentForm> {

    /**
     * @return java.util.List<com.get.financecenter.vo.PaymentFormDto>
     * @Description: 列表数据
     * @Param [paymentFormItemVo]
     * <AUTHOR>
     */
    List<PaymentFormVo> datas(PaymentFormDto paymentFormDto, Page page);

    LambdaQueryWrapper<PaymentForm> getWrapper(PaymentFormDto paymentFormDto);

    List<PaymentFormVo>  packageData(List<PaymentFormVo> collect, PaymentFormDto paymentFormDto, String local);

    /**
     * 批量更新
     * @param paymentForms
     */
    void batchUpdate(List<PaymentForm> paymentForms);
    /**
     * 导出付款大单列表
     *
     * @param paymentFormDto
     */
    void exportPaymentFormExcel(PaymentFormDto paymentFormDto);


    Map<Long,PaymentForm> getAgentIds(Set<Long> ids);

    /**
     * @return java.lang.Long
     * @Description: 新增多个应收计划
     * @Param [paymentFormVo]
     * <AUTHOR>
     */
    Long add(PaymentFormDto paymentFormDto);

    /**
     * @return com.get.financecenter.vo.PaymentFormDto
     * @Description: 修改
     * @Param [paymentFormItemVo]
     * <AUTHOR>
     */
    PaymentFormVo update(PaymentFormDto paymentFormDto);

    /**
     * @return com.get.financecenter.vo.PaymentFormDto
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    PaymentFormVo findPaymentFormById(Long id);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * 作废
     *
     * @param ids
     */
    void updateStatus(Set<Long> ids);

    /**
     * @return java.util.List<com.get.financecenter.vo.MediaAndAttachedDto>
     * @Description: 添加附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<FMediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * @return java.util.List<com.get.financecenter.vo.MediaAndAttachedDto>
     * @Description: 获取附件
     * @Param [attachedVo, page]
     * <AUTHOR>
     */
    List<FMediaAndAttachedVo> getMedia(MediaAndAttachedDto attachedVo, Page page);

    /**
     * @return java.lang.String
     * @Description: 根据付款单id获取货币类型
     * @Param [formId]
     * <AUTHOR>
     */
    String getCurrencyByFormId(Long formId);


    /**
     * @return com.get.salecenter.vo.SelItem
     * @Description: 根据付款单id获取货币类型
     * @Param [formIds]
     * <AUTHOR>
     */
    List<SelItem> getCurrencyByFormIds(Set<Long> formIds);

    /**
     * @return java.math.BigDecimal
     * @Description: 根据付款单id获取付款
     * @Param [formId]
     * <AUTHOR>
     */
    BigDecimal getAmountByFormId(Long formId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据vo获取付款单ids
     * @Param [fkCompanyId]
     * <AUTHOR>
     */
    List<Long> getFormByCompanyId(Long fkCompanyId);

    /**
     * @return java.lang.Long
     * @Description: 增加或者更新评论
     * @Param [commentVo]
     * <AUTHOR>
     **/
    Long editComment(CommentDto commentDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.CommentVo>
     * @Description: 获取所有评论
     * @Param [commentVo, page]
     * <AUTHOR>
     */
    List<FCommentVo> getComments(CommentDto commentDto, Page page);

    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> findTypeKeySelect();

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description:
     * @Param [tableName, companyId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> findTypeTargetSelect(String tableName, Long companyId);

    /**
     * 根据名称获取对象下拉
     * @param tableName
     * @param targetName
     * @return
     */
    List<BaseSelectEntity> getTargetSelectByName(String tableName, String targetName);

    /**
     * @return java.util.List<com.get.financecenter.vo.PaymentFormDto>
     * @Description: 获取应付列表
     * @Param [payFormId]
     * <AUTHOR>
     */
    List<PaymentFormVo> getPayFormList(Long planId);

    /**
     * feign根据应付计划ids获取所绑定的付款单子项
     *
     * @Date 16:52 2021/12/2
     * <AUTHOR>
     */
    List<PaymentFormVo> getPayFormListFeignByPlanIds(Set<Long> planIds);




    /**
     * 修改付款单代理
     *
     * @Date 18:09 2022/12/21
     * <AUTHOR>
     */
    void updatePaymentFormAgent(PaymentFormAgentUpdateDto paymentFormAgentUpdateDto);


    /**
     * 获取付款单
     * @param paymentFormId
     * @return
     */
    PaymentForm getPayFormById(Long paymentFormId);

    void updatePaymentFormDate(PaymentFormDateDto paymentFormDateDto);

    /**
     * 留学服务费，批量创建付款单，涉及表：
     * m_payment_form、m_payment_form_item
     *
     * @param serviceFeePaymentFormDto
     */
    Boolean saveBatchPaymentForms(ServiceFeePaymentFormDto serviceFeePaymentFormDto);

    /**
     * 澳小保创建付款单
     *
     * @param insurancePaymentFormDto
     * @return
     */
    List<InsurancePaymentFormVo> createInsurancePaymentForm(InsurancePaymentFormDto insurancePaymentFormDto);

}
