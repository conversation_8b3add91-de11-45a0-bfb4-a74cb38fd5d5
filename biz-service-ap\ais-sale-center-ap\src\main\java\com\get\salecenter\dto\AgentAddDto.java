 package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AgentAddDto extends BaseVoEntity {

    @Valid
    private AgentDto agentVo;

    @Valid
    private MediaAndAttachedDto mediaAndAttachedVo;

    private List<AgentIdCardDto> agentIdCardVos;

    @NotEmpty(message = "联系人不能为空",groups = {BaseVoEntity.Add.class})
    @Valid
    private List<AgentContactPersonDto> agentContactPersonVos;

    @ApiModelProperty("分配代理项目成员")
    private List<AgentRoleStaffAddDto> agentRoleStaffAddVos;

   

}
