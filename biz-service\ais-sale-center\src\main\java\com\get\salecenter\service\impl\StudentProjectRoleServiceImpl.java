package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.StudentProjectRoleMapper;
import com.get.salecenter.dto.RoleStaffDepartmentDto;
import com.get.salecenter.vo.StudentProjectRoleVo;
import com.get.salecenter.entity.StudentProjectRole;
import com.get.salecenter.service.IDeleteService;
import com.get.salecenter.service.IStudentProjectRoleService;
import com.get.salecenter.dto.StudentProjectRoleDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/11/2 16:16
 * @verison: 1.0
 * @description:
 */
@Service
public class StudentProjectRoleServiceImpl extends GetServiceImpl<StudentProjectRoleMapper,StudentProjectRole> implements IStudentProjectRoleService {
    @Resource
    private StudentProjectRoleMapper studentProjectRoleMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IDeleteService deleteService;

    @Override
    public StudentProjectRoleVo findStudentProjectRoleById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentProjectRole studentProjectRole = studentProjectRoleMapper.selectById(id);
        if (GeneralTool.isEmpty(studentProjectRole)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (!SecureUtil.validateCompany(studentProjectRole.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }

        StudentProjectRoleVo studentProjectRoleVo = BeanCopyUtils.objClone(studentProjectRole, StudentProjectRoleVo::new);

        Result<String> result = permissionCenterClient.getCompanyNameById(studentProjectRole.getFkCompanyId());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            studentProjectRoleVo.setCompanyName(result.getData());
        }

        return studentProjectRoleVo;
    }

    @Override
    public Long addStudentProjectRole(StudentProjectRoleDto studentProjectRoleDto) {
        if (GeneralTool.isEmpty(studentProjectRoleDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        StudentProjectRole studentProjectRole = BeanCopyUtils.objClone(studentProjectRoleDto, StudentProjectRole::new);
        if (validateAdd(studentProjectRoleDto)) {
            Integer maxViewOrder = studentProjectRoleMapper.getMaxViewOrder(studentProjectRoleDto.getFkCompanyId());
            studentProjectRole.setViewOrder(maxViewOrder);
            utilService.updateUserInfoToEntity(studentProjectRole);
            studentProjectRoleMapper.insertSelective(studentProjectRole);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return studentProjectRole.getId();
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        deleteService.deleteValidateRole(id);
        studentProjectRoleMapper.deleteById(id);
    }

    @Override
    public StudentProjectRoleVo updateStudentProjectRole(StudentProjectRoleDto studentProjectRoleDto) {
        if (studentProjectRoleDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        StudentProjectRole result = studentProjectRoleMapper.selectById(studentProjectRoleDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        StudentProjectRole studentProjectRole = BeanCopyUtils.objClone(studentProjectRoleDto, StudentProjectRole::new);
        if (validateUpdate(studentProjectRoleDto)) {
            utilService.updateUserInfoToEntity(studentProjectRole);
            studentProjectRoleMapper.updateById(studentProjectRole);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return findStudentProjectRoleById(studentProjectRole.getId());
    }

    /**
     * 获取存在学习方案角色下的所属部门
     *
     * @Date 10:59 2023/4/6
     * <AUTHOR>
     */
    @Override
    public ListResponseBo<BaseSelectEntity> getRoleStaffDepartmentByRoleId(RoleStaffDepartmentDto roleStaffDepartmentVo) {
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId()).getData();
        staffFollowerIds.add(SecureUtil.getStaffId());
        return new ListResponseBo<>(studentProjectRoleMapper.getRoleStaffDepartmentByRoleId(roleStaffDepartmentVo.getProjectRoleKey(), roleStaffDepartmentVo.getCompanyIds(), staffFollowerIds));
    }

    /**
     * 获取学习方案角色下的员工
     *
     * @param
     * @param
     * @param subordinateFlag
     * @return
     */
    @Override
    public ListResponseBo<BaseSelectEntity> getRoleStaff(String projectRoleKey, String departmentIdStr, Boolean subordinateFlag, String companyIdStr) {
        if (Objects.isNull(projectRoleKey)) {
            return new ListResponseBo<>();
        }
        List<Long> staffFollowerIds = null;
        if (!subordinateFlag) {
            staffFollowerIds = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId()).getData();
            staffFollowerIds.add(SecureUtil.getStaffId());
        }
        return new ListResponseBo<>(studentProjectRoleMapper.getRoleStaffByRoleId(projectRoleKey, departmentIdStr, staffFollowerIds, companyIdStr));
    }

    @Override
    public List<StudentProjectRoleVo> getStudentProjectRoles(StudentProjectRoleDto studentProjectRoleDto, Page page) {
//        Example example = new Example(StudentProjectRole.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<StudentProjectRole> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        //不选所属公司时
        if (GeneralTool.isEmpty(studentProjectRoleDto) || GeneralTool.isEmpty(studentProjectRoleDto.getFkCompanyId())) {
            List<Long> companyIds = getCompanyIds();
            lambdaQueryWrapper.in(StudentProjectRole::getFkCompanyId, companyIds);
        }
        if (GeneralTool.isNotEmpty(studentProjectRoleDto)) {
            //查询条件-角色Id
            if (GeneralTool.isNotEmpty(studentProjectRoleDto.getId())) {
                lambdaQueryWrapper.eq(StudentProjectRole::getId, studentProjectRoleDto.getId());
            }
            //查询条件-所属公司
            if (GeneralTool.isNotEmpty(studentProjectRoleDto.getFkCompanyId())) {
                if (!SecureUtil.validateCompany(studentProjectRoleDto.getFkCompanyId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }

//                criteria.andEqualTo("fkCompanyId", studentProjectRoleDto.getFkCompanyId());
                lambdaQueryWrapper.eq(StudentProjectRole::getFkCompanyId, studentProjectRoleDto.getFkCompanyId());
            }
            //查询条件-角色名称
            if (GeneralTool.isNotEmpty(studentProjectRoleDto.getRoleName())) {
                lambdaQueryWrapper.like(StudentProjectRole::getRoleName, studentProjectRoleDto.getRoleName());
            }
        }
        lambdaQueryWrapper.orderByDesc(StudentProjectRole::getViewOrder);
        IPage<StudentProjectRole> iPage = studentProjectRoleMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<StudentProjectRole> studentProjectRoles = iPage.getRecords();
        page.setAll((int) iPage.getTotal());

        List<StudentProjectRoleVo> convertDatas = new ArrayList<>();

        //公司ids
        Set<Long> companyIds = studentProjectRoles.stream().map(StudentProjectRole::getFkCompanyId).collect(Collectors.toSet());
        //根据公司ids获取名称map
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(companyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }

        for (StudentProjectRole studentProjectRole : studentProjectRoles) {
            StudentProjectRoleVo studentProjectRoleVo = BeanCopyUtils.objClone(studentProjectRole, StudentProjectRoleVo::new);
            //设置所属公司名称
            String companyName = companyNamesByIds.get(studentProjectRole.getFkCompanyId());
            studentProjectRoleVo.setCompanyName(companyName);
            //设置涉及部门名称集合
            setDepartmentNameList(studentProjectRoleVo);
            convertDatas.add(studentProjectRoleVo);
        }
        return convertDatas;
    }

    /**
     * @return void
     * @Description :设置涉及部门名称集合
     * @Param [studentProjectRoleVo]
     * <AUTHOR>
     */
    private void setDepartmentNameList(StudentProjectRoleVo studentProjectRoleVo) {
        String departmentNum = studentProjectRoleVo.getDepartmentNum();
        if (GeneralTool.isNotEmpty(departmentNum)) {
            //拆分
            String[] departmentNumList = departmentNum.split(",", 0);
            List<String> departmentNameList = new ArrayList<>();
            Result<List<String>> result = permissionCenterClient.getDepartmentNameList(departmentNumList);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                departmentNameList = result.getData();
            }
            studentProjectRoleVo.setDepartmentNameList(departmentNameList);
        }
    }

    @Override
    public void movingOrder(List<StudentProjectRoleDto> studentProjectRoleDtos) {
        if (GeneralTool.isEmpty(studentProjectRoleDtos)) {
            throw new GetServiceException(ResultCode.INVALID_PARAM, "传入值为空");
        }
        StudentProjectRole ro = BeanCopyUtils.objClone(studentProjectRoleDtos.get(0), StudentProjectRole::new);
        Integer oneorder = ro.getViewOrder();
        StudentProjectRole rt = BeanCopyUtils.objClone(studentProjectRoleDtos.get(1), StudentProjectRole::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        studentProjectRoleMapper.updateById(ro);
        studentProjectRoleMapper.updateById(rt);
    }

    @Override
    public List<BaseSelectEntity> getRoleSelect(Long companyId) {
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        return studentProjectRoleMapper.getRoleSelect(companyId);
    }

    @Override
    public List<BaseSelectEntity> getRoleCollection() {
        List<StudentProjectRole> studentProjectRoles = studentProjectRoleMapper.getRoleCollection();
        Set<Long> companyIds = studentProjectRoles.stream().map(StudentProjectRole::getFkCompanyId).collect(Collectors.toSet());
        Map<Long, String> companys = new HashMap<>();
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
        if (result.isSuccess() && result.getData() != null) {
            companys = result.getData();
        }
        List<BaseSelectEntity> data = new ArrayList<>();
        List<StudentProjectRole> collect = studentProjectRoles.stream()
                .sorted(Comparator.comparing(StudentProjectRole::getFkCompanyId).thenComparing(StudentProjectRole::getViewOrder, Comparator.reverseOrder())).collect(Collectors.toList());
        for (StudentProjectRole studentProjectRole : collect) {
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append('【')
                    .append(companys.get(studentProjectRole.getFkCompanyId()))
                    .append('】')
                    .append(studentProjectRole.getRoleName())
                    .append('（')
                    .append(studentProjectRole.getRoleKey())
                    .append('）');
            baseSelectEntity.setId(studentProjectRole.getId());
            baseSelectEntity.setName(stringBuffer.toString());
            baseSelectEntity.setNameChn(studentProjectRole.getRoleKey());
            data.add(baseSelectEntity);
        }
        return data;
    }

    @Override
    public List<BaseSelectEntity> getDepartmentRole(Long companyId) {
        //获取当前登录人部门Id
        Long fkDepartmentId = SecureUtil.getFkDepartmentId();
        List<StudentProjectRole> studentProjectRoles = studentProjectRoleMapper.getDepartmentRole(fkDepartmentId, companyId);
        Set<Long> companyIds = studentProjectRoles.stream().map(StudentProjectRole::getFkCompanyId).collect(Collectors.toSet());
        Map<Long, String> companys = new HashMap<>();
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
        if (result.isSuccess() && result.getData() != null) {
            companys = result.getData();
        }
        List<BaseSelectEntity> data = new ArrayList<>();
        List<StudentProjectRole> collect = studentProjectRoles.stream()
                .sorted(Comparator.comparing(StudentProjectRole::getViewOrder).reversed().thenComparing(StudentProjectRole::getFkCompanyId).thenComparing(StudentProjectRole::getRoleName)).collect(Collectors.toList());
        for (StudentProjectRole studentProjectRole : collect) {
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append('【')
                    .append(companys.get(studentProjectRole.getFkCompanyId()))
                    .append('】')
                    .append(studentProjectRole.getRoleName())
                    .append('（')
                    .append(studentProjectRole.getRoleKey())
                    .append('）');
            baseSelectEntity.setId(studentProjectRole.getId());
            baseSelectEntity.setName(stringBuffer.toString());
            baseSelectEntity.setNameChn(studentProjectRole.getRoleKey());
            data.add(baseSelectEntity);
        }
        return data;
    }

    @Override
    public String getRoleNameById(Long roleId) {
        if (GeneralTool.isEmpty(roleId)) {
            return null;
        }
        StudentProjectRole projectRole = studentProjectRoleMapper.selectById(roleId);
        if (GeneralTool.isEmpty(projectRole)) {
            return null;
        }
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append('【')
                .append(permissionCenterClient.getCompanyNameById(projectRole.getFkCompanyId()).getData())
                .append('】')
                .append(projectRole.getRoleName());
        return stringBuffer.toString();
    }

    private boolean validateAdd(StudentProjectRoleDto studentProjectRoleDto) {
//        Example example = new Example(StudentProjectRole.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkCompanyId", studentProjectRoleDto.getFkCompanyId());
//        criteria.andEqualTo("roleName", studentProjectRoleDto.getRoleName());
        List<StudentProjectRole> list = this.studentProjectRoleMapper.selectList(Wrappers.<StudentProjectRole>lambdaQuery()
                .eq(StudentProjectRole::getFkCompanyId, studentProjectRoleDto.getFkCompanyId()).eq(StudentProjectRole::getRoleName, studentProjectRoleDto.getRoleName()));
        if (GeneralTool.isNotEmpty(list)) {
            return false;
        }
        if (GeneralTool.isNotEmpty(studentProjectRoleDto.getRoleKey())) {
//            example.clear();
//            example.createCriteria().andEqualTo("roleKey", studentProjectRoleDto.getRoleKey());
//            List<StudentProjectRole> studentProjectRoleList = this.studentProjectRoleMapper.selectByExample(example);
            List<StudentProjectRole> studentProjectRoleList = this.studentProjectRoleMapper.selectList(Wrappers.<StudentProjectRole>lambdaQuery()
                    .eq(StudentProjectRole::getRoleKey, studentProjectRoleDto.getRoleKey()));
            return !GeneralTool.isNotEmpty(studentProjectRoleList);
        }
        return true;
    }

    private boolean validateUpdate(StudentProjectRoleDto studentProjectRoleDto) {
//        Example example = new Example(StudentProjectRole.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkCompanyId", studentProjectRoleDto.getFkCompanyId());
//        criteria.andEqualTo("roleName", studentProjectRoleDto.getRoleName());
//        criteria.andNotEqualTo("id", studentProjectRoleDto.getId());
//        List<StudentProjectRole> list = this.studentProjectRoleMapper.selectByExample(example);

        List<StudentProjectRole> list = this.studentProjectRoleMapper.selectList(Wrappers.<StudentProjectRole>lambdaQuery()
                .eq(StudentProjectRole::getFkCompanyId, studentProjectRoleDto.getFkCompanyId())
                .eq(StudentProjectRole::getRoleName, studentProjectRoleDto.getRoleName())
                .ne(StudentProjectRole::getId, studentProjectRoleDto.getId()));

        if (GeneralTool.isNotEmpty(list)) {
            return false;
        }
        if (GeneralTool.isNotEmpty(studentProjectRoleDto.getRoleKey())) {
//            example.clear();
//            example.createCriteria().andEqualTo("roleKey", studentProjectRoleDto.getRoleKey()).andNotEqualTo("id", studentProjectRoleDto.getId());
//            List<StudentProjectRole> studentProjectRoleList = this.studentProjectRoleMapper.selectByExample(example);
            List<StudentProjectRole> studentProjectRoleList = this.studentProjectRoleMapper.selectList(Wrappers.<StudentProjectRole>lambdaQuery()
                    .eq(StudentProjectRole::getRoleKey, studentProjectRoleDto.getRoleKey())
                    .ne(StudentProjectRole::getId, studentProjectRoleDto.getId()));
            return !GeneralTool.isNotEmpty(studentProjectRoleList);
        }
        return true;
    }

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :获取登录人对应所有公司id集合
     * @Param []
     * <AUTHOR>
     */
    private List<Long> getCompanyIds() {
        List<Long> companyIds = SecureUtil.getCompanyIdsByStaffId(GetAuthInfo.getStaffId());
        if (GeneralTool.isEmpty(companyIds)) {
            companyIds.add(0L);
        }
        return companyIds;
    }

    /**
     * 获取所有项目角色名名称
     *
     * @Date 14:57 2021/8/2
     * <AUTHOR>
     */
    @Override
    public Map<Long, String> getAllRoleName() {
//        List<StudentProjectRole> studentProjectRoles = studentProjectRoleMapper.selectAll();
        List<StudentProjectRole> studentProjectRoles = studentProjectRoleMapper.selectList(Wrappers.<StudentProjectRole>lambdaQuery());
        Set<Long> fkCompanyIds = studentProjectRoles.stream().map(StudentProjectRole::getFkCompanyId).collect(Collectors.toSet());
        Map<Long, String> companyNameMap = permissionCenterClient.getCompanyNamesByIds(fkCompanyIds).getData();
        Map<Long, String> nameMap = new HashMap<>();
        for (StudentProjectRole studentProjectRole : studentProjectRoles) {
            StringBuffer stringBuffer = new StringBuffer();
            if (GeneralTool.isNotEmpty(fkCompanyIds) && GeneralTool.isNotEmpty(studentProjectRole.getFkCompanyId())) {
                stringBuffer.append('【').append(companyNameMap.get(studentProjectRole.getFkCompanyId())).append('】');
            }
            if (GeneralTool.isNotEmpty(studentProjectRole.getRoleName())) {
                stringBuffer.append(studentProjectRole.getRoleName());
            }
            if (GeneralTool.isNotEmpty(studentProjectRole.getRoleKey())) {
                stringBuffer.append('（').append(studentProjectRole.getRoleKey()).append('）');
            }
            nameMap.put(studentProjectRole.getId(), stringBuffer.toString());
        }
        return nameMap;
    }

    @Override
    public StudentProjectRoleVo getRoleByKey(String roleKey) {
        if (GeneralTool.isEmpty(roleKey)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("role_key_null"));
        }
        StudentProjectRole studentProjectRole = studentProjectRoleMapper.getRoleByKey(roleKey);
        if (GeneralTool.isNotEmpty(studentProjectRole)) {
            StudentProjectRoleVo studentProjectRoleVo = BeanCopyUtils.objClone(studentProjectRole, StudentProjectRoleVo::new);
            return studentProjectRoleVo;
        }
        return null;
    }

    @Override
    public List<StudentProjectRoleVo> getRoleByKeys(Set<String> roleKeys) {
        if (GeneralTool.isEmpty(roleKeys)) {
            return Collections.emptyList();
        }
        return studentProjectRoleMapper.getRoleByKeys(roleKeys);
    }

    @Override
    public Map<Long, String> getRoleNameByIds(Set<Long> roleIds) {
        if (GeneralTool.isEmpty(roleIds)) {
            return new HashMap<>();
        }
        List<StudentProjectRole> studentProjectRoles = studentProjectRoleMapper.selectBatchIds(roleIds);
        if (GeneralTool.isEmpty(studentProjectRoles)) {
            return null;
        }
        Map<Long, String> map = new HashMap<>();
        for (StudentProjectRole studentProjectRole : studentProjectRoles) {
            map.put(studentProjectRole.getId(), studentProjectRole.getRoleName());
        }
        return map;
    }

    /**
     * 获取对应公司下有申请计划的 角色下拉
     *
     * @Date 17:39 2023/1/11
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getExistsOfferItemAgentList(Long companyId) {
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        if (GeneralTool.isEmpty(SecureUtil.getCountryIds())) {
            return null;
        }
        return studentProjectRoleMapper.getExistsOfferItemAgentList(companyId, SecureUtil.getCountryIds());
    }

    @Override
    public List<StudentProjectRole> getStudentProjectRoleListByRoleIds(Set<Long> roleIds) {
      return   studentProjectRoleMapper.selectBatchIds(roleIds);
    }


}
