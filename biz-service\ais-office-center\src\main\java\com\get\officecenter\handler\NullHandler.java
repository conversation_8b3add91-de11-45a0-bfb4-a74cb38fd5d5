package com.get.officecenter.handler;

import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutMessage;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> Wang(https://github.com/binarywang)
 */
@Component
public class NullHandler extends AbstractHandler {
  @Override
  public WxCpXmlOutMessage handle(WxCpTpXmlMessage wxMessage, Map<String, Object> context, WxCpTpService wxCpService,
                                  WxSessionManager sessionManager){
    return null;
  }
}
