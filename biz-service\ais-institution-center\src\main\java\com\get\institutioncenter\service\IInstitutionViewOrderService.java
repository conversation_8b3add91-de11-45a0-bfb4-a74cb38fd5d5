package com.get.institutioncenter.service;

import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.InstitutionVo;
import com.get.institutioncenter.vo.InstitutionViewOrderVo;
import com.get.institutioncenter.entity.InstitutionViewOrder;
import com.get.institutioncenter.dto.InstitutionViewOrderDto;

import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/6/8 17:20
 */
public interface IInstitutionViewOrderService extends BaseService<InstitutionViewOrder> {

    void movingOrder(List<InstitutionViewOrderDto> institutionViewOrderDtos);

    void delete(Long id);

    List<InstitutionViewOrderVo> datas(InstitutionViewOrderDto data, SearchBean<InstitutionViewOrderDto> page);

    void adds(List<InstitutionViewOrderDto> data);

    List<InstitutionVo> searchInstitution(String fkInstitutionName);

    void movingOrderSelect(int end,int start);
}
