package com.get.officecenter.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.GetDateUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.TemplateExcelUtils;
import com.get.officecenter.vo.*;
import com.get.officecenter.vo.LeaveStockVo;
import com.get.officecenter.entity.ClockInData;
import com.get.officecenter.entity.LeaveStock;
import com.get.officecenter.entity.WorkScheduleDateConfig;
import com.get.officecenter.entity.WorkScheduleStaffConfig;
import com.get.officecenter.entity.WorkScheduleTimeConfig;
import com.get.officecenter.mapper.LeaveStockMapper;
import com.get.officecenter.service.ClockInDataService;
import com.get.officecenter.service.ILeaveApplicationFormService;
import com.get.officecenter.service.ILeaveApplicationFormTypeService;
import com.get.officecenter.service.LeaveLogService;
import com.get.officecenter.service.LeaveStockService;
import com.get.officecenter.service.WorkScheduleDateConfigService;
import com.get.officecenter.service.WorkScheduleStaffConfigService;
import com.get.officecenter.service.WorkScheduleTimeConfigService;
import com.get.officecenter.utils.AttendanceUtils;
import com.get.officecenter.dto.AttendanceStatisticsExcelDto;
import com.get.officecenter.dto.LeaveLogDto;
import com.get.officecenter.dto.LeaveStockDto;
import com.get.officecenter.dto.StaffOfLeaveStockDto;
import com.get.officecenter.dto.query.LeaveStockQueryDto;
import com.get.permissioncenter.vo.DepartmentAndStaffVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2022/1/13
 * @TIME: 18:41
 * @Description:
 **/
@Slf4j
@Service
public class LeaveStockServiceImpl extends GetServiceImpl<LeaveStockMapper,LeaveStock> implements LeaveStockService {

    @Resource
    private LeaveStockMapper leaveStockMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ILeaveApplicationFormTypeService leaveApplicationFormTypeService;
    @Resource
    private LeaveLogService leaveLogService;
    @Resource
    private WorkScheduleDateConfigService workScheduleDateConfigService;
    @Resource
    private ILeaveApplicationFormService leaveApplicationFormService;
    @Resource
    private ClockInDataService clockInDataService;
    @Resource
    private WorkScheduleTimeConfigService workScheduleTimeConfigService;
    @Resource
    private WorkScheduleStaffConfigService workScheduleStaffConfigService;

    @Override
    public LeaveStockVo findLeaveStockById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LeaveStock leaveStock = leaveStockMapper.selectById(id);
        if (GeneralTool.isEmpty(leaveStock)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        LeaveStockVo leaveStockVo = BeanCopyUtils.objClone(leaveStock, LeaveStockVo::new);
        //公休类型名称
        String leaveTypeName = leaveApplicationFormTypeService.getLeaveApplicationFormTypeNameByKey(leaveStockVo.getLeaveTypeKey());
        if (GeneralTool.isNotEmpty(leaveTypeName)) {
            leaveStockVo.setLeaveTypeName(leaveTypeName);
        }
        return leaveStockVo;
    }

    @Override
    public void add(LeaveStockDto leaveStockDto) {
        if (GeneralTool.isEmpty(leaveStockDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (Long fkStaffId : leaveStockDto.getFkStaffIds()) {
            LeaveStock leaveStock = BeanCopyUtils.objClone(leaveStockDto, LeaveStock::new);
            leaveStock.setFkStaffId(fkStaffId);
            utilService.updateUserInfoToEntity(leaveStock);
            leaveStockMapper.insert(leaveStock);
            //工休日志
            LeaveLogDto leaveLogDto = new LeaveLogDto();
            leaveLogDto.setFkCompanyId(leaveStockDto.getFkCompanyId());
            leaveLogDto.setFkStaffId(fkStaffId);
            leaveLogDto.setLeaveTypeKey(leaveStockDto.getLeaveTypeKey());
            leaveLogDto.setRemark(leaveStockDto.getRemark());
            leaveLogDto.setOptTypeKey(ProjectKeyEnum.HUMAN_RESOURCES_ADD.key);
            leaveLogDto.setFkLeaveStockId(leaveStock.getId());
            //计算最终时长、记录日志
            leaveLogDto.setDuration(leaveStockDto.getLeaveStock());
            leaveLogService.addLeaveLog(leaveLogDto);
        }
    }


    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (leaveStockMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        leaveStockMapper.deleteById(id);
    }

    @Override
    public LeaveStockVo updateLeaveStock(LeaveStockDto leaveStockDto) {
        if (leaveStockDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        LeaveStock result = leaveStockMapper.selectById(leaveStockDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        LeaveStock leaveStock = BeanCopyUtils.objClone(leaveStockDto, LeaveStock::new);
        utilService.updateUserInfoToEntity(leaveStock);
        leaveStockMapper.updateById(leaveStock);
        return findLeaveStockById(leaveStockDto.getId());
    }

    @Override
    public List<LeaveStockVo> getLeaveStocks(LeaveStockQueryDto leaveStockQueryDto, Page page) {
        Set<Long> fkStaffIds = new HashSet<>();
        //当前登录人ID
        Long staffId = SecureUtil.getStaffId();
        //获取当前登陆人的所有资源key
        List<String> apiKeys = SecureUtil.getApiKeysByStaffId(staffId);
        //默认查看自己的数据
        fkStaffIds.add(staffId);
        //查看所属部门数据
        if (apiKeys.contains(ProjectKeyEnum.LEAVE_STOCKS_SHOW_DEPARTMENT.key)) {
            Long fkDepartmentId = SecureUtil.getFkDepartmentId();
            //获取当前公司部门的全部员工（不包含已经离职）
            List<StaffVo> staffs = permissionCenterClient.getStaffDtos(leaveStockQueryDto.getFkCompanyId(), fkDepartmentId, null);
            if (GeneralTool.isNotEmpty(staffs)) {
                fkStaffIds = staffs.stream().map(StaffVo::getId).collect(Collectors.toSet());
            }
        }
        //查看全部数据
        if (apiKeys.contains(ProjectKeyEnum.LEAVE_STOCKS_SHOW_ALL.key) || SecureUtil.getStaffInfo().getIsAdmin()) {
            //获取当前公司的全部员工（不包含已经离职）
            List<StaffVo> staffs = permissionCenterClient.getStaffDtoByFkCompanyId(leaveStockQueryDto.getFkCompanyId());
            if (GeneralTool.isNotEmpty(staffs)) {
                fkStaffIds = staffs.stream().map(StaffVo::getId).collect(Collectors.toSet());
            }
        }
        //直属员工
        List<Long> subordinateStaffIds = permissionCenterClient.getAllSubordinateIds(staffId);
        fkStaffIds.addAll(subordinateStaffIds);

        List<Long> fkStaffIdList = new ArrayList<>();
        if (GeneralTool.isNotEmpty(leaveStockQueryDto.getKeyWord())) {
            //模糊查询对应员工Ids
            Result<List<Long>> result = permissionCenterClient.getStaffIdsByNameKeyOrEnNameKey(leaveStockQueryDto.getKeyWord());
            if (GeneralTool.isNotEmpty(result.getData())) {
                fkStaffIdList = result.getData();
            }
        }

        if (GeneralTool.isNotEmpty(fkStaffIdList)) {
            fkStaffIds.retainAll(fkStaffIdList);
        }

        if (GeneralTool.isEmpty(fkStaffIds)) {
            page.setAll(0);
            return Collections.emptyList();
        }

        IPage<LeaveStock> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<LeaveStock> leaveStocks = leaveStockMapper.selectLeaveStock(iPage, leaveStockQueryDto.getFkCompanyId(), leaveStockQueryDto.getLeaveTypeKey(), leaveStockQueryDto.getEffectiveDeadline(), fkStaffIds, leaveStockQueryDto.getId());
        page.setAll((int) iPage.getTotal());
        List<LeaveStockVo> datas = leaveStocks.stream().map(leaveStock -> BeanCopyUtils.objClone(leaveStock, LeaveStockVo::new)).collect(Collectors.toList());
        //公司名称
        Set<Long> fkCompanyIds = datas.stream().map(LeaveStockVo::getFkCompanyId).collect(Collectors.toSet());
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkCompanyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkCompanyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }
        //员工名称
        Set<Long> staffIds = datas.stream().map(LeaveStockVo::getFkStaffId).collect(Collectors.toSet());
        Map<Long, String> staffNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(staffIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getStaffNameMap(staffIds);
            if (result.isSuccess() && result.getData() != null) {
                staffNamesByIds = result.getData();
            }
        }
        for (LeaveStockVo leaveStockVo : datas) {
            //公司名
            leaveStockVo.setFkCompanyName(companyNamesByIds.get(leaveStockVo.getFkCompanyId()));
            //员工名
            leaveStockVo.setFkStaffName(staffNamesByIds.get(leaveStockVo.getFkStaffId()));
            //公休类型名称
            String leaveTypeName = leaveApplicationFormTypeService.getLeaveApplicationFormTypeNameByKey(leaveStockVo.getLeaveTypeKey());
            if (GeneralTool.isNotEmpty(leaveTypeName)) {
                leaveStockVo.setLeaveTypeName(leaveTypeName);
            }
        }
        return datas;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LeaveStockVo adjustLeavetock(LeaveStockDto leaveStockDto) {
        if (leaveStockDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        LeaveStock result = leaveStockMapper.selectById(leaveStockDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //调整时长
        if (GeneralTool.isNotEmpty(leaveStockDto.getStatus()) && GeneralTool.isNotEmpty(leaveStockDto.getChangeLeaveStock())) {
            //当前时长
            BigDecimal stock = result.getLeaveStock();
            //扣减时库存不能出现负值
            if (leaveStockDto.getStatus().equals(1) && stock.compareTo(leaveStockDto.getChangeLeaveStock()) < 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("stock_cannot_be_negative"));
            }
            //工休日志
            LeaveLogDto leaveLogDto = new LeaveLogDto();
            leaveLogDto.setFkCompanyId(leaveStockDto.getFkCompanyId());
            leaveLogDto.setFkStaffId(leaveStockDto.getFkStaffId());
            leaveLogDto.setLeaveTypeKey(leaveStockDto.getLeaveTypeKey());
            leaveLogDto.setRemark(leaveStockDto.getRemark());
            leaveLogDto.setOptTypeKey(ProjectKeyEnum.HUMAN_RESOURCES_UPDATE.key);
            //计算最终时长、记录日志
            BigDecimal leaveLogstock = new BigDecimal(0);
            if (leaveStockDto.getStatus().equals(0)) {
                leaveLogstock = stock.add(leaveStockDto.getChangeLeaveStock());
                leaveLogDto.setDuration(leaveStockDto.getChangeLeaveStock());
            }
            if (leaveStockDto.getStatus().equals(1)) {
                leaveLogstock = stock.subtract(leaveStockDto.getChangeLeaveStock());
                leaveLogDto.setDuration(new BigDecimal(0).subtract(leaveStockDto.getChangeLeaveStock()));
            }
            LeaveStock leaveStock = BeanCopyUtils.objClone(leaveStockDto, LeaveStock::new);
            leaveStock.setLeaveStock(leaveLogstock);
            utilService.updateUserInfoToEntity(leaveStock);
            leaveStockMapper.updateById(leaveStock);
            leaveLogService.addLeaveLog(leaveLogDto);
        }
        return findLeaveStockById(leaveStockDto.getId());
    }

    @Override
    public List<BaseSelectEntity> getStaffSelect(Long fkCompanyId) {
        if (GeneralTool.isEmpty(fkCompanyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//       return permissionCenterClient.getStaffByCompanyId(fkCompanyId);
        return permissionCenterClient.getStaffByCompanyId(fkCompanyId);
    }

    @Override
    public LeaveStockVo getLeaveStockByStaffId(Long fkStaffId, String leaveTypeKey){
        return leaveStockMapper.getLeaveStockByStaffId(fkStaffId,leaveTypeKey);
    }

    @Override
    public List<IaeAttendanceStatisticsExcelVo> iaeAttendanceStatistics(AttendanceStatisticsExcelDto attendanceStatisticsExcelDto){
        if (GeneralTool.isEmpty(attendanceStatisticsExcelDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Long fkCompanyId = attendanceStatisticsExcelDto.getFkCompanyId();

        //当前登录人ID
        Long staffId = SecureUtil.getStaffId();
        //获取当前登陆人的所有资源key
        List<String> apiKeys = SecureUtil.getApiKeysByStaffId(staffId);
        Set<Long> fkStaffIds = new HashSet<>();
        //默认查看自己的数据
        fkStaffIds.add(staffId);
        //查看所属部门数据
        if (apiKeys.contains(ProjectKeyEnum.ATTENDANCE_STATISTICS_SHOW_DEPARTMENT.key)){
            Long fkDepartmentId = SecureUtil.getFkDepartmentId();
            //获取当前公司部门的全部员工（不包含已经离职）
            List<StaffVo> staffs = permissionCenterClient.getStaffDtos(fkCompanyId,fkDepartmentId,null);
            if (GeneralTool.isNotEmpty(staffs)) {
                fkStaffIds = staffs.stream().map(StaffVo::getId).collect(Collectors.toSet());
            }
        }
        //包含对应apiKey或则为管理员可查看全部数据
        if(apiKeys.contains(ProjectKeyEnum.ATTENDANCE_STATISTICS_SHOW_ALL.key) || SecureUtil.getStaffInfo().getIsAdmin()) {
            //获取当前公司的全部员工（不包含已经离职）
            List<StaffVo> staffs = permissionCenterClient.getStaffDtoByFkCompanyId(fkCompanyId);
            if (GeneralTool.isNotEmpty(staffs)) {
                fkStaffIds = staffs.stream().map(StaffVo::getId).collect(Collectors.toSet());
            }
        }
        //直属员工
        List<Long> subordinateStaffIds = permissionCenterClient.getAllSubordinateIds(staffId);
        fkStaffIds.addAll(subordinateStaffIds);

        Date date = attendanceStatisticsExcelDto.getDate();
        List<IaeAttendanceStatisticsExcelVo> iaeAttendanceStatisticsExcelVos = new ArrayList<>();

        //根据员工IDS获取部门
        List<DepartmentAndStaffVo> list= permissionCenterClient.getDepartmentAndStaffDtoByStaffIds(fkStaffIds).getData();
        Map<Long, Long> departmentIdMap = list.stream().collect(Collectors.toMap(DepartmentAndStaffVo::getFkStaffId, DepartmentAndStaffVo::getFkDepartmentId));

        //获取考勤时间范围配置
        List<Integer> config= getAttachedRangeTimeConfig(fkCompanyId);
        Date startTime = null;
        Date endTime = null;
        int year = GetDateUtil.getYear(date);
        int month = GetDateUtil.getMonth(date)+1;
        int actualMaximum= GetDateUtil.getActualMaximum(date);
        if(GeneralTool.isEmpty(config)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("attachedRangeTimeConfig_not_set"));
        }
        if(config.size() < 2 && config.get(0).equals(0)){
            //考勤开始时间:指定月份的1号7点30分开始
             startTime = AttendanceUtils.getOpenClockIn(year,month,1);
            //考勤结束时间:指定月份的下一个月1号凌晨4点结束
             endTime = AttendanceUtils.getCloseClockIn(year,month,actualMaximum,actualMaximum);
        }
        if(config.size() > 1){
            //根据配置自定义考勤时间范围
            startTime = AttendanceUtils.getOpenClockIn(year,month-1,config.get(0));
            endTime = AttendanceUtils.getCloseClockIn(year,month,config.get(1),actualMaximum);
        }

        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        log.info("考勤开始时间>>>>>>>>>{}",df.format(startTime));
        log.info("考勤结束时间>>>>>>>>>{}",df.format(endTime));

        //根据考勤范围时间获取对应所有打卡记录
        List<ClockInData> clockInDatas = clockInDataService.getClockInData(startTime,endTime,fkCompanyId);
        Map<Long,List<ClockInData>> clockInDataMap = clockInDatas.stream().collect(Collectors.groupingBy(ClockInData::getFkStaffId));

        //获取排班时间设定
        List<WorkScheduleTimeConfig> timeConfigList = workScheduleTimeConfigService.getWorkScheduleTimeConfigList(fkCompanyId);


        //特殊人员排班时间设定
        Map<Long,List<WorkScheduleStaffConfig>> staffConfigListMap = workScheduleStaffConfigService.getWorkScheduleStaffConfigList(fkCompanyId);


        //获取所有工休记录
        List<LeaveLogVo> leaveLogByfkStaffIds = leaveLogService.getLeaveLogByfkStaffIds(fkStaffIds, fkCompanyId, startTime, endTime);



        //根据时间范围的所有排班
        List<WorkScheduleDateConfig> workScheduleDateConfigList = workScheduleDateConfigService.getWorkScheduleDateConfigByDate(startTime,endTime,fkCompanyId,null);

        //获取剩余年假库存
        List<LeaveStockVo> remainingAnnualVacationList = leaveStockMapper.getLeaveStockByStaffIds(fkStaffIds,endTime,fkCompanyId,"annualVacation");
        Map<Long, BigDecimal> remainingAnnualVacationMap = remainingAnnualVacationList.stream()
                .collect(Collectors.toMap(LeaveStockVo::getFkStaffId, LeaveStockVo::getLeaveStockSum));

        //获取剩余调休库存
        List<LeaveStockVo> remainingTakeDeferredHolidaysList = leaveStockMapper.getLeaveStockByStaffIds(fkStaffIds,endTime,fkCompanyId,"takeDeferredHolidays");
        Map<Long, BigDecimal> remainingTakeDeferredHolidaysMap = remainingTakeDeferredHolidaysList.stream()
                .collect(Collectors.toMap(LeaveStockVo::getFkStaffId, LeaveStockVo::getLeaveStockSum));
        List<StaffVo> staffVoList = permissionCenterClient.getStaffDtoByIds(fkStaffIds);
        for (StaffVo staff : staffVoList) {
            IaeAttendanceStatisticsExcelVo iaeAttendanceStatisticsExcelVo = new IaeAttendanceStatisticsExcelVo();
            String nameEn = "";
            if(GeneralTool.isNotEmpty(staff.getNameEn())){
                nameEn = staff.getNameEn();
            }
            iaeAttendanceStatisticsExcelVo.setStaffName(nameEn);
            String name = "";
            if(GeneralTool.isNotEmpty(staff.getName())){
                name = staff.getName();
            }
            iaeAttendanceStatisticsExcelVo.setStaffNameChn(name);
            iaeAttendanceStatisticsExcelVo.setOfficeName(staff.getOfficeName());
            //应出勤天数
            BigDecimal workDays = getAttendanceDays(startTime,endTime,fkCompanyId,departmentIdMap.get(staff.getId()),timeConfigList,staffConfigListMap.get(staff.getId()),workScheduleDateConfigList);
            iaeAttendanceStatisticsExcelVo.setAttendanceDays(workDays);

            //实际出勤天数
            List<ClockInData> clockInDataList = clockInDataMap.get(staff.getId());
            BigDecimal attendanceDays = new BigDecimal(0);
            if(GeneralTool.isNotEmpty(clockInDataList)){
                attendanceDays = getActualAttendanceDays(startTime,endTime,fkCompanyId,departmentIdMap.get(staff.getId()),clockInDataList,timeConfigList,staffConfigListMap.get(staff.getId()),workScheduleDateConfigList).setScale(0, BigDecimal.ROUND_UP);
            }
            //考勤号为空。默认为全勤
            if(GeneralTool.isEmpty(staff.getAttendanceNum())){
                attendanceDays = workDays;
            }
            iaeAttendanceStatisticsExcelVo.setActualAttendanceDays(attendanceDays);


            //调休天数
            BigDecimal takeDeferredHolidays = new BigDecimal(0);
            Map<Long, BigDecimal> takeDeferredHolidaysMap = getLeaveDuration(leaveLogByfkStaffIds,timeConfigList,staffConfigListMap.get(staff.getId()),workScheduleDateConfigList,departmentIdMap,
                    ProjectKeyEnum.COMPENSATORY_LEAVE_DEDUCTION.key, "takeDeferredHolidays", startTime, endTime);
            if (GeneralTool.isNotEmpty(takeDeferredHolidaysMap.get(staff.getId()))) {
                takeDeferredHolidays = takeDeferredHolidaysMap.get(staff.getId());
            }
            iaeAttendanceStatisticsExcelVo.setTakeDeferredHolidays(takeDeferredHolidays.divide(new BigDecimal(8)));


            //加班天数
            BigDecimal overtime = new BigDecimal(0);
            Map<Long, BigDecimal> overtimeMap = getLeaveDuration(leaveLogByfkStaffIds,timeConfigList,staffConfigListMap.get(staff.getId()),workScheduleDateConfigList,departmentIdMap,
                    ProjectKeyEnum.OVERTIME_TO_INCREASE_COMPENSATORY_TIME_OFF.key,"overtime",startTime,endTime);
            if(GeneralTool.isNotEmpty(overtimeMap.get(staff.getId()))){
                overtime = overtimeMap.get(staff.getId());
            }
            iaeAttendanceStatisticsExcelVo.setOvertime(overtime.divide(new BigDecimal(8)));


            //出差天数
            BigDecimal evection = new BigDecimal(0);
            Map<Long, BigDecimal> evectionMap = getLeaveDuration(leaveLogByfkStaffIds,timeConfigList,staffConfigListMap.get(staff.getId()),workScheduleDateConfigList,departmentIdMap,
                    ProjectKeyEnum.EVECTION_ADD.key,"evection",startTime,endTime);
            if(GeneralTool.isNotEmpty(evectionMap.get(staff.getId()))){
                evection = evectionMap.get(staff.getId());
            }
            iaeAttendanceStatisticsExcelVo.setEvection(evection.divide(new BigDecimal(8)));

            Integer beingLateTimes = 0;
            if(GeneralTool.isNotEmpty(clockInDataList)){
                //当月迟到次数
                beingLateTimes = getBeingLateTimes(startTime, endTime, fkCompanyId, departmentIdMap.get(staff.getId()), timeConfigList, staffConfigListMap.get(staff.getId()), clockInDataList);
            }

            //考勤扣款
            iaeAttendanceStatisticsExcelVo.setAttendanceDeduction(beingLateTimes > 3 ? new BigDecimal((beingLateTimes-3)*50):new BigDecimal(0));

            //额度外迟到次数
            iaeAttendanceStatisticsExcelVo.setOverLateNumber(beingLateTimes > 3 ? beingLateTimes-3:0);

            //事假天数
            BigDecimal leaveVacation = new BigDecimal(0);
            Map<Long, BigDecimal> leaveVacationMap = getLeaveDuration(leaveLogByfkStaffIds,timeConfigList,staffConfigListMap.get(staff.getId()),workScheduleDateConfigList,departmentIdMap,
                    ProjectKeyEnum.UNPAID_LEAVE_DEDUCTION.key,"leaveVacation",startTime,endTime);
            if(GeneralTool.isNotEmpty(leaveVacationMap.get(staff.getId()))){
                leaveVacation = leaveVacationMap.get(staff.getId());
            }
            iaeAttendanceStatisticsExcelVo.setLeaveVacation(leaveVacation.divide(new BigDecimal(8)));

            //病假天数
            BigDecimal diseaseVacation = new BigDecimal(0);
            Map<Long, BigDecimal> diseaseVacationMap = getLeaveDuration(leaveLogByfkStaffIds,timeConfigList,staffConfigListMap.get(staff.getId()),workScheduleDateConfigList,departmentIdMap,
                    ProjectKeyEnum.SICK_LEAVE_DEDUCTION.key,"diseaseVacation",startTime,endTime);
            if(GeneralTool.isNotEmpty(diseaseVacationMap.get(staff.getId()))){
                diseaseVacation = diseaseVacationMap.get(staff.getId());
            }
            iaeAttendanceStatisticsExcelVo.setDiseaseVacation(diseaseVacation.divide(new BigDecimal(8)));

            //年假天数
            BigDecimal annualVacation = new BigDecimal(0);
            Map<Long, BigDecimal> annualVacationMap = getLeaveDuration(leaveLogByfkStaffIds,timeConfigList,staffConfigListMap.get(staff.getId()),workScheduleDateConfigList,departmentIdMap,
                    ProjectKeyEnum.ANNUAL_LEAVE_DEDUCTION.key,"annualVacation",startTime,endTime);
            if(GeneralTool.isNotEmpty(annualVacationMap.get(staff.getId()))){
                annualVacation = annualVacationMap.get(staff.getId());
            }
            iaeAttendanceStatisticsExcelVo.setAnnualLeave(annualVacation.divide(new BigDecimal(8)));

            //婚假天数
            BigDecimal marriageVacation = new BigDecimal(0);
            Map<Long, BigDecimal> marriageVacationMap = getLeaveDuration(leaveLogByfkStaffIds,timeConfigList,staffConfigListMap.get(staff.getId()),workScheduleDateConfigList,departmentIdMap,
                    ProjectKeyEnum.MARRIAGE_VACATION_DEDUCTION.key,"marriageVacation",startTime,endTime);
            if(GeneralTool.isNotEmpty(marriageVacationMap.get(staff.getId()))){
                marriageVacation = marriageVacationMap.get(staff.getId());
            }
            iaeAttendanceStatisticsExcelVo.setMarriageVacation(marriageVacation.divide(new BigDecimal(8)));

            //婚前体检
            BigDecimal premaritalExamination = new BigDecimal(0);
            Map<Long, BigDecimal> premaritalExaminationMap = getLeaveDuration(leaveLogByfkStaffIds,timeConfigList,staffConfigListMap.get(staff.getId()),workScheduleDateConfigList,departmentIdMap,
                    ProjectKeyEnum.PREMARITAL_EXAMINATION_DEDUCTION.key,"premaritalExamination",startTime,endTime);
            if(GeneralTool.isNotEmpty(premaritalExaminationMap.get(staff.getId()))){
                premaritalExamination = premaritalExaminationMap.get(staff.getId());
            }
            iaeAttendanceStatisticsExcelVo.setPremaritalExamination(premaritalExamination.divide(new BigDecimal(8)));

            //产假
            BigDecimal maternityVacation = new BigDecimal(0);
            Map<Long, BigDecimal> maternityVacationMap = getLeaveDuration(leaveLogByfkStaffIds,timeConfigList,staffConfigListMap.get(staff.getId()),workScheduleDateConfigList,departmentIdMap,
                    ProjectKeyEnum.MATERNITY_VACATION_DEDUCTION.key,"maternityVacation",startTime,endTime);
            if(GeneralTool.isNotEmpty(maternityVacationMap.get(staff.getId()))){
                maternityVacation = maternityVacationMap.get(staff.getId());
            }
            iaeAttendanceStatisticsExcelVo.setMaternityVacation(maternityVacation.divide(new BigDecimal(8)));

            //产检假
            BigDecimal maternityCheckVacation = new BigDecimal(0);
            Map<Long, BigDecimal> maternityCheckVacationMap = getLeaveDuration(leaveLogByfkStaffIds,timeConfigList,staffConfigListMap.get(staff.getId()),workScheduleDateConfigList,departmentIdMap,
                    ProjectKeyEnum.MATERNITY_CHECK_VACATION_DEDUCTION.key,"maternityCheckVacation",startTime,endTime);
            if(GeneralTool.isNotEmpty(maternityCheckVacationMap.get(staff.getId()))){
                maternityCheckVacation = maternityCheckVacationMap.get(staff.getId());
            }
            iaeAttendanceStatisticsExcelVo.setMaternityCheckVacation(maternityCheckVacation.divide(new BigDecimal(8)));

            //丧假
            BigDecimal funeralVacation = new BigDecimal(0);
            Map<Long, BigDecimal> funeralVacationMap = getLeaveDuration(leaveLogByfkStaffIds,timeConfigList,staffConfigListMap.get(staff.getId()),workScheduleDateConfigList,departmentIdMap,
                    ProjectKeyEnum.FUNERAL_VACATION_DEDUCTION.key,"funeralVacation",startTime,endTime);
            if(GeneralTool.isNotEmpty(funeralVacationMap.get(staff.getId()))){
                funeralVacation = funeralVacationMap.get(staff.getId());
            }
            iaeAttendanceStatisticsExcelVo.setFuneralVacation(funeralVacation.divide(new BigDecimal(8)));

            //工伤假
            BigDecimal workRelatedVacation = new BigDecimal(0);
            Map<Long, BigDecimal> workRelatedVacationMap = getLeaveDuration(leaveLogByfkStaffIds,timeConfigList,staffConfigListMap.get(staff.getId()),workScheduleDateConfigList,departmentIdMap,
                    ProjectKeyEnum.WORK_RELATED_VACATION_DEDUCTION.key,"workRelatedVacation",startTime,endTime);
            if(GeneralTool.isNotEmpty(workRelatedVacationMap.get(staff.getId()))){
                workRelatedVacation =  workRelatedVacationMap.get(staff.getId());
            }
            iaeAttendanceStatisticsExcelVo.setWorkRelatedVacation(workRelatedVacation.divide(new BigDecimal(8)));
            //剩余补休天数
            BigDecimal remainingTakeDeferredHolidays = new BigDecimal(0);
            if(GeneralTool.isNotEmpty(remainingTakeDeferredHolidaysMap.get(staff.getId()))){
                remainingTakeDeferredHolidays = remainingTakeDeferredHolidaysMap.get(staff.getId());
            }
            iaeAttendanceStatisticsExcelVo.setRemainingTakeDeferredHolidays(remainingTakeDeferredHolidays.divide(new BigDecimal(8)));
            //剩余年假天数
            BigDecimal remainingAnnualLeave = new BigDecimal(0);
            if(GeneralTool.isNotEmpty(remainingAnnualVacationMap.get(staff.getId()))){
                remainingAnnualLeave = remainingAnnualVacationMap.get(staff.getId());
            }
            iaeAttendanceStatisticsExcelVo.setRemainingAnnualLeave(remainingAnnualLeave.divide(new BigDecimal(8)));

            iaeAttendanceStatisticsExcelVos.add(iaeAttendanceStatisticsExcelVo);

        }
        return iaeAttendanceStatisticsExcelVos;
    }


    @Override
    public List<AttendanceStatisticsExcelVo> attendanceStatistics(AttendanceStatisticsExcelDto attendanceStatisticsExcelDto) {
        if (GeneralTool.isEmpty(attendanceStatisticsExcelDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Long fkCompanyId = attendanceStatisticsExcelDto.getFkCompanyId();
        //当前登录人ID
        Long staffId = SecureUtil.getStaffId();
        //获取当前登陆人的所有资源key
        List<String> apiKeys = SecureUtil.getApiKeysByStaffId(staffId);
        Set<Long> fkStaffIds = new HashSet<>();
        //默认查看自己的数据
        fkStaffIds.add(staffId);
        //查看所属部门数据
        if (apiKeys.contains(ProjectKeyEnum.ATTENDANCE_STATISTICS_SHOW_DEPARTMENT.key)){
            Long fkDepartmentId = SecureUtil.getFkDepartmentId();
            //获取当前公司部门的全部员工（不包含已经离职）
            List<StaffVo> staffs = permissionCenterClient.getStaffDtos(fkCompanyId,fkDepartmentId,null);
            if (GeneralTool.isNotEmpty(staffs)) {
                fkStaffIds = staffs.stream().map(StaffVo::getId).collect(Collectors.toSet());
            }
        }
        //查看全部数据
        if(apiKeys.contains(ProjectKeyEnum.ATTENDANCE_STATISTICS_SHOW_ALL.key) || SecureUtil.getStaffInfo().getIsAdmin()) {
            //获取当前公司的全部员工（不包含已经离职）
            List<StaffVo> staffs = permissionCenterClient.getStaffDtoByFkCompanyId(fkCompanyId);
            if (GeneralTool.isNotEmpty(staffs)) {
                fkStaffIds = staffs.stream().map(StaffVo::getId).collect(Collectors.toSet());
            }
        }
        //直属员工
        List<Long> subordinateStaffIds = permissionCenterClient.getAllSubordinateIds(staffId);
        fkStaffIds.addAll(subordinateStaffIds);

        Date date = attendanceStatisticsExcelDto.getDate();
        List<AttendanceStatisticsExcelVo> attendanceStatisticsExcelVos = new ArrayList<>();
        //根据员工IDS获取部门
        List<DepartmentAndStaffVo> list= permissionCenterClient.getDepartmentAndStaffDtoByStaffIds(fkStaffIds).getData();
        Map<Long, Long> departmentIdMap = list.stream().collect(Collectors.toMap(DepartmentAndStaffVo::getFkStaffId, DepartmentAndStaffVo::getFkDepartmentId));

        //获取考勤时间范围配置
        List<Integer> config= getAttachedRangeTimeConfig(fkCompanyId);
        Date startTime = null;
        Date endTime = null;
        int year = GetDateUtil.getYear(date);
        int month = GetDateUtil.getMonth(date)+1;
        int actualMaximum= GetDateUtil.getActualMaximum(date);
        if(GeneralTool.isEmpty(config)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("attachedRangeTimeConfig_not_set"));
        }
        if(config.size() < 2 && config.get(0).equals(0)){
            //考勤开始时间:指定月份的1号7点30分开始
            startTime = AttendanceUtils.getOpenClockIn(year,month,1);
            //考勤结束时间:指定月份的下一个月1号凌晨4点结束
            endTime = AttendanceUtils.getCloseClockIn(year,month,actualMaximum,actualMaximum);
        }
        if(config.size() > 1){
            //根据配置自定义考勤时间范围
            startTime = AttendanceUtils.getOpenClockIn(year,month-1,config.get(0));
            endTime = AttendanceUtils.getCloseClockIn(year,month,config.get(1),actualMaximum);
        }

        log.info("考勤开始时间>>>>>>>>>{}",DateUtil.formatDateTime(startTime));
        log.info("考勤结束时间>>>>>>>>>{}",DateUtil.formatDateTime(endTime));

        //根据考勤范围时间获取对应所有打卡记录
        List<ClockInData> clockInDatas = clockInDataService.getClockInData(startTime,endTime,fkCompanyId);
        Map<Long,List<ClockInData>> clockInDataMap = clockInDatas.stream().collect(Collectors.groupingBy(ClockInData::getFkStaffId));

        //获取时间范围所有工休记录
        List<LeaveLogVo> leaveLogByfkStaffIds = leaveLogService.getLeaveLogByfkStaffIds(fkStaffIds, fkCompanyId, startTime, endTime);

        //获取排班时间设定
        List<WorkScheduleTimeConfig> timeConfigList = workScheduleTimeConfigService.getWorkScheduleTimeConfigList(fkCompanyId);


        //特殊人员排班时间设定
        Map<Long,List<WorkScheduleStaffConfig>> staffConfigListMap = workScheduleStaffConfigService.getWorkScheduleStaffConfigList(fkCompanyId);


        //根据时间范围的所有排班
        List<WorkScheduleDateConfig> workScheduleDateConfigList = workScheduleDateConfigService.getWorkScheduleDateConfigByDate(startTime,endTime,fkCompanyId,null);



        //当前加班累计结余时间（当前考勤范围时间之后有效的）
        List<LeaveStockVo> leaveStockVos = leaveStockMapper.getLeaveStockByStaffIds(fkStaffIds,endTime,fkCompanyId,"takeDeferredHolidays");
        Map<Long, BigDecimal> balanceOvertimeMap = leaveStockVos.stream()
                .collect(Collectors.toMap(LeaveStockVo::getFkStaffId, LeaveStockVo::getLeaveStockSum));
        //获取考勤范围时间之后所有变更补休时长的日志记录合计时长
        Map<Long, BigDecimal> takeDeferredHolidaySumDurationMap = leaveLogService.getLeaveLogsAboutTakeDeferredHoliday(fkStaffIds,fkCompanyId, endTime);



        //当前实时剩余上一年年假
        List<LeaveStockVo> remainingAnnualVacationLastYearList = leaveStockMapper.getAnnualVacationByLastYear(fkStaffIds,date,fkCompanyId,"annualVacation");
        Map<Long, BigDecimal> remainingAnnualVacationLastYearMap = remainingAnnualVacationLastYearList.stream()
                .collect(Collectors.toMap(LeaveStockVo::getFkStaffId, LeaveStockVo::getLeaveStockSum));
        //获取考勤范围时间之后所有变更上一年年假时长的日志记录合计时长
        Map<Long, BigDecimal> annualVacationLastYearSumDurationMap = leaveLogService.getLeaveLogsAboutAnnualVacationLastYear(fkStaffIds,fkCompanyId, endTime,date);


        //当前实时剩余今年年假
        List<LeaveStockVo> remainingAnnualVacationThisYearList = leaveStockMapper.getAnnualVacationByThisYear(fkStaffIds,date,fkCompanyId, "annualVacation");
        Map<Long, BigDecimal> remainingAnnualVacationThisYearMap = remainingAnnualVacationThisYearList.stream()
                .collect(Collectors.toMap(LeaveStockVo::getFkStaffId, LeaveStockVo::getLeaveStockSum));
        //获取考勤范围时间之后所有变更今年年假时长的日志记录合计时长
        Map<Long, BigDecimal> annualVacationThisYearSumDurationMap = leaveLogService.getLeaveLogsAboutAnnualVacationThisYear(fkStaffIds,fkCompanyId, endTime,date);


        //获取考勤时间范围已休上一年年假记录
        List<LeaveLogVo> annualVacationLastYearLeaveLogList =  leaveLogService.getAnnualVacationLastYearLog(fkStaffIds, fkCompanyId, startTime, endTime,date);

        //考勤范围时间已休今年年假记录
        List<LeaveLogVo> annualVacationThisYearLeaveLogList = leaveLogService.getAnnualVacationThisYearLog(fkStaffIds, fkCompanyId, startTime, endTime ,date);

        //单季度病假剩余天数
        List<LeaveStockVo> diseaseVacationQuarterList = leaveStockMapper.getDiseaseVacationQuarter(fkStaffIds,date,fkCompanyId,"diseaseVacation");
        Map<Long, BigDecimal> diseaseVacationQuarterMap = diseaseVacationQuarterList.stream()
                .collect(Collectors.toMap(LeaveStockVo::getFkStaffId, LeaveStockVo::getLeaveStockSum));

        //获取考勤范围时间之后所有变更指定病假库存ids的日志记录合计时长
        Set<Long> fkLeaveStockIds =  diseaseVacationQuarterList.stream().map(LeaveStockVo::getId).collect(Collectors.toSet());
        Map<Long, BigDecimal> diseaseVacationQuarterSumDurationMap = leaveLogService.getLeaveLogsAboutDiseaseVacationQuarter(fkStaffIds,fkLeaveStockIds,fkCompanyId,endTime);

        //考勤范围时间考勤情况
        Map<Long, String> attendanceDetails = leaveApplicationFormService.getLeaveApplicationFormByFkStaffIds(fkStaffIds,startTime,endTime,fkCompanyId,departmentIdMap,timeConfigList,staffConfigListMap,workScheduleDateConfigList);

        List<StaffVo> staffVoList = permissionCenterClient.getStaffDtoByIds(fkStaffIds);
        for (StaffVo staff : staffVoList) {
            AttendanceStatisticsExcelVo attendanceStatisticsExcelVo = new AttendanceStatisticsExcelVo();
            attendanceStatisticsExcelVo.setStaffName(staff.getNameEn());
            attendanceStatisticsExcelVo.setStaffNameChn(staff.getName());
            attendanceStatisticsExcelVo.setDepartmentName(staff.getDepartmentName());
            attendanceStatisticsExcelVo.setEntryDate(GeneralTool.isNotEmpty(staff.getEntryDate()) ? DateUtil.formatDate(staff.getEntryDate()) : null);
            attendanceStatisticsExcelVo.setAttendanceNum(staff.getAttendanceNum());

            //补休
            BigDecimal takeDeferredHolidays = new BigDecimal(0);
            Map<Long, BigDecimal> takeDeferredHolidaysMap = getLeaveDuration(leaveLogByfkStaffIds, timeConfigList,
                    staffConfigListMap.get(staff.getId()), workScheduleDateConfigList, departmentIdMap,
                    ProjectKeyEnum.COMPENSATORY_LEAVE_DEDUCTION.key, ProjectKeyEnum.TAKE_DEFERRED_HOLIDAYS.key, startTime, endTime);
            if (GeneralTool.isNotEmpty(takeDeferredHolidaysMap.get(staff.getId()))) {
                takeDeferredHolidays = takeDeferredHolidaysMap.get(staff.getId());
            }


            //事假
            BigDecimal leaveVacation = new BigDecimal(0);
            Map<Long, BigDecimal> leaveVacationMap = getLeaveDuration(leaveLogByfkStaffIds, timeConfigList,
                    staffConfigListMap.get(staff.getId()), workScheduleDateConfigList, departmentIdMap,
                    ProjectKeyEnum.UNPAID_LEAVE_DEDUCTION.key, ProjectKeyEnum.LEAVE_VACATION.key, startTime, endTime);
            if (GeneralTool.isNotEmpty(leaveVacationMap.get(staff.getId()))) {
                leaveVacation = leaveVacationMap.get(staff.getId());
            }

            //获取打卡记录，计算迟到次数，出勤天数
            List<ClockInData> clockInDataList = clockInDataMap.get(staff.getId());
            Integer beingLateTimes = 0;
            if(GeneralTool.isNotEmpty(clockInDataList)){
                //当月迟到次数
                beingLateTimes = getBeingLateTimes(startTime,endTime,fkCompanyId,departmentIdMap.get(staff.getId()),timeConfigList,staffConfigListMap.get(staff.getId()),clockInDataList);
            }
            //免责迟到
            attendanceStatisticsExcelVo.setExemptionBeingLate(beingLateTimes > 3 ? 3:beingLateTimes);
            //有责迟到
            attendanceStatisticsExcelVo.setResponsibleBeingLate(beingLateTimes > 3 ? beingLateTimes-3 : 0);
            //当月出勤天数
            BigDecimal attendanceDays = new BigDecimal(0);
            if(GeneralTool.isNotEmpty(clockInDataList)){
                attendanceDays = getActualAttendanceDays(startTime,endTime,fkCompanyId,departmentIdMap.get(staff.getId()),
                        clockInDataList,timeConfigList,staffConfigListMap.get(staff.getId()),workScheduleDateConfigList);
            }
            attendanceStatisticsExcelVo.setAttendanceDays(attendanceDays.setScale(0, BigDecimal.ROUND_UP));
            attendanceStatisticsExcelVo.setLeaveVacation(leaveVacation);
            attendanceStatisticsExcelVo.setTakeDeferredHolidays(takeDeferredHolidays);


            //加班累计结余时间
            BigDecimal balanceOvertime = new BigDecimal(0);
            if (GeneralTool.isNotEmpty(balanceOvertimeMap.get(staff.getId()))) {
                if (GeneralTool.isNotEmpty(takeDeferredHolidaySumDurationMap.get(staff.getId()))) {
                    if (takeDeferredHolidaySumDurationMap.get(staff.getId()).compareTo(BigDecimal.ZERO) > 0) {
                        balanceOvertime = balanceOvertimeMap.get(staff.getId()).subtract(takeDeferredHolidaySumDurationMap.get(staff.getId()));
                    }
                    if (takeDeferredHolidaySumDurationMap.get(staff.getId()).compareTo(BigDecimal.ZERO) < 0) {
                        balanceOvertime = balanceOvertimeMap.get(staff.getId()).add(takeDeferredHolidaySumDurationMap.get(staff.getId()).abs());
                    }
                    if (takeDeferredHolidaySumDurationMap.get(staff.getId()).compareTo(BigDecimal.ZERO) == 0) {
                        balanceOvertime = balanceOvertimeMap.get(staff.getId());
                    }

                } else {
                    balanceOvertime = balanceOvertimeMap.get(staff.getId());
                }

            }

            //获取本月加班的时长
            BigDecimal overtimeMonth = new BigDecimal(0);
            Map<Long, BigDecimal> overtimeMonthMap = getLeaveDuration(leaveLogByfkStaffIds,timeConfigList,
                    staffConfigListMap.get(staff.getId()),workScheduleDateConfigList,departmentIdMap,
                    ProjectKeyEnum.OVERTIME_TO_INCREASE_COMPENSATORY_TIME_OFF.key,ProjectKeyEnum.OVERTIEM.key,startTime,endTime);
            if (GeneralTool.isNotEmpty(overtimeMonthMap.get(staff.getId()))) {
                overtimeMonth = overtimeMonthMap.get(staff.getId());
            }


            //加班累计时间（除本月加班外剩余的加班时长加上补休的）
            BigDecimal overtime = balanceOvertime.subtract(overtimeMonth).add(takeDeferredHolidays);
            //加班累计剩余时间
            BigDecimal remainingOvertime = overtime.add(overtimeMonth).subtract(takeDeferredHolidays).subtract(leaveVacation);

            attendanceStatisticsExcelVo.setOvertime(overtime);
            attendanceStatisticsExcelVo.setOvertimeMonth(overtimeMonth);
            attendanceStatisticsExcelVo.setRemainingOvertime(remainingOvertime);
            attendanceStatisticsExcelVo.setBalanceOvertime(balanceOvertime);
            //上一年剩余年假
            BigDecimal remainingAnnualVacationLastYear = new BigDecimal(0);
            if (GeneralTool.isNotEmpty(remainingAnnualVacationLastYearMap.get(staff.getId()))) {
                if (GeneralTool.isNotEmpty(annualVacationLastYearSumDurationMap.get(staff.getId()))) {
                    if (annualVacationLastYearSumDurationMap.get(staff.getId()).compareTo(BigDecimal.ZERO) > 0) {
                        remainingAnnualVacationLastYear = remainingAnnualVacationLastYearMap.get(staff.getId()).subtract(annualVacationLastYearSumDurationMap.get(staff.getId()));
                    }
                    if (annualVacationLastYearSumDurationMap.get(staff.getId()).compareTo(BigDecimal.ZERO) < 0) {
                        remainingAnnualVacationLastYear = remainingAnnualVacationLastYearMap.get(staff.getId()).add(annualVacationLastYearSumDurationMap.get(staff.getId()).abs());
                    }
                    if (annualVacationLastYearSumDurationMap.get(staff.getId()).compareTo(BigDecimal.ZERO) == 0) {
                        remainingAnnualVacationLastYear = remainingAnnualVacationLastYearMap.get(staff.getId());
                    }
                } else {
                    remainingAnnualVacationLastYear = remainingAnnualVacationLastYearMap.get(staff.getId());
                }
            }
            //今年剩余年假
            BigDecimal remainingAnnualVacationThisYear = new BigDecimal(0);
            if (GeneralTool.isNotEmpty(remainingAnnualVacationThisYearMap.get(staff.getId()))) {
                if (GeneralTool.isNotEmpty(annualVacationThisYearSumDurationMap.get(staff.getId()))) {
                    if (annualVacationThisYearSumDurationMap.get(staff.getId()).compareTo(BigDecimal.ZERO) > 0) {
                        remainingAnnualVacationThisYear = remainingAnnualVacationThisYearMap.get(staff.getId()).subtract(annualVacationThisYearSumDurationMap.get(staff.getId()));
                    }
                    if (annualVacationThisYearSumDurationMap.get(staff.getId()).compareTo(BigDecimal.ZERO) < 0) {
                        remainingAnnualVacationThisYear = remainingAnnualVacationThisYearMap.get(staff.getId()).add(annualVacationThisYearSumDurationMap.get(staff.getId()).abs());
                    }
                    if (annualVacationThisYearSumDurationMap.get(staff.getId()).compareTo(BigDecimal.ZERO) == 0) {
                        remainingAnnualVacationThisYear = remainingAnnualVacationThisYearMap.get(staff.getId());
                    }

                } else {
                    remainingAnnualVacationThisYear = remainingAnnualVacationThisYearMap.get(staff.getId());
                }
            }

            //本月已休上一年年假时长
            BigDecimal annualVacationLastYearMonth = new BigDecimal(0);
            Map<Long, BigDecimal> annualVacationLastYearMonthMap = getLeaveDuration(annualVacationLastYearLeaveLogList,timeConfigList,
                    staffConfigListMap.get(staff.getId()),workScheduleDateConfigList,departmentIdMap,
                    ProjectKeyEnum.ANNUAL_LEAVE_DEDUCTION.key,ProjectKeyEnum.ANNUAL_VACATION.key,startTime,endTime);
            if (GeneralTool.isNotEmpty(annualVacationLastYearMonthMap.get(staff.getId()))) {
                annualVacationLastYearMonth = annualVacationLastYearMonthMap.get(staff.getId());
            }


            //本月已休今年年假时长
            BigDecimal annualVacationThisYearMonth = new BigDecimal(0);
            Map<Long, BigDecimal> annualVacationThisYearMonthMap = getLeaveDuration(annualVacationThisYearLeaveLogList,timeConfigList,
                    staffConfigListMap.get(staff.getId()),workScheduleDateConfigList,departmentIdMap,
                    ProjectKeyEnum.ANNUAL_LEAVE_DEDUCTION.key,ProjectKeyEnum.ANNUAL_VACATION.key,startTime,endTime);
            if (GeneralTool.isNotEmpty(annualVacationThisYearMonthMap.get(staff.getId()))) {
                annualVacationThisYearMonth = annualVacationThisYearMonthMap.get(staff.getId());
            }

            //累计上一年年假 = 上一年剩余年假 + 本月已休上一年年假时长
            BigDecimal annualVacationLastYear = remainingAnnualVacationLastYear.add(annualVacationLastYearMonth);
            //累计今年年假 = 今年剩余年假 + 本月已休今年年假时长
            BigDecimal annualVacationThisYear = remainingAnnualVacationThisYear.add(annualVacationThisYearMonth);
            attendanceStatisticsExcelVo.setAnnualLeaveLastYear(annualVacationLastYear);
            attendanceStatisticsExcelVo.setAnnualLeaveThisYear(annualVacationThisYear);
            attendanceStatisticsExcelVo.setRemainingAnnualLeaveLastYear(remainingAnnualVacationLastYear);
            attendanceStatisticsExcelVo.setRemainingAnnualLeaveThisYear(remainingAnnualVacationThisYear);
            attendanceStatisticsExcelVo.setAnnualLeaveLastYearMonth(annualVacationLastYearMonth);
            attendanceStatisticsExcelVo.setAnnualLeaveThisYearMonth(annualVacationThisYearMonth);
            //本季度病假剩余天数
            BigDecimal diseaseVacationQuarter = new BigDecimal(0);
            if (GeneralTool.isNotEmpty(diseaseVacationQuarterMap.get(staff.getId()))) {
                if (GeneralTool.isNotEmpty(diseaseVacationQuarterSumDurationMap.get(staff.getId()))) {
                    if (diseaseVacationQuarterSumDurationMap.get(staff.getId()).compareTo(BigDecimal.ZERO) > 0) {
                        diseaseVacationQuarter = (diseaseVacationQuarterMap.get(staff.getId()).subtract(diseaseVacationQuarterSumDurationMap.get(staff.getId()))).divide(new BigDecimal(8), 2, BigDecimal.ROUND_HALF_UP);
                    }
                    if (diseaseVacationQuarterSumDurationMap.get(staff.getId()).compareTo(BigDecimal.ZERO) < 0) {
                        diseaseVacationQuarter = (diseaseVacationQuarterMap.get(staff.getId()).add(diseaseVacationQuarterSumDurationMap.get(staff.getId()))).divide(new BigDecimal(8), 2, BigDecimal.ROUND_HALF_UP);
                    }
                    if (diseaseVacationQuarterSumDurationMap.get(staff.getId()).compareTo(BigDecimal.ZERO) == 0) {
                        diseaseVacationQuarter = diseaseVacationQuarterMap.get(staff.getId()).divide(new BigDecimal(8), 2, BigDecimal.ROUND_HALF_UP);
                    }
                } else {
                    diseaseVacationQuarter = diseaseVacationQuarterMap.get(staff.getId()).divide(new BigDecimal(8), 2, BigDecimal.ROUND_HALF_UP);
                }
            }
            attendanceStatisticsExcelVo.setDiseaseVacationQuarter(diseaseVacationQuarter);

            //应出勤天数
            BigDecimal workDays = getAttendanceDays(startTime,endTime,fkCompanyId,departmentIdMap.get(staff.getId()),timeConfigList,staffConfigListMap.get(staff.getId()),workScheduleDateConfigList);
            //是否全勤
            if (workDays.compareTo(attendanceDays) == 0) {
                attendanceStatisticsExcelVo.setIsFullAttendance("是");
            } else {
                attendanceStatisticsExcelVo.setIsFullAttendance("否");
            }
            //考勤情况
            if (GeneralTool.isNotEmpty(attendanceDetails) && GeneralTool.isNotEmpty(attendanceDetails.get(staff.getId()))) {
                attendanceStatisticsExcelVo.setAttendanceDetails(attendanceDetails.get(staff.getId()));
            }else {
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append("无");
                attendanceStatisticsExcelVo.setAttendanceDetails(stringBuffer.toString());
            }
            attendanceStatisticsExcelVos.add(attendanceStatisticsExcelVo);
        }
        return attendanceStatisticsExcelVos;
    }

    /**
     * 获取考勤时间范围系统配置
     * @param fkCompanyId
     * @return
     */
    public List<Integer> getAttachedRangeTimeConfig(Long fkCompanyId) {
        if (GeneralTool.isEmpty(fkCompanyId)){
            fkCompanyId = SecureUtil.getFkCompanyId();
        }
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.ATTENDANCE_RANGE_TIME.key, 1).getData();
        String configValue1 = companyConfigMap.get(fkCompanyId);
        if (GeneralTool.isEmpty(configValue1)) {
            return null;
        }
        JSONArray jsonArray = JSON.parseArray(configValue1);
        return jsonArray.toJavaList(Integer.class);

//        ConfigVo data = permissionCenterClient.getConfigByKey(ProjectKeyEnum.ATTENDANCE_RANGE_TIME.key).getData();
//        if (GeneralTool.isNotEmpty(data) && GeneralTool.isNotEmpty(data.getValue1())){
//            JSONObject jsonObject = JSONObject.parseObject(data.getValue1());
//            JSONArray otherConfigs = jsonObject.getJSONArray("OTHER");
//            JSONArray iaeConfigs = jsonObject.getJSONArray("IAE");
//            List<Integer> otherConfigList = otherConfigs.toJavaList(Integer.class);
//            List<Integer> iaeConfigList = iaeConfigs.toJavaList(Integer.class);
//            if (fkCompanyId.equals(3L)){
//                return iaeConfigList;
//            }else{
//                return otherConfigList;
//            }
//        }
//        return null;
    }


    /**
     * 根据类型获取对应工休时长
     * @param logDtos 所有工休记录
     * @param optTypeKey 操作类型关键字
     * @param formTypeKey 工休类型关键字
     * @param startTime 考勤开始时间
     * @param endTime 考勤结束时间
     * @return
     */
    public Map<Long,BigDecimal> getLeaveDuration(List<LeaveLogVo> logDtos,
                                                 List<WorkScheduleTimeConfig> workScheduleTimeConfigList,
                                                 List<WorkScheduleStaffConfig> workScheduleStaffConfigList,
                                                 List<WorkScheduleDateConfig> workScheduleDateConfigList,
                                                 Map<Long,Long> departmentIdMap,
                                                 String optTypeKey,
                                                 String formTypeKey,
                                                 Date startTime,
                                                 Date endTime){
        List<LeaveLogVo> dataList= logDtos.stream()
                .filter(l->optTypeKey.equals(l.getOptTypeKey()) && formTypeKey.equals(l.getLeaveTypeKey()))
                .collect(Collectors.toList());

        for (LeaveLogVo data : dataList) {

            if (data.getStartTime().compareTo(startTime) >= 0 && data.getEndTime().compareTo(endTime) <= 0) {
                data.setDurationSum(data.getDuration());
                continue;
            }

            //类型为加班,零点后加班算到新的一天
            if (formTypeKey.equals(ProjectKeyEnum.OVERTIEM.key)) {
                Date overStartTime = GetDateUtil.setTime(startTime, 0, 0, 0);
                if (data.getStartTime().compareTo(overStartTime) < 0 && data.getEndTime().compareTo(overStartTime) > 0) {
                    //重新设置申请开始时间
                    data.setStartTime(GetDateUtil.setTime(startTime, 0, 0, 0));
                    data.setDurationSum(new BigDecimal(DateUtil.between(data.getStartTime(), data.getEndTime(), DateUnit.HOUR)));
                    continue;
                }

                Date overEndTime = GetDateUtil.setTime(endTime, 23, 59, 59);
                if (data.getStartTime().compareTo(overEndTime) <= 0 && data.getEndTime().compareTo(overEndTime) > 0) {
                    //重新设置申请结束时间
                    data.setEndTime(GetDateUtil.setTime(endTime, 23, 59, 59));
                    data.setDurationSum(new BigDecimal(DateUtil.between(data.getStartTime(), data.getEndTime(), DateUnit.HOUR)));
                    continue;
                }

                data.setDurationSum(data.getDuration());
                continue;
            }

            WorkScheduleTimeConfig timeConfig = AttendanceUtils.getTimeConfig(workScheduleTimeConfigList, data.getFkCompanyId(), departmentIdMap.get(data.getFkStaffId()));
            //对工休时间范围部分不在考勤范围的处理
            if (data.getStartTime().compareTo(startTime) < 0 && data.getEndTime().compareTo(startTime) >= 0) {
                WorkScheduleStaffConfig staffConfig = AttendanceUtils.getWorkScheduleStaffConfig(startTime, formTypeKey, workScheduleStaffConfigList);
                //重新设定工休申请开始时间
                List<Integer> workingStart = AttendanceUtils.getWorkScheduleTimeConfig(timeConfig, staffConfig,1);
                data.setStartTime(GetDateUtil.setTime(startTime, workingStart.get(0), workingStart.get(1), workingStart.get(2)));
            }

            if (data.getStartTime().compareTo(endTime) < 0 && data.getEndTime().compareTo(endTime) > 0) {
                Date newEndDate = GetDateUtil.getYesterdayDate(endTime);
                WorkScheduleStaffConfig staffConfig = AttendanceUtils.getWorkScheduleStaffConfig(newEndDate, formTypeKey, workScheduleStaffConfigList);
                //重新设定工休申请结束时间
                List<Integer> workingEnd = AttendanceUtils.getWorkScheduleTimeConfig(timeConfig, staffConfig, 2);
                data.setEndTime(GetDateUtil.setTime(newEndDate, workingEnd.get(0), workingEnd.get(1), workingEnd.get(2)));
            }

            //重新计算工休申请时长
            BigDecimal durationSum = AttendanceUtils.getDuration(workScheduleTimeConfigList, workScheduleStaffConfigList, workScheduleDateConfigList,
                    data.getFkCompanyId(), departmentIdMap.get(data.getFkStaffId()), formTypeKey, data.getStartTime(), data.getEndTime(), 1);

            data.setDurationSum(durationSum);

        }

        Map<Long, BigDecimal> map = new HashMap<>();
        for (LeaveLogVo data : dataList) {
            if (map.get(data.getFkStaffId()) != null) {
                map.put(data.getFkStaffId(), map.get(data.getFkStaffId()).add(data.getDurationSum()));
            } else {
                map.put(data.getFkStaffId(), data.getDurationSum());
            }
        }

        return map;
    }

    /**
     * 计算应该出勤天数
     * <AUTHOR>
     * @DateTime 2023/2/2 15:04
     */
    public BigDecimal getAttendanceDays(Date startTime,
                                        Date endTime,
                                        Long fkCompanyId,
                                        Long fkDepartmentId,
                                        List<WorkScheduleTimeConfig> workScheduleTimeConfigList,
                                        List<WorkScheduleStaffConfig> workScheduleStaffConfigList,
                                        List<WorkScheduleDateConfig> workScheduleDateConfigList) {
        //排班时间设定
        WorkScheduleTimeConfig timeConfig = AttendanceUtils.getTimeConfig(workScheduleTimeConfigList, fkCompanyId, fkDepartmentId);
        BigDecimal attendanceDays = new BigDecimal(0);
        while (startTime.compareTo(endTime) < 0) {
            //特殊人员排班设定
            WorkScheduleStaffConfig workScheduleStaffConfig = AttendanceUtils.getWorkScheduleStaffConfig(startTime,null,workScheduleStaffConfigList);

            //非工作日
            workScheduleDateConfigList = workScheduleDateConfigList.stream().filter(c -> GeneralTool.isEmpty(c.getFkDepartmentId()) || c.getFkDepartmentId().equals(fkDepartmentId)).collect(Collectors.toList());
            Boolean ignoreDate= AttendanceUtils.dayOff(startTime,workScheduleDateConfigList,workScheduleStaffConfig);
            if(ignoreDate){
                //第二天
                startTime = GetDateUtil.getTomorrowDate(startTime);
                continue;
            }
            attendanceDays = attendanceDays.add(new BigDecimal(1));
            //第二天
            startTime = GetDateUtil.getTomorrowDate(startTime);
        }
       return attendanceDays;

    }



    /**
     * 计算实际出勤天数
     * @param startTime
     * @param endTime
     * @param clockInDataList
     * @return
     */
    public BigDecimal getActualAttendanceDays(
                                        Date startTime,
                                        Date endTime,
                                        Long fkCompanyId,
                                        Long fkDepartmentId,
                                        List<ClockInData> clockInDataList,
                                        List<WorkScheduleTimeConfig> workScheduleTimeConfigList,
                                        List<WorkScheduleStaffConfig> workScheduleStaffConfigList,
                                        List<WorkScheduleDateConfig> workScheduleDateConfigList){
        //时间设定配置
        WorkScheduleTimeConfig workScheduleTimeConfig = AttendanceUtils.getTimeConfig(workScheduleTimeConfigList, fkCompanyId, fkDepartmentId);
        BigDecimal attendanceDays = new BigDecimal(0);
        while(startTime.compareTo(endTime) < 0){
            //特殊人员时间配置
            WorkScheduleStaffConfig workScheduleStaffConfig = AttendanceUtils.getWorkScheduleStaffConfig(startTime,null,workScheduleStaffConfigList);

            //非工作日
            Boolean ignoreDate = AttendanceUtils.dayOff(startTime, workScheduleDateConfigList,workScheduleStaffConfig);
            if(ignoreDate){
                //第二天
                startTime = GetDateUtil.getTomorrowDate(startTime);
                continue;
            }
            //打卡生效开始时间
            Date start = AttendanceUtils.getOpenClockIn(GetDateUtil.getYear(startTime),
                    GetDateUtil.getMonth(startTime)+1,GetDateUtil.getDay(startTime));
            //打卡生效结束时间
            Date end = AttendanceUtils.getCloseClockIn(GetDateUtil.getYear(startTime),
                    GetDateUtil.getMonth(startTime)+1,GetDateUtil.getDay(startTime),GetDateUtil.getActualMaximum(startTime));



            //上班时间
            List<Integer> workingStart = AttendanceUtils.getWorkScheduleTimeConfig(workScheduleTimeConfig,workScheduleStaffConfig,1);
            Date onDutyTime = GetDateUtil.setTime(start, workingStart.get(0), workingStart.get(1), workingStart.get(2));

            //下班时间
            List<Integer> workingEnd = AttendanceUtils.getWorkScheduleTimeConfig(workScheduleTimeConfig,workScheduleStaffConfig,2);
            Date offDutyTime = GetDateUtil.setTime(start, workingEnd.get(0), workingEnd.get(1), workingEnd.get(2));

            //午休开始时间配置
            List<Integer> noonBreakStart = AttendanceUtils.getWorkScheduleTimeConfig(workScheduleTimeConfig,workScheduleStaffConfig,3);

            //午休结束时间配置
            List<Integer> noonBreakEnd = AttendanceUtils.getWorkScheduleTimeConfig(workScheduleTimeConfig,workScheduleStaffConfig,4);

            //当前日期应该工作时长
            BigDecimal workingDuration = GeneralTool.isNotEmpty(workScheduleStaffConfig) ? workScheduleStaffConfig.getWorkingDuration() : workScheduleTimeConfig.getWorkingDuration();

            List<Date> onDutyClockIns = new ArrayList<>();
            List<Date> offDutyClockIns = new ArrayList<>();
            //上班后两个小时
            Date onDutyStart = GetDateUtil.getAdvanceDateByHour(onDutyTime,-2L);
            //下班前两个小时
            Date offDutyEnd = GetDateUtil.getAdvanceDateByHour(offDutyTime,2L);

            for(ClockInData clockInData:clockInDataList){
                //上班卡
                if(start.compareTo(clockInData.getClockInTime())<= 0 && onDutyStart.compareTo(clockInData.getClockInTime())>=0 ){
                    onDutyClockIns.add(clockInData.getClockInTime());
                }
                //下班卡
                if(offDutyEnd.compareTo(clockInData.getClockInTime())<0 && end.compareTo(clockInData.getClockInTime())>=0){
                    offDutyClockIns.add(clockInData.getClockInTime());
                }
            }

            //同时存在上班下班卡
            if(GeneralTool.isNotEmpty(onDutyClockIns) && GeneralTool.isNotEmpty(offDutyClockIns)){
                //上班打卡
                Date timeHead = onDutyClockIns.stream().min(Date::compareTo).get();
                //下班打卡
                Date timeTail = offDutyClockIns.stream().min(Date::compareTo).get();

                //区分上班打卡是否迟到情况,对应赋值开始打卡时间
                Date startClockIn = timeHead.compareTo(onDutyTime) <= 0 ? onDutyTime : timeHead;

                //区分下班打卡是否早退情况,对应赋值结束打卡时间
                Date endClockIn = timeTail.compareTo(offDutyTime) < 0 ? timeTail : offDutyTime;


                //计算出勤时长：两次打卡时间差-午休时长
                BigDecimal attendance = new BigDecimal(0);
                if(GeneralTool.isNotEmpty(noonBreakStart) && GeneralTool.isNotEmpty(noonBreakEnd)){
                    Integer noonBreakTime = ((noonBreakEnd.get(0)*60 + noonBreakEnd.get(1)) -(noonBreakStart.get(0)*60 +noonBreakStart.get(1)))/60;
                    Long hours = (endClockIn.getTime()-startClockIn.getTime())/ (1000 * 60 * 60) - noonBreakTime;
                    attendance = new BigDecimal(hours);

                }
                BigDecimal dailyAttendance = attendance.divide(workingDuration,2,BigDecimal.ROUND_HALF_UP);
                attendanceDays = attendanceDays.add(dailyAttendance);
            }
            startTime = GetDateUtil.getTomorrowDate(startTime);
        }
        return attendanceDays;
    }

    /**
     * 计算迟到次数
     * @param startTime 考勤开始时间
     * @param endTime 考勤结束时间
     * @param fkDepartmentId 部门
     * @param timeConfigList 排班时间设定
     * @param workScheduleStaffConfigList 特殊人员排班时间设定
     * @param clockInDataList 打卡记录
     * @return
     */
    public Integer getBeingLateTimes(Date startTime,
                                     Date endTime,
                                     Long fkCompanyId,
                                     Long fkDepartmentId,
                                     List<WorkScheduleTimeConfig> timeConfigList,
                                     List<WorkScheduleStaffConfig> workScheduleStaffConfigList,
                                     List<ClockInData> clockInDataList){
        Integer beingLateTimes = 0;
        WorkScheduleTimeConfig timeConfig = AttendanceUtils.getTimeConfig(timeConfigList, fkCompanyId, fkDepartmentId);
        while(startTime.compareTo(endTime) < 0){
            //打卡生效开始时间
            Date openClockIn = AttendanceUtils.getOpenClockIn(GetDateUtil.getYear(startTime), GetDateUtil.getMonth(startTime)+1,GetDateUtil.getDay(startTime));
            //特殊人员时间配置
            WorkScheduleStaffConfig workScheduleStaffConfig = AttendanceUtils.getWorkScheduleStaffConfig(startTime,null,workScheduleStaffConfigList);
            //上班时间
            List<Integer> workingStart = AttendanceUtils.getWorkScheduleTimeConfig(timeConfig,workScheduleStaffConfig,1);
            Date workingStartTime = GetDateUtil.setTime(openClockIn,workingStart.get(0),workingStart.get(1),workingStart.get(2));
            //上班后两个小时
            Date afterWorkingStartTime = GetDateUtil.getAdvanceDateByHour(workingStartTime,-2);
            //获取符合当天打卡范围的上班打卡记录
            List<ClockInData> onDutyClockIns = new ArrayList<>();
            for(ClockInData clockInData:clockInDataList){
                if(openClockIn.compareTo(clockInData.getClockInTime()) <= 0 && afterWorkingStartTime.compareTo(clockInData.getClockInTime()) >= 0 ){
                    onDutyClockIns.add(clockInData);
                }
            }
            //迟到判定
            ClockInData clockInData = onDutyClockIns.stream().min(Comparator.comparing(c->c.getClockInTime())).orElse(null);
            if(GeneralTool.isNotEmpty(clockInData) && clockInData.getClockInTime().compareTo(workingStartTime) > 0){
                beingLateTimes++;
            }
            //第二天
            startTime = GetDateUtil.getTomorrowDate(startTime);
        }
        return beingLateTimes;
    }


    @Override
    public void exportAttendanceStatisticsExcel(HttpServletResponse response, AttendanceStatisticsExcelDto attendanceStatisticsExcelDto) throws Exception {
        List<AttendanceStatisticsExcelVo> attendanceStatisticsExcelVos = attendanceStatistics(attendanceStatisticsExcelDto);
        Map<String, Object> param = new HashMap<>();
        if(GeneralTool.isNotEmpty(attendanceStatisticsExcelVos)){
            param.put("list", attendanceStatisticsExcelVos);
        }
        TemplateExcelUtils.downLoadExcel("考勤统计", "AttendanceStatistics.xlsx", param, response);
    }

    @Override
    public void exportIaeAttendanceStatisticsExcel(HttpServletResponse response, AttendanceStatisticsExcelDto attendanceStatisticsExcelDto) throws Exception {
        List<IaeAttendanceStatisticsExcelVo> attendanceStatisticsExcelDtos = iaeAttendanceStatistics(attendanceStatisticsExcelDto);
        Map<String, Object> param = new HashMap<>();
        if(GeneralTool.isNotEmpty(attendanceStatisticsExcelDtos)){
            SimpleDateFormat fm = new SimpleDateFormat("yyyy-MM");
            param.put("attendanceTime",fm.format(attendanceStatisticsExcelDto.getDate()));
            SimpleDateFormat fm2 = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            param.put("crateTime",fm2.format(new Date()));
            param.put("list", attendanceStatisticsExcelDtos);
            param.put("total",attendanceStatisticsExcelDtos.size());
        }
        TemplateExcelUtils.downLoadExcel("IAE考勤统计", "iaeAttendanceStatistics.xlsx", param, response);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean adjustSystemLeavetock(LeaveStockDto leaveStockDto) {
        if (leaveStockDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        LeaveStock result = leaveStockMapper.selectById(leaveStockDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
//        //调整时长
//        if(GeneralTool.isNotEmpty(leaveStockDto.getStatus()) && GeneralTool.isNotEmpty(leaveStockDto.getChangeLeaveStock())){
//            //当前时长
//            BigDecimal stock= result.getLeaveStock();
//            //工休日志
//            LeaveLogDto leaveLogVo = new LeaveLogDto();
//            leaveLogVo.setFkCompanyId(leaveStockDto.getFkCompanyId());
//            leaveLogVo.setFkStaffId(leaveStockDto.getFkStaffId());
//            leaveLogVo.setLeaveTypeKey(leaveStockDto.getLeaveTypeKey());
//            leaveLogVo.setRemark(leaveStockDto.getRemark());
//            leaveLogVo.setOptTypeKey(leaveStockDto.getOptTypeKey());
//            //计算最终时长、记录日志
//            BigDecimal leaveLogstock = new BigDecimal(0);
//            if(leaveStockDto.getStatus().equals(0)){
//                leaveLogstock = stock.add(leaveStockDto.getChangeLeaveStock());
//                leaveLogVo.setDuration(leaveStockDto.getChangeLeaveStock());
//            }
//            if(leaveStockDto.getStatus().equals(1)){
//                leaveLogstock = stock.subtract(leaveStockDto.getChangeLeaveStock());
//                leaveLogVo.setDuration(new BigDecimal(0).subtract(leaveStockDto.getChangeLeaveStock()));
//            }
//        }
        BeanUtils.copyProperties(leaveStockDto, result);
        leaveStockMapper.updateById(result);
        return true;
    }

    /**
     * 新增信息(工作流feign调用）
     *
     * @param leaveStockDto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addSystem(LeaveStockDto leaveStockDto) {
        if (GeneralTool.isEmpty(leaveStockDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        LeaveStock leaveStock = new LeaveStock();
        BeanUtils.copyProperties(leaveStockDto, leaveStock);
        leaveStockMapper.insert(leaveStock);
        return leaveStock.getId();
    }

    @Override
    public List<LeaveStockVo> getLeaveStockDtos(LeaveStockDto leaveStockDto) {
        Set<Long> fkStaffIds = Sets.newHashSet();
        if (GeneralTool.isNotEmpty(leaveStockDto.getFkStaffIds())){
            fkStaffIds.addAll(leaveStockDto.getFkStaffIds());
        }
        List<LeaveStock> leaveStocks = leaveStockMapper.selectLeaveStock(null, leaveStockDto.getFkCompanyId(), leaveStockDto.getLeaveTypeKey(), leaveStockDto.getEffectiveDeadline(),fkStaffIds, leaveStockDto.getId());
        return BeanCopyUtils.copyListProperties(leaveStocks, LeaveStockVo::new);
    }

    /**
     * 移动端休假管理员工列表
     *
     * @param staffOfLeaveStockDto
     * @param page
     * @return
     */
    @Override
    public List<StaffOfLeaveStockVo> getStaffOfLeaveStockList(StaffOfLeaveStockDto staffOfLeaveStockDto, Page page) {
        if (GeneralTool.isEmpty(staffOfLeaveStockDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(staffOfLeaveStockDto.getFkCompanyId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

        IPage<StaffOfLeaveStockVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<StaffOfLeaveStockVo> staffOfLeaveStockVos = leaveStockMapper.getStaffOfLeaveStockList(iPage, staffOfLeaveStockDto);
        page.setAll((int) iPage.getTotal());

        return staffOfLeaveStockVos;
    }

    @Override
    public List<LeaveStockVo> getEfficientLeaveStockDtos(LeaveStockDto leaveStockDto) {
        Set<Long> fkStaffIds = Sets.newHashSet();
        if (GeneralTool.isNotEmpty(leaveStockDto.getFkStaffIds())){
            fkStaffIds.addAll(leaveStockDto.getFkStaffIds());
        }
        List<LeaveStock> leaveStocks = leaveStockMapper.selectEfficientLeaveStock(null, leaveStockDto.getFkCompanyId(), leaveStockDto.getLeaveTypeKey(), leaveStockDto.getEffectiveDeadline(),fkStaffIds, leaveStockDto.getId());
        return BeanCopyUtils.copyListProperties(leaveStocks, LeaveStockVo::new);
    }


}
