package com.get.aisplatformcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.aisplatformcenterap.dto.GetPermissionMenuDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoAndItemDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoSearchDto;
import com.get.aisplatformcenterap.dto.UserScopedDataDto;
import com.get.aisplatformcenterap.entity.MReleaseInfo;
import com.get.aisplatformcenterap.vo.MenuTreeVo;
import com.get.aisplatformcenterap.vo.PlatFormTypeVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVos;
import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.permissioncenter.vo.ResourceVo;
import java.util.List;
import javax.validation.Valid;


/**
 * 发版信息
 */
public interface MReleaseInfoService extends IService<MReleaseInfo> {

    /**
     * 获取下拉框数据
     * @return
     */
    List<PlatFormTypeVo> getPlatformTypeDropDown();

    /**
     * 分页获取数据(无权限过滤)
     * @param releaseInfoSearchDto
     * @param page
     * @return
     */
    List<ReleaseInfoAndItemVo> getReleaseInfoAndItem(@Valid ReleaseInfoSearchDto releaseInfoSearchDto, Page page);

    /**
     * 添加
     * @param releaseInfoAndItemDto
     */
    void addReleaseInfoAndItem(ReleaseInfoAndItemDto releaseInfoAndItemDto);

    /**
     * 删除
     * @param id
     */
    void deleteReleaseInfoAndItem(Long id);

    /**
     * 根据id获取详细信息
     * @param id
     * @return
     */
    ReleaseInfoAndItemVo getDetailedInformationById(Long id);

    /**
     * 编辑
     * @param releaseInfoAndItemDto
     */
    void editReleaseInfoAndItem(ReleaseInfoAndItemDto releaseInfoAndItemDto);

    /**
     * 更改发版信息的状态
     * @param releaseInfoAndItemDto
     */
    void updateReleaseInfoStatus(ReleaseInfoAndItemDto releaseInfoAndItemDto);

    /**
     * 根据用户权限获取数据
     * @param userScopedDataDto
     * @param page
     * @return
     */
    List<ReleaseInfoAndItemVo> getUserListByResourceKeys(@Valid UserScopedDataDto userScopedDataDto, Page page);

    /**
     * 根据用户权限获取最新创建的一条数据
     * @param userScopedDataDto
     * @return
     */
    ReleaseInfoAndItemVo getUserOneByResourceKeys(UserScopedDataDto userScopedDataDto);

    /**
     * 分页获取数据
     * @param re
     * @param page
     * @return
     */
    ReleaseInfoAndItemVos getReleaseInfoAndItemAndPage(@Valid ReleaseInfoSearchDto re, Page page);

    /**
     * 根据用户权限获取分页数据
     * @param page
     * @return
     */
    ReleaseInfoAndItemVos getUserListByResourceKeysAndPage(SearchBean<UserScopedDataDto> page);

    /**
     * 获取AIS菜单
     * @return
     */
    List<ResourceVo> getAisPermissionMenu();

    /**
     * 获取华通伙伴菜单
     * @param getPermissionMenuDto
     * @return
     */
    List<MenuTreeVo> getPartnerPermissionMenu(GetPermissionMenuDto getPermissionMenuDto);

    /**
     * 根据用户权限获取发布信息和发布信息详情数据
     * @param userScopedDataDto
     * @return
     */
    ReleaseInfoAndItemVo getDetailByResourceKeys(UserScopedDataDto userScopedDataDto);
}

