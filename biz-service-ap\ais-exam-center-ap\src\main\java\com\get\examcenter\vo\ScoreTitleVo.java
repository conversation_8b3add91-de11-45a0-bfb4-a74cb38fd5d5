package com.get.examcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON>.
 * Time: 17:25
 * Date: 2021/8/27
 * Description:称号管理返回类
 */
@Data
public class ScoreTitleVo extends BaseEntity implements Serializable {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 称号图片
     */
    @ApiModelProperty(value = "称号图片")
    private List<MediaAndAttachedVo> mediaAndAttachedVoList;

    /**
     * 分值范围
     */
    @ApiModelProperty(value = "分值范围")
    private String scoreRange;

    /**
     * 排名范围
     */
    @ApiModelProperty(value = "排名范围")
    private String rankingRange;

    /**
     * 考试Id
     */
    @ApiModelProperty(value = "考试名称")
    private String fkExaminationName;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    //================实体类======================
    private static final long serialVersionUID = 1L;
    /**
     * 称号名称
     */
    @ApiModelProperty(value = "称号名称")
    private String title;
    /**
     * 分值范围（小）
     */
    @ApiModelProperty(value = "分值范围（小）")
    private Integer scoreMin;
    /**
     * 分值范围（大）
     */
    @ApiModelProperty(value = "分值范围（大）")
    private Integer scoreMax;
    /**
     * 排名范围（小）
     */
    @ApiModelProperty(value = "排名范围（小）")
    private Integer rankingMin;
    /**
     * 排名范围（大）
     */
    @ApiModelProperty(value = "排名范围（大）")
    private Integer rankingMax;
    /**
     * 颜色编码，用于突显，可不填
     */
    @ApiModelProperty(value = "颜色编码，用于突显，可不填")
    private String colorCode;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    /**
     * 考试Id
     */
    @ApiModelProperty(value = "考试Id")
    private Long fkExaminationId;

    /**
     * 所属公司Id
     */
    @ApiModelProperty(value = "所属公司Id")
    private Long fkCompanyId;

    /**
     * 参数配置
     */
    @ApiModelProperty(value = "参数配置")
    private String paramJson;



}
