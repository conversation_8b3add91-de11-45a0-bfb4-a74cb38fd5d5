<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.officecenter.mapper.WorkScheduleTimeConfigMapper">

    <select id="getCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM m_work_schedule_time_config
        WHERE fk_company_id = #{fkCompanyId}
        <choose>
            <when test="fkDepartmentId!=null and fkDepartmentId!=''">
                AND fk_department_id = #{fkDepartmentId}
            </when>
            <otherwise>
                AND fk_department_id IS NULL
            </otherwise>
        </choose>

    </select>

    <select id="getWorkScheduleTimeConfig" resultType="com.get.officecenter.entity.WorkScheduleTimeConfig">
        SELECT * FROM m_work_schedule_time_config
        <where>
            AND fk_company_id = #{timeConfigDto.fkCompanyId}
            <!--<if test="timeConfigDto.week != null">-->
                <!--AND FIND_IN_SET(#{timeConfigDto.week},working_week_cycle)-->
            <!--</if>-->
        </where>
    </select>
</mapper>