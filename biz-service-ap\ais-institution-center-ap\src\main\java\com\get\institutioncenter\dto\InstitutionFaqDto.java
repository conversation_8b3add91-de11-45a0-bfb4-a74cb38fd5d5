package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 11:51
 * @Description:
 **/
@Data
public class InstitutionFaqDto extends BaseVoEntity {

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id", required = true)
    @NotNull(message = "学校Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionId;

    /**
     * 问题
     */
    @NotBlank(message = "问题不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "问题", required = true)
    private String question;

    /**
     * 回答
     */
    @ApiModelProperty(value = "回答", required = true)
    @NotBlank(message = "回答不能为空", groups = {Add.class, Update.class})
    private String answer;

    /**
     * 排序，数字由小到大排列
     */
    @NotNull(message = "排序不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "排序，数字由小到大排列", required = true)
    private Integer viewOrder;

    @ApiModelProperty(value = "关键词搜索")
    private String keyWord;

}
