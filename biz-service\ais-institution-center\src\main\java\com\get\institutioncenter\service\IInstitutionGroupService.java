package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.dto.InstitutionGroupDto;
import com.get.institutioncenter.vo.InstitutionGroupVo;
import com.get.institutioncenter.entity.InstitutionGroup;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/10/20
 * @TIME: 10:20
 * @Description:学校集团
 **/
public interface IInstitutionGroupService extends BaseService<InstitutionGroup> {

    /**
     * @return com.get.institutioncenter.vo.InstitutionGroupVo
     * @Description：详情
     * @Param [id]
     * <AUTHOR>
     **/
    InstitutionGroupVo findInstitutionGroupById(Long id);

    /**
     * @return void
     * @Description：批量新增
     * @Param [institutionGroupDtos]
     * <AUTHOR>
     **/
    void batchAdd(ValidList<InstitutionGroupDto> institutionGroupDtos);


    /**
     * @return void
     * @Description：删除
     * @Param [id]
     * <AUTHOR>
     **/
    void delete(Long id);

    /**
     * @return com.get.institutioncenter.vo.InstitutionGroupVo
     * @Description：修改
     * @Param [institutionGroupDto]
     * <AUTHOR>
     **/
    InstitutionGroupVo updateInstitutionGroup(InstitutionGroupDto institutionGroupDto);


    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionGroupVo>
     * @Description：列表
     * @Param [institutionGroupDto, page]
     * <AUTHOR>
     **/
    List<InstitutionGroupVo> getInstitutionGroups(InstitutionGroupDto institutionGroupDto, Page page);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionGroupVo>
     * @Description：学校集团下拉框
     * @Param []
     * <AUTHOR>
     **/
    List<BaseSelectEntity> getInstitutionGroupSelect();

    /**
     * feign调用 通过集团ids 查找对应的集团名称map
     *
     * @Date 17:54 2021/6/3
     * <AUTHOR>
     */
    Map<Long, String> getInstitutionGroupNamesByIds(Set<Long> institutionGroupIdSet);

}
