<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.officecenter.mapper.ClockInDataMapper">
    <select id="getAllClockInData" resultType="com.get.officecenter.entity.ClockInData">
        SELECT * FROM m_clock_in_data
        <where>
            <if test="clockInDataDto.fkCompanyId != null and clockInDataDto.fkCompanyId!=''">
                AND fk_company_id=#{clockInDataDto.fkCompanyId}
            </if>
            <if test="clockInDataDto.dateStart != null">
                AND DATE_FORMAT(clock_in_time,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{clockInDataDto.dateStart},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test="clockInDataDto.dateEnd != null">
                AND DATE_FORMAT(clock_in_time,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{clockInDataDto.dateEnd},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test="clockInDataDto.dataSource != null and clockInDataDto.dataSource != ''">
                AND data_source = #{clockInDataDto.dataSource}
            </if>
            <if test="clockInDataDto.isActive != null">
                AND is_active = #{clockInDataDto.isActive}
            </if>
            AND fk_staff_id IS NOT NULL
        </where>
        ORDER BY clock_in_time DESC
    </select>

    <delete id="deleteClockInData">
        DELETE FROM m_clock_in_data WHERE
         DATE_FORMAT(clock_in_time,'%Y-%m') = DATE_FORMAT(#{clockInTime},'%Y-%m')
         AND data_source = #{dataSource}
         AND fk_company_id = #{fkCompanyId}
    </delete>

    <insert id="importAttendanceMachineData">
        INSERT INTO m_clock_in_data(fk_company_id,fk_staff_id,clock_in_time,data_source,is_active,gmt_create,gmt_create_user)
        VALUES
        <foreach collection="attendanceMachineDataVos" separator="," item="attendanceMachineDataDto">
            (#{fkCompanyId},#{attendanceMachineDataDto.staffId},#{attendanceMachineDataDto.clockInTime},0,1,
            #{attendanceMachineDataDto.gmtCreate},#{attendanceMachineDataDto.gmtCreateUser})
        </foreach>
    </insert>

    <insert id="importDingTalkAttendanceData">
        INSERT INTO m_clock_in_data(fk_company_id,fk_staff_id,clock_in_time,data_source,is_active,gmt_create,gmt_create_user)
        VALUES
        <foreach collection="dingTalkAttendanceVos" separator="," item="dingTalkAttendanceDto">
            (#{fkCompanyId},#{dingTalkAttendanceDto.staffId},#{dingTalkAttendanceDto.punchTime},1,#{dingTalkAttendanceDto.isActive},
            #{dingTalkAttendanceDto.gmtCreate},#{dingTalkAttendanceDto.gmtCreateUser})
        </foreach>
    </insert>

    <select id="getClockInData" resultType="com.get.officecenter.entity.ClockInData">
        SELECT * FROM m_clock_in_data WHERE
        DATE_FORMAT(clock_in_time,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{startTime},'%Y-%m-%d %H:%i:%s')
        AND DATE_FORMAT(clock_in_time,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endTime},'%Y-%m-%d %H:%i:%s')
        AND fk_staff_id IS NOT NULL
        AND is_active = 1
        AND fk_company_id = #{fkCompanyId}
    </select>
</mapper>