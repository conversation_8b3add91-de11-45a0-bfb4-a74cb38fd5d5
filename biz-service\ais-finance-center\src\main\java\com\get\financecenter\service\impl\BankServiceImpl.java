package com.get.financecenter.service.impl;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.financecenter.dao.BankMapper;
import com.get.financecenter.vo.BankVo;
import com.get.financecenter.service.IBankService;
import com.get.financecenter.dto.BankDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/2/14
 * @TIME: 14:46
 * @Description:
 **/
@Service
public class BankServiceImpl implements IBankService {
    @Resource
    private BankMapper bankMapper;

    @Override
    public void addBank(List<BankDto> bankDto) {

    }

    @Override
    public List<BankVo> getBankDtos(BankDto bankDto, Page page) {
        return null;
    }

    @Override
    public BankVo updateBank(BankDto bankDto) {
        return null;
    }

    @Override
    public void deleteBank(Long id) {

    }

    @Override
    public BankVo findBankById(Long id) {
        return null;
    }

    @Override
    public List<BaseSelectEntity> bankSelect(String fkCurrencyTypeNum) {
        return bankMapper.bankSelect(fkCurrencyTypeNum);
    }
}
