<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentOfferNoticeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.StudentOfferNotice">
        <id column="id" property="id" />
        <result column="fk_report_sale_id" property="fkReportSaleId" />
        <result column="fk_student_id" property="fkStudentId" />
<!--        <result column="fk_student_offer_item_step_id_highest" property="fkStudentOfferItemStepIdHighest" />-->
        <result column="fk_agent_id" property="fkAgentId" />
        <result column="fk_student_offer_notice_template_id" property="fkStudentOfferNoticeTemplateId" />
        <result column="email_subject" property="emailSubject" />
        <result column="email_content" property="emailContent" />
        <result column="fk_staff_id_from" property="fkStaffIdFrom" />
        <result column="from_email" property="fromEmail" />
        <result column="cc_email" property="ccEmail" />
        <result column="to_email" property="toEmail" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_create_user" property="gmtCreateUser" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="gmt_modified_user" property="gmtModifiedUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fk_report_sale_id,fk_student_id, fk_agent_id, fk_student_offer_notice_template_id, email_subject, email_content,fk_staff_id_from ,from_email,cc_email ,to_email, remark, status, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
    </sql>


    <select id="getEmailStatisticsOfferItem" resultType="com.get.salecenter.vo.EmailStatisticsOfferItemVo">
        SELECT
            a.id,
            a.fk_parent_student_offer_item_id,
            a.fk_agent_id,
            a.fk_student_id,
            a.fk_student_offer_item_step_id,
            a.fk_student_offer_id,
            a.fk_institution_id,
            a.fk_institution_course_id,
            a.fk_institution_course_major_level_ids,
            a.defer_opening_time,
            a.old_institution_name,
            a.old_course_custom_name,
            a.old_course_major_level_name,
            a.fk_area_country_id,
            b.agent_contact_email as agentEmail
        FROM
            m_student_offer_item AS a
            INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.itemPermissionSql"/>
            ) z ON a.id=z.id
            INNER JOIN m_agent AS ma ON ma.id = a.fk_agent_id
            LEFT JOIN m_student_offer b on b.id = a.fk_student_offer_id
        WHERE a.`status` = 1
        AND a.is_follow = 0
<!--        <if test="emailStatisticsDto.fkCompanyId !=null">-->
<!--        AND b.fk_company_id = 2-->
<!--        </if>-->
        <if test="emailStatisticsDto.openingTimeStart != null">
            AND  DATE_FORMAT(a.defer_opening_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{emailStatisticsDto.openingTimeStart},'%Y-%m-%d')
        </if>
        <if test="emailStatisticsDto.openingTimeEnd != null">
            AND DATE_FORMAT(a.defer_opening_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{emailStatisticsDto.openingTimeEnd},'%Y-%m-%d')
        </if>
        <if test="emailStatisticsDto.studentIds != null and emailStatisticsDto.studentIds.size()>0">
            AND a.fk_student_id in
            <foreach collection="emailStatisticsDto.studentIds" item="studentId" open="(" close=")" separator=",">
            #{studentId}
            </foreach>
        </if>
        <if test="emailStatisticsDto.openingTimeEnd != null">
            and not EXISTS (
            SELECT 1 FROM m_student_offer_item t
            where t.status = 1
            <!--暂时只统计英国-->
            AND t.fk_area_country_id = 7
            and DATE_FORMAT(t.defer_opening_time,'%Y-%m-%d') <![CDATA[> ]]> DATE_FORMAT(#{emailStatisticsDto.openingTimeEnd},'%Y-%m-%d') and t.fk_student_id = a.fk_student_id
            )
        </if>
        <!--暂时只统计英国-->
        AND a.fk_area_country_id = 7


    </select>
    <select id="getEmailStatisticsOfferItemIsLanguageCourse"
            resultType="com.get.salecenter.vo.EmailStatisticsOfferItemVo">
        SELECT
            a.*
        FROM
            ais_sale_center.m_student_offer_item a
        WHERE
            1 = 1
          AND a.`status` = 1
          AND (
                FIND_IN_SET( 2, a.fk_institution_course_major_level_ids )
                OR FIND_IN_SET( 3, a.fk_institution_course_major_level_ids )
                OR FIND_IN_SET( 19, a.fk_institution_course_major_level_ids )
                OR a.old_course_major_level_name LIKE ( '%English%' ))
        <if test="emailStatisticsDto.openingTimeStart != null">
            AND  DATE_FORMAT(a.defer_opening_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{emailStatisticsDto.openingTimeStart},'%Y-%m-%d')
        </if>
        <if test="emailStatisticsDto.openingTimeEnd != null">
            AND DATE_FORMAT(a.defer_opening_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{emailStatisticsDto.openingTimeEnd},'%Y-%m-%d')
        </if>
        <if test="emailStatisticsDto.studentIds != null and emailStatisticsDto.studentIds.size()>0">
            AND a.fk_student_id in
            <foreach collection="emailStatisticsDto.studentIds" item="studentId" open="(" close=")" separator=",">
                #{studentId}
            </foreach>
        </if>
        <!--暂时只统计英国-->
        AND a.fk_area_country_id = 7


    </select>
    <select id="getStudentOfferNotices" resultType="com.get.salecenter.vo.StudentOfferNoticeListVo">
        SELECT
            a.id,
            a.fk_student_id,
            b.birthday,
            c.`name` agentName,
            a.email_content,
            a.email_subject,
            a.`status`,
            a.from_email,
            a.fk_staff_id_from,
            a.to_email,
            a.cc_email,
            a.gmt_create
        FROM
            `m_student_offer_notice` a
                LEFT JOIN m_student b on a.fk_student_id = b.id
                INNER JOIN m_agent c on c.id = a.fk_agent_id AND c.is_reject_email = 0
                LEFT JOIN u_student_offer_notice_template d on d.id = a.fk_student_offer_notice_template_id
        WHERE 1=1
            and a.status !=-2
          <if test="loginId !=null and loginId !=''">
            and a.gmt_create_user = #{loginId}
          </if>
          <if test="studentOfferNoticeListDto.fkCompanyId !=null">
          and b.fk_company_id = #{studentOfferNoticeListDto.fkCompanyId}
          </if>
          <if test="studentOfferNoticeListDto.reportStatus !=null">
          and a.`status` = #{studentOfferNoticeListDto.reportStatus}
          </if>
         <if test="studentOfferNoticeListDto.studentName !=null and studentOfferNoticeListDto.studentName !=''">
             and (
             REPLACE(CONCAT(b.first_name,b.last_name),' ','') like concat('%',#{studentOfferNoticeListDto.studentName},'%')
             OR REPLACE(CONCAT(b.last_name,b.first_name),' ','') like concat('%',#{studentOfferNoticeListDto.studentName},'%')
             OR REPLACE(b.`name`,' ','') like concat('%',#{studentOfferNoticeListDto.studentName},'%')
             OR REPLACE(b.last_name,' ','') like concat('%',#{studentOfferNoticeListDto.studentName},'%')
             OR REPLACE(b.first_name,' ','') like concat('%',#{studentOfferNoticeListDto.studentName},'%'))
         </if>
        ORDER BY a.gmt_create desc,a.id desc
    </select>

</mapper>
