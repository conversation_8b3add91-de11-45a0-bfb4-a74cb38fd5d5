package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-02-14 09:48:45
 */

@Data
@TableName("r_student_agent") 
public class RStudentAgentEntity extends BaseEntity implements Serializable {

  @ApiModelProperty( "关系Id")
  private Long id;
 

  @ApiModelProperty( "学生Id")
  private Long fkStudentId;
 

  @ApiModelProperty( "代理Id")
  private Long fkAgentId;
 

  @ApiModelProperty( "是否激活：0否/1是")
  private Boolean isActive;
 

  @ApiModelProperty( "绑定时间")
  private LocalDateTime activeDate;
 

  @ApiModelProperty( "取消绑定时间（下次绑定时，需要重新建立记录）")
  private LocalDateTime unactiveDate;
 

 

}
