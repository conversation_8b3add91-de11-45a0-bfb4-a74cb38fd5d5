package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;
import java.util.Set;

@Data
public class BusinessStudentVo extends BaseEntity {
    @ApiModelProperty(value = "学生资源Id")
    private  Long  fkClientId;

    @ApiModelProperty(value = "学生编号")
    private String num;

    @ApiModelProperty(value = "最终状态名称")
    private String maxStepOrderName;

    @ApiModelProperty(value = "最终状态编号")
    private Integer  maxStepOrder;

    /**
     * 学生现居所在国家Id
     */
    @ApiModelProperty(value = "学生现居所在国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "申请国家/地区")
    private String areaCountryNames;

    @ApiModelProperty(value = "代理编号")
    private Set<String> currentAgentNumList;

    @ApiModelProperty(value = "代理名称")
    private Set<String> currentStaffNameList;

    @ApiModelProperty(value = "BD名称")
    private Set<String> currentBdNameList;
}
