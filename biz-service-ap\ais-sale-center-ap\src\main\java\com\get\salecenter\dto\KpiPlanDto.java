package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2024/4/16
 * @TIME: 14:17
 * @Description:
 **/
@Data
public class KpiPlanDto extends BaseVoEntity {
    @ApiModelProperty(value = "公司ID")
    @NotNull(message = "所属公司不能为空", groups = {Add.class, Update.class})
    private String fkCompanyIds;


    @NotBlank(message = "标题不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "学生创建时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date studentCreateTimeStart;

    @ApiModelProperty(value = "学生创建时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date studentCreateTimeEnd;
    /**
     * 申请创建开始时间
     */
    @ApiModelProperty(value = "申请计划创建时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date offerItemCreateTimeStart;

    /**
     * 申请创建结束时间
     */
    @ApiModelProperty(value = "申请计划创建结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date offerItemCreateTimeEnd;



    @ApiModelProperty(value = "入学时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date intakeTimeStart;

    @ApiModelProperty(value = "入学时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date intakeTimeEnd;

    @ApiModelProperty(value = "步骤登记时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepTimeStart;

    @ApiModelProperty(value = "步骤登记时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepTimeEnd;


    @ApiModelProperty(value = "是否开启定时统计：0否/1是")
    @NotNull(message = "定时统计不能为空", groups = {Add.class, Update.class})
    private Integer isEnableScheduledCount;

 
}
