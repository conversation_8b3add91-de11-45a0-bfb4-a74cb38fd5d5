package com.get.salecenter.utils;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import lombok.experimental.UtilityClass;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.util.StringUtils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.AlgorithmParameters;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.Security;
import java.security.spec.InvalidParameterSpecException;
import java.util.Arrays;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2021/3/25;
 * @TIME: 14:51
 * @Description: 微信工具类
 **/
@UtilityClass
public class WxUtils {


    /**
     * @return java.lang.String
     * @Description: 生成分享二维码 转成Base64
     * @Param []
     * <AUTHOR>
     */
    /*public static String getShareCodeToBase64() {
        try {
            String wxCodeUrl = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + getAccessToken();
            URL url = new URL(wxCodeUrl);
            HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.setRequestMethod("POST");
            // 发送POST请求必须设置如下两行
            urlConnection.setDoOutput(true);
            urlConnection.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            PrintWriter printWriter = new PrintWriter(urlConnection.getOutputStream());
            // 发送请求参数
            JSONObject paramJson = new JSONObject();
            paramJson.put("scene", "market");
            printWriter.write(paramJson.toString());
            // flush输出流的缓冲
            printWriter.flush();
            //开始获取数据
            InputStream in = urlConnection.getInputStream();
            ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
            byte[] buff = new byte[1024];
            int rc;
            while ((rc = in.read(buff, 0, buff.length)) > 0) {
                swapStream.write(buff, 0, rc);
            }
            byte[] data = swapStream.toByteArray();
            String result = new String(Base64.encodeBase64(data));
            swapStream.close();
            in.close();
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }*/

    private static final Logger log = LoggerFactory.getLogger(WxUtils.class);

    /**
     * @return java.lang.String
     * @Description: 获取token
     * @Param []
     * <AUTHOR>
     */
    private static String getAccessToken(String APP_ID, String APP_SECRET) throws Exception {
        if (StringUtils.isEmpty(APP_ID) || StringUtils.isEmpty(APP_SECRET)) {
            throw new Exception("appId或者appSecret参数异常");
        }
        String requestUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + APP_ID + "&secret=" + APP_SECRET;
        URL url = new URL(requestUrl);
        // 打开和URL之间的连接
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        // 设置通用的请求属性
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Connection", "Keep-Alive");
        connection.setUseCaches(false);
        connection.setDoOutput(true);
        connection.setDoInput(true);
        // 得到请求的输出流对象
        DataOutputStream out = new DataOutputStream(connection.getOutputStream());
        out.writeBytes("");
        out.flush();
        out.close();
        // 建立实际的连接
        connection.connect();
        // 定义 BufferedReader输入流来读取URL的响应
        BufferedReader in = null;
        if (requestUrl.contains("nlp")) {
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "GBK"));
        } else {
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
        }
        String result = "";
        String getLine;
        while ((getLine = in.readLine()) != null) {
            result += getLine;
        }
        in.close();

        JSONObject jsonObject = JSONUtil.parseObj(result);
        return jsonObject.getStr("access_token");
    }

    /**
     * @return void
     * @Description: 生成分享二维码
     * @Param [response]
     * <AUTHOR>
     * //
     */
    public static void getShareCode(HttpServletResponse response, String APP_ID, String APP_SECRET) {
        try {
            String wxCodeUrl = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + getAccessToken(APP_ID, APP_SECRET);
            URL url = new URL(wxCodeUrl);
            HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.setRequestMethod("POST");
            // 发送POST请求必须设置如下两行
            urlConnection.setDoOutput(true);
            urlConnection.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            PrintWriter printWriter = new PrintWriter(urlConnection.getOutputStream());
            // 发送请求参数
            JSONObject paramJson = new JSONObject();
            paramJson.put("scene", "type=market");
            printWriter.write(paramJson.toString());
            // flush输出流的缓冲
            printWriter.flush();
            //开始获取数据
            BufferedInputStream bis = new BufferedInputStream(urlConnection.getInputStream());
            OutputStream out = response.getOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while (-1 != (length = bis.read(buffer, 0, buffer.length))) {
                out.write(buffer, 0, length);
            }
            response.setContentType("image/png");
            out.flush();
            bis.close();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取微信小程序 session_key 和 openid
     *
     * <AUTHOR>
     * @param code 调用微信登陆返回的Code
     * @return
     */
/*    public static com.alibaba.fastjson.JSONObject getSessionKeyOropenid(String code){
        //微信端登录code值
        String wxCode = code;
        Map<String,String> requestUrlParam = new HashMap<String,String>();
        requestUrlParam.put("appid", APP_ID);	//开发者设置中的appId
        requestUrlParam.put("secret", APP_SECRET);	//开发者设置中的appSecret
        requestUrlParam.put("js_code", wxCode);	//小程序调用wx.login返回的code
        requestUrlParam.put("grant_type", "authorization_code");	//默认参数

        //发送post请求读取调用微信 https://api.weixin.qq.com/sns/jscode2session 接口获取openid用户唯一标识
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(sendPost(" https://api.weixin.qq.com/sns/jscode2session", requestUrlParam));
        return jsonObject;
    }*/

    /**
     * 解密用户敏感数据获取用户信息
     *
     * @param encryptedData 包括敏感数据在内的完整用户信息的加密数据
     * @param sessionKey    数据进行加密签名的密钥
     * @param iv            加密算法的初始向量
     * @return
     * <AUTHOR>
     */
    public static com.alibaba.fastjson.JSONObject getUserInfo(String encryptedData, String sessionKey, String iv) {
        Base64 base64 = new Base64();
        // 被加密的数据
        byte[] dataByte = base64.decode(encryptedData);
        // 加密秘钥
        byte[] keyByte = base64.decode(sessionKey);
        // 偏移量
        byte[] ivByte = base64.decode(iv);
        try {
            // 如果密钥不足16位，那么就补足.  这个if 中的内容很重要
            int base = 16;
            if (keyByte.length % base != 0) {
                int groups = keyByte.length / base + (keyByte.length % base != 0 ? 1 : 0);
                byte[] temp = new byte[groups * base];
                Arrays.fill(temp, (byte) 0);
                System.arraycopy(keyByte, 0, temp, 0, keyByte.length);
                keyByte = temp;
            }
            // 初始化
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
            SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
            AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
            parameters.init(new IvParameterSpec(ivByte));
            cipher.init(Cipher.DECRYPT_MODE, spec, parameters);// 初始化
            byte[] resultByte = cipher.doFinal(dataByte);
            if (null != resultByte && resultByte.length > 0) {
                String result = new String(resultByte, "UTF-8");
                return JSON.parseObject(result);
            }
        } catch (NoSuchAlgorithmException e) {
            log.error(e.getMessage(), e);
        } catch (NoSuchPaddingException e) {
            log.error(e.getMessage(), e);
        } catch (InvalidParameterSpecException e) {
            log.error(e.getMessage(), e);
        } catch (IllegalBlockSizeException e) {
            log.error(e.getMessage(), e);
        } catch (BadPaddingException e) {
            log.error(e.getMessage(), e);
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        } catch (InvalidKeyException e) {
            log.error(e.getMessage(), e);
        } catch (InvalidAlgorithmParameterException e) {
            log.error(e.getMessage(), e);
        } catch (NoSuchProviderException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url 发送请求的 URL
     * @return 所代表远程资源的响应结果
     */
    public static String sendPost(String url, Map<String, ?> paramMap) {
        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";

        String param = "";
        Iterator<String> it = paramMap.keySet().iterator();

        while (it.hasNext()) {
            String key = it.next();
            param += key + "=" + paramMap.get(key) + "&";
        }

        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("Accept-Charset", "utf-8");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            out.print(param);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        //使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result;
    }


    /*public static String CreateOrder() throws Exception{
        //自定义单号
        String payCode = IdUtil.fastSimpleUUID();
        Map<String, String> params = UnifiedOrderModel
                .builder()
                .appid("wx9a24c5ab547e58b3")
                .mch_id("1602083054")
                .nonce_str(WxPayKit.generateStr())
                .body("支付")
                .attach("支付")
                .out_trade_no(payCode)
                .total_fee("2")
                .spbill_create_ip("")
                .notify_url("2")
                .trade_type(TradeType.NATIVE.getTradeType())
                .build()
                .createSign("jsi293k230djksdf923127sjweik6ag2", SignType.HMACSHA256);
        String xmlResult = WxPayApi.pushOrder(false, params);
        Map<String, String> result = WxPayKit.xmlToMap(xmlResult);
        String returnCode = result.get("return_code");
        String returnMsg = result.get("return_msg");
        System.out.println(returnMsg);
        if (!WxPayKit.codeIsOk(returnCode)) {
            // 异常状态判断 替换成你自己的
            throw new Exception("");
        }
        String resultCode = result.get("result_code");
        if (!WxPayKit.codeIsOk(resultCode)) {
            // 异常状态判断 替换成你自己的
            throw new Exception("");
        }
        //生成预付订单success
        String qrCodeUrl = result.get("code_url");
        return qrCodeUrl;
    }*/

    public SignatureHeader getSignatureHeaderByHttpHeaders(HttpHeaders headers) {
        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setSignature(headers.getFirst("Wechatpay-Signature"));
        signatureHeader.setSerial(headers.getFirst("Wechatpay-Serial"));
        signatureHeader.setTimeStamp(headers.getFirst("Wechatpay-Timestamp"));
        signatureHeader.setNonce(headers.getFirst("Wechatpay-Nonce"));
        return signatureHeader;
    }

    /**
     * GET 请求
     *
     * @param url
     * @param params
     * @param tTypeReference
     * @param <T>
     * @return
     */
    public static  <T> T doRequest(String url,
                                   Map<String, String> params,
                                   TypeReference<T> tTypeReference) {
        String apiUrl = url;
        String urlParams = "";
        Set<String> keySet = params.keySet();
        for (String paramKey : keySet) {
            urlParams += "&" + paramKey + "=" + encodeParam(params.get(paramKey));
        }
        try {
            String result = doGet(apiUrl + "?" + urlParams, null);
            try {
                T response = JSON.parseObject(result, tTypeReference);
                return response;
            } catch (Exception e) {
                throw e;
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public static String encodeParam(String param) {
        try {
            return URLEncoder.encode(param, "UTF-8");
        } catch (Exception e) {
            return param;
        }
    }

    public static String doGet(String url, Map<String, String> param) {
        if (StringUtils.isEmpty(url)) {
            return null;
        } else {
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(15000).setConnectTimeout(15000).build();
            CloseableHttpClient httpclient = HttpClients.custom().setDefaultRequestConfig(requestConfig).build();
            String resultString = "";
            CloseableHttpResponse response = null;

            try {
                URIBuilder builder = new URIBuilder(url);
                if (param != null) {
                    for (String key : param.keySet()) {
                        builder.addParameter(key, (String) param.get(key));
                    }
                }

                URI uri = builder.build();
                HttpGet httpGet = new HttpGet(uri);
                response = httpclient.execute(httpGet);
                if (response.getStatusLine().getStatusCode() == 200) {
                    resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
                }
            } catch (Exception e) {
                log.error("HttpClientUtil-doGet方法发生异常:", e);
            } finally {
                try {
                    if (response != null) {
                        response.close();
                    }

                    httpclient.close();
                } catch (IOException e) {
                    log.error("HttpClientUtil-doGet方法发生异常:", e);
                }

            }

            return resultString;
        }
    }

}
