package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 学校类型关系
 * @TableName r_institution_commission_label
 */
@Data
@TableName("r_institution_commission_label")
public class RInstitutionCommissionLabelEntity extends BaseEntity implements Serializable {
    @ApiModelProperty("公司Id")
    private Long fkCompanyId;

    @ApiModelProperty("学校主键Id")
    private Long fkInstitutionId;


    @ApiModelProperty("标签Id")
    private Long fkLabelId;

    private static final long serialVersionUID = 1L;

}