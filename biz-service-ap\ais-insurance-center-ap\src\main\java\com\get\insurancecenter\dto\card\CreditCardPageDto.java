package com.get.insurancecenter.dto.card;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote: 信用卡列表数据
 */
@Data
public class CreditCardPageDto {

    @ApiModelProperty(value = "信用卡类型：Visa/Master/UnionPay")
    private String cardTypeKey;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "1:未还款;2:逾期未还;3:已还款;4:不分已还")
    private Integer status;
}
