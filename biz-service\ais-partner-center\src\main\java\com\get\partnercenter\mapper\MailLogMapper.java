package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.partnercenter.dto.MailLogDto;
import com.get.partnercenter.entity.MailLogEntity;
import com.get.partnercenter.vo.MailLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【m_partner_user】的数据库操作Mapper
 * @createDate 2024-12-20 14:13:45
 * @Entity com.partner.entity.MPartnerUser
 */
@Mapper
@DS("partnerdb")
public interface MailLogMapper extends BaseMapper<MailLogEntity> {

    /**
     * 查询邮件日志
     * @param page
     * @param logDto
     * @param staffFollowerIds
     * @return
     */
    List<MailLogVo> selectMailLog(IPage<MailLogVo> page, @Param("param") MailLogDto logDto, @Param("staffFollowerIds") List<Long> staffFollowerIds);


    MailLogVo selectDetail(@Param("id") Long id);
}




