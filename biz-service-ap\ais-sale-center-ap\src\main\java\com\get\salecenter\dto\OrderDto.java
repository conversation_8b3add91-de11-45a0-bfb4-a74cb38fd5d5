package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/23 10:26
 */
@Data
public class OrderDto {
    @ApiModelProperty("issue课程id")
    private Long issueCourseId;
    @ApiModelProperty("加密后的key")
    private String key;
    @ApiModelProperty("bms课程计划id")
    private Long itemId;
    @ApiModelProperty("创建人id")
    private Integer creatId;
    @ApiModelProperty("创建人名称")
    private String creatName;
    @ApiModelProperty("国家")
    private Long fkInstitutionId;
    @ApiModelProperty("一级院校")
    private Long fkInstitutionFacultyId;
    @ApiModelProperty("二级院校")
    private Long fkInstitutionZoneId;
    @ApiModelProperty("联系人邮箱")
    private String stuContPerEmailG;
    @ApiModelProperty("联系人姓名")
    private String stuContPerNameG;
    @ApiModelProperty(value = "HTI聯系人姓名")
    private String stuContPerName;
    @ApiModelProperty(value = "HTI聯系人邮箱")
    private String stuContPerEmail;
    @ApiModelProperty("来源")
    private String stuSource;
}
