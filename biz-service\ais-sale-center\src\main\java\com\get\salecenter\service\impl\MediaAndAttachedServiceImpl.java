package com.get.salecenter.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.platformconfigcenter.feign.IPlatformConfigCenterClient;
import com.get.salecenter.dao.sale.AgentContractMapper;
import com.get.salecenter.dao.sale.MediaAndAttachedMapper;
import com.get.salecenter.dao.sale.PayablePlanMapper;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.entity.AgentContract;
import com.get.salecenter.entity.SaleMediaAndAttached;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.google.common.collect.Lists;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/8/14 9:58
 * @verison: 1.0
 * @description:
 */
@Service
public class MediaAndAttachedServiceImpl extends ServiceImpl<MediaAndAttachedMapper, SaleMediaAndAttached> implements IMediaAndAttachedService {
    @Resource
    private MediaAndAttachedMapper attachedMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private PayablePlanMapper payablePlanMapper;
    @Resource
    private AgentContractMapper agentContractMapper;
    @Resource
    private IPlatformConfigCenterClient platformConfigCenterClient;

    /**
     * @Description:上传文件
     * @Param
     * @Date 12:14 2021/5/12
     * <AUTHOR>
     */
    @Override
    public List<FileDto> upload(MultipartFile[] multipartFiles, String type) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        List<FileDto> fileDtos = null;
        Result<List<FileDto>> result = fileCenterClient.upload(multipartFiles, type);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        } else {
            throw new GetServiceException(result.getMessage());
        }
        return fileDtos;
    }

    @Override
    public List<FileDto> upload(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
//        List<FileDto> fileDtos = null;
//        try {
//            ListResponseBo responseBo = uploadService.upload(multipartFiles, LoggerModulesConsts.SALECENTER);
//            if (!ErrorCodeEnum.REQUEST_OK.getCode().equals(responseBo.getCode())) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
//            }
//            JSONArray jsonArray = JSONArray.fromObject(responseBo.getDatas());
//            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
//        } catch (IOException e) {
//            e.printStackTrace();
//        }

        List<FileDto> fileDtos = null;
        Result<List<FileDto>> result = fileCenterClient.upload(multipartFiles, LoggerModulesConsts.SALECENTER);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
        }
        return fileDtos;
    }

    @Override
    public List<FileDto> uploadAppendix(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
//        List<FileDto> fileDtos = null;
//        try {
//            ListResponseBo responseBo = uploadService.uploadAppendix(multipartFiles, LoggerModulesConsts.SALECENTER);
//            if (!ErrorCodeEnum.REQUEST_OK.getCode().equals(responseBo.getCode())) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
//            }
//            JSONArray jsonArray = JSONArray.fromObject(responseBo.getDatas());
//            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
//        } catch (IOException e) {
//            e.printStackTrace();
//        }

        List<FileDto> fileDtos = null;
        Result<List<FileDto>> result = fileCenterClient.uploadAppendix(multipartFiles, LoggerModulesConsts.SALECENTER);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        } else {
            throw new GetServiceException(result.getMessage());
        }
        return fileDtos;
    }

    @Override
    public void deleteMediaAttached(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        SaleMediaAndAttached mediaAndAttached = attachedMapper.selectById(id);
        if (GeneralTool.isEmpty(mediaAndAttached)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_not_exist"));
        }
        if (FileTypeEnum.SALE_APP_AGENT_CHANGE_FILE.key.equals(mediaAndAttached.getTypeKey())) {
            log.error("无法删除变更声明文件");
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        // 代理申请管理的附件删除不能删除file表里的数据
        String fkTableName = mediaAndAttached.getFkTableName();
        if (!ObjectUtil.equal(TableEnum.APP_AGENT.key, fkTableName)) {
            Result<Boolean> result = fileCenterClient.delete(mediaAndAttached.getFkFileGuid(), LoggerModulesConsts.SALECENTER);
            if (result.isSuccess()) {
                int i = attachedMapper.deleteById(id);
                if (i <= 0) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
                }
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        } else {
            int i = attachedMapper.deleteById(id);
            if (i <= 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        }

    }

    @Override
    public MediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo.getFkTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("tableName_null"));
        }
        SaleMediaAndAttached andAttached = BeanCopyUtils.objClone(mediaAttachedVo, SaleMediaAndAttached::new);
        Integer nextIndexKey = attachedMapper.getNextIndexKey(mediaAttachedVo.getFkTableId(), mediaAttachedVo.getFkTableName());
        nextIndexKey = GeneralTool.isNotEmpty(nextIndexKey) ? nextIndexKey : 0;
        //实例化对象
        andAttached.setIndexKey(nextIndexKey);
        if (GeneralTool.isEmpty(andAttached.getGmtCreateUser())) {
            utilService.updateUserInfoToEntity(andAttached);
        }
        attachedMapper.insert(andAttached);
        MediaAndAttachedVo mediaAndAttachedVo = BeanCopyUtils.objClone(andAttached, MediaAndAttachedVo::new);
        mediaAndAttachedVo.setFilePath(mediaAttachedVo.getFilePath());
        mediaAndAttachedVo.setFileNameOrc(mediaAttachedVo.getFileNameOrc());
        mediaAndAttachedVo.setTypeValue(FileTypeEnum.getValue(mediaAttachedVo.getTypeKey()));
        mediaAndAttachedVo.setId(andAttached.getId());
        mediaAndAttachedVo.setFileKey(mediaAttachedVo.getFileKey());
        return mediaAndAttachedVo;
    }

    /**
     * 批量新增
     * @param mediaAttachedVos
     * @return
     */
    @Override
    public Boolean saveBatchMediaAndAttached(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)){
            return false;
        }
        List<SaleMediaAndAttached> saleMediaAndAttacheds = Lists.newArrayList();
        for (MediaAndAttachedDto mediaAttachedVo : mediaAttachedVos) {
            if (GeneralTool.isEmpty(mediaAttachedVo.getFkTableName())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("tableName_null"));
            }
            SaleMediaAndAttached andAttached = BeanCopyUtils.objClone(mediaAttachedVo, SaleMediaAndAttached::new);
            Integer nextIndexKey = attachedMapper.getNextIndexKey(mediaAttachedVo.getFkTableId(), mediaAttachedVo.getFkTableName());
            nextIndexKey = GeneralTool.isNotEmpty(nextIndexKey) ? nextIndexKey : 0;
            //实例化对象
            andAttached.setIndexKey(nextIndexKey);
            utilService.setCreateInfo(andAttached);
            saleMediaAndAttacheds.add(andAttached);
        }
        return saveBatch(saleMediaAndAttacheds);
    }

    @Override
    public List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo) {
        List<SaleMediaAndAttached> mediaAndAttacheds = getMediaAndAttacheds(attachedVo);
        System.out.println(mediaAndAttacheds);
        return getFileMedia(mediaAndAttacheds);
    }

    /**
     * @Description: 根据表ids获取批量的附件
     * @Author: Jerry
     * @Date:9:32 2021/9/7
     */
    @Override
    public Map<Long, List<MediaAndAttachedVo>> getMediaAndAttachedDtoByFkTableIds(String fkTableName, Set<Long> fkTableIds) {
        List<SaleMediaAndAttached> mediaAndAttacheds = getMediaAndAttachedsByFkTableIds(fkTableName, fkTableIds);
        return getFileMediaByFkTableIds(mediaAndAttacheds);
    }

    @Override
    public List<Long> isExist(String fkTypeKey, Set<Long> fkTableIds) {
        List<SaleMediaAndAttached> saleMediaAndAttacheds = attachedMapper.selectList(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                .eq(SaleMediaAndAttached::getTypeKey, fkTypeKey)
                .in(SaleMediaAndAttached::getFkTableId, fkTableIds));
        return saleMediaAndAttacheds.stream().map(SaleMediaAndAttached::getFkTableId).distinct().collect(Collectors.toList());
    }

    /**
     * @Description: 根据表ids解析媒体附件, 返回表id对应的附件对象
     * @Author: Jerry
     * @Date:9:35 2021/9/7
     */
    private Map<Long, List<MediaAndAttachedVo>> getFileMediaByFkTableIds(List<SaleMediaAndAttached> mediaAndAttachedList) {
        Map<Long, List<MediaAndAttachedVo>> map = new HashMap<>();
        if (GeneralTool.isEmpty(mediaAndAttachedList)) {
            return map;
        }
        //获取guid集合
        List<String> guidList = mediaAndAttachedList.stream().map(SaleMediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        if (GeneralTool.isEmpty(guidList)) {
            return map;
        }
        //转换成DTO
        List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedList.stream()
                .map(mediaAndAttached -> BeanCopyUtils.objClone(mediaAndAttached, MediaAndAttachedVo::new)).collect(Collectors.toList());
        //根据GUID服务调用查询
//        ListResponseBo responseBo = fileCenterClient.findFileByGuid(guidList, LoggerModulesConsts.SALECENTER);
//        JSONArray jsonArray = JSONArray.fromObject(responseBo.getDatas());

        List<FileDto> fileDtos = Lists.newArrayList();
        Map<String, List<String>> guidListWithTypeMap = new HashMap<>();
        guidListWithTypeMap.put(LoggerModulesConsts.SALECENTER, guidList);
        Result<List<FileDto>> fileDtoResult = fileCenterClient.findFileByGuid(guidListWithTypeMap);
        if (fileDtoResult.isSuccess() && GeneralTool.isNotEmpty(fileDtoResult.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(fileDtoResult.getData());
            fileDtos.addAll(JSONArray.toList(jsonArray, new FileDto(), new JsonConfig()));
        }
        //返回结果不为空时
        List<MediaAndAttachedVo> collect = null;
        mediaAndAttachedVos.removeIf(Objects::isNull);
        fileDtos.removeIf(Objects::isNull);
        if (GeneralTool.isNotEmpty(fileDtos) && GeneralTool.isNotEmpty(mediaAndAttachedVos)) {
            //遍历查询GUID是否一致
            collect = mediaAndAttachedVos.stream().map(mediaAndAttachedDto -> fileDtos
                    .stream()
                    .filter(fileDto -> fileDto.getFileGuid().equals(mediaAndAttachedDto.getFkFileGuid()))
                    .findFirst()
                    .map(fileDto -> {
                        mediaAndAttachedDto.setFilePath(fileDto.getFilePath());
                        mediaAndAttachedDto.setFileNameOrc(fileDto.getFileNameOrc());
                        mediaAndAttachedDto.setFileKey(fileDto.getFileKey());
                        mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedDto.getTypeKey()));
                        /*mediaAndAttachedDto.setFkTableName(null);*/
                        return mediaAndAttachedDto;
                    }).orElse(null)
            ).collect(Collectors.toList());
        }
        if (GeneralTool.isEmpty(collect)) {
            return map;
        }
        collect.removeIf(Objects::isNull);
        for (MediaAndAttachedVo mediaAndAttachedVo : collect) {
            //表id
            Long fkTableId = mediaAndAttachedVo.getFkTableId();
            //如果集合中包含表id，则往原记录上面添加记录
            if (map.containsKey(fkTableId)) {
                List<MediaAndAttachedVo> mediaAndAttachedVoList = map.get(fkTableId);
                mediaAndAttachedVoList.add(mediaAndAttachedVo);
                continue;
            }
            //添加新记录
            List<MediaAndAttachedVo> mediaAndAttachedVoList = new ArrayList<>();
            mediaAndAttachedVoList.add(mediaAndAttachedVo);
            map.put(fkTableId, mediaAndAttachedVoList);
        }
        return map;
    }

    /**
     * @Description: 根据表ids获取媒体附件
     * @Author: Jerry
     * @Date:9:34 2021/9/7
     */
    private List<SaleMediaAndAttached> getMediaAndAttachedsByFkTableIds(String fkTableName, Set<Long> fkTableIds) throws GetServiceException {
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTableName", fkTableName);
//        criteria.andIn("fkTableId", fkTableIds);
//        example.orderBy("indexKey").desc();
//        return attachedMapper.selectByExample(example);

        return attachedMapper.selectList(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                .eq(SaleMediaAndAttached::getFkTableName, fkTableName)
                .in(SaleMediaAndAttached::getFkTableId, fkTableIds).orderByDesc(SaleMediaAndAttached::getIndexKey));
    }

    @Override
    public int getItemMediaCount(SaleMediaAndAttached attached) {
        if (GeneralTool.isEmpty(attached)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        return attachedMapper.getItemMediaCount(attached);
    }

    @Override
    public List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        LambdaQueryWrapper<SaleMediaAndAttached> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
            lambdaQueryWrapper.eq(SaleMediaAndAttached::getTypeKey, attachedVo.getTypeKey());
        }else {
            if(GeneralTool.isNotEmpty(attachedVo.getTypeKeyList())){
                lambdaQueryWrapper.in(SaleMediaAndAttached::getTypeKey,attachedVo.getTypeKeyList());
//                for (String typeKey:attachedVo.getTypeKeyList()){
//                   // lambdaQueryWrapper.eq(SaleMediaAndAttached::getTypeKey, attachedVo.getTypeKey());
//
//                }
            }

        }
//        lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_CONTRACT.key);//这里不能指定表名
        lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableName,attachedVo.getFkTableName());
        lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableId, attachedVo.getFkTableId());
        lambdaQueryWrapper.orderByDesc(SaleMediaAndAttached::getIndexKey);
        IPage<SaleMediaAndAttached> iPage = attachedMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<SaleMediaAndAttached> mediaAndAttacheds = iPage.getRecords();
        page.setAll((int) iPage.getTotal());
        return getFileMedia(mediaAndAttacheds);
    }

    @Override
    public List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo, Page page, String... typeKeys) {
        if (GeneralTool.isEmpty(attachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        LambdaQueryWrapper<SaleMediaAndAttached> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(typeKeys)) {
            lambdaQueryWrapper.in(SaleMediaAndAttached::getTypeKey, typeKeys);
        }
//        lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_CONTRACT.key);//这里不能指定表名
        lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableName,attachedVo.getFkTableName());
        lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableId, attachedVo.getFkTableId());
        lambdaQueryWrapper.orderByDesc(SaleMediaAndAttached::getIndexKey);
        IPage<SaleMediaAndAttached> iPage = attachedMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<SaleMediaAndAttached> mediaAndAttacheds = iPage.getRecords();
        page.setAll((int) iPage.getTotal());
        return getFileMedia(mediaAndAttacheds);
    }

    @Override
    public List<MediaAndAttachedVo> getMediaAndAttachedDtos(List<Long> fkTableIds, String fkTableName, String fkTypeKey) {
        if (GeneralTool.isEmpty(fkTableIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SaleMediaAndAttached> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(fkTypeKey)) {
            lambdaQueryWrapper.eq(SaleMediaAndAttached::getTypeKey, fkTypeKey);
        }
        lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableName, fkTableName);
        lambdaQueryWrapper.in(SaleMediaAndAttached::getFkTableId, fkTableIds);
        lambdaQueryWrapper.orderByDesc(SaleMediaAndAttached::getIndexKey);
        List<SaleMediaAndAttached> saleMediaAndAttacheds = attachedMapper.selectList(lambdaQueryWrapper);
        return getFileMedia(saleMediaAndAttacheds);
    }

    @Override
    public List<MediaAndAttachedVo> getMediaAndAttachedDtos(List<Long> fkTableIds, String fkTableName, String fkTypeKey, Page page) {
        if (GeneralTool.isEmpty(fkTableIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SaleMediaAndAttached> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(fkTypeKey)){
            lambdaQueryWrapper.eq(SaleMediaAndAttached::getTypeKey, fkTypeKey);
        }
        lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableName, fkTableName);
        lambdaQueryWrapper.in(SaleMediaAndAttached::getFkTableId, fkTableIds);
        lambdaQueryWrapper.orderByDesc(SaleMediaAndAttached::getIndexKey);
        IPage<SaleMediaAndAttached> iPage = attachedMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<SaleMediaAndAttached> mediaAndAttacheds = iPage.getRecords();
        List<MediaAndAttachedVo> resultlist=getFileMedia(mediaAndAttacheds);
        if(GeneralTool.isNotEmpty(resultlist)){
            page.setAll((int) resultlist.size());
        }
        return resultlist;
    }

    @Override
    public void updateTableId(Long id, Long tableId) {
        SaleMediaAndAttached mediaAndAttached = new SaleMediaAndAttached();
        mediaAndAttached.setFkTableId(tableId);
        mediaAndAttached.setId(id);
        attachedMapper.updateById(mediaAndAttached);
    }

    @Override
    public void deleteMediaAndAttachedByTableId(Long tableId, String tableName) {
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTableId", tableId);
//        criteria.andEqualTo("fkTableName", tableName);
//        attachedMapper.deleteByExample(example);
        attachedMapper.delete(Wrappers.<SaleMediaAndAttached>lambdaQuery().eq(SaleMediaAndAttached::getFkTableId, tableId).eq(SaleMediaAndAttached::getFkTableName, tableName));
    }


    @Override
    public List<Map<String, Object>> findAgentMediaType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.SALEAGENT);
    }

    @Override
    public List<Map<String, Object>> findContractMediaType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.SALECONTRACT);
    }

    @Override
    public void movingOrder(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        SaleMediaAndAttached ro = BeanCopyUtils.objClone(mediaAttachedVos.get(0), SaleMediaAndAttached::new);
        Integer oneorder = ro.getIndexKey();
        SaleMediaAndAttached rt = BeanCopyUtils.objClone(mediaAttachedVos.get(1), SaleMediaAndAttached::new);
        Integer twoorder = rt.getIndexKey();
        ro.setIndexKey(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setIndexKey(oneorder);
        utilService.updateUserInfoToEntity(rt);
        attachedMapper.updateById(ro);
        attachedMapper.updateById(rt);
    }


    /**
     * 如果有学生是issue 才会调用这个
     *
     * @param attachedVo
     * @return
     * @throws
     */
    private List<MediaAndAttachedVo> getMediaAndAttachedsApp(MediaAndAttachedDto attachedVo, Long fkTableId) {
        if (GeneralTool.isEmpty(attachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        LambdaQueryWrapper<SaleMediaAndAttached> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
            lambdaQueryWrapper.eq(SaleMediaAndAttached::getTypeKey, attachedVo.getTypeKey());
        }
        lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableName, attachedVo.getTypeKey());
        lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableId, attachedVo.getFkTableId());
        lambdaQueryWrapper.orderByDesc(SaleMediaAndAttached::getIndexKey);
        List<MediaAndAttachedVo> mediaAndAttachedVos = BeanCopyUtils.copyListProperties(attachedMapper.selectList(lambdaQueryWrapper), MediaAndAttachedVo::new);
        mediaAndAttachedVos.stream().filter(Objects::nonNull).forEach(d -> d.setIsAppFile(false));
        //TODO 无用表
//        List<MediaAndAttachedVo> studentFiles = platformConfigCenterClient.getStudentFiles(fkTableId);
//        if (GeneralTool.isNotEmpty(studentFiles)) {
//            mediaAndAttachedVos.addAll(BeanCopyUtils.copyListProperties(studentFiles, MediaAndAttachedVo::new));
//        }
        return mediaAndAttachedVos;
    }

    //如果有学生是issue 才会调用这个
    private List<MediaAndAttachedVo> getFileMediaApp(List<MediaAndAttachedVo> mediaAndAttachedList) {
        if (GeneralTool.isEmpty(mediaAndAttachedList)) {
            return null;
        }
        //获取guid集合
        List<String> guidList = mediaAndAttachedList.stream().filter(d -> d.getIsAppFile().equals(false)).map(MediaAndAttachedVo::getFkFileGuid).collect(Collectors.toList());
        //转换成DTO
        List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedList.stream().filter(d -> d.getIsAppFile().equals(false))
                .map(mediaAndAttached -> BeanCopyUtils.objClone(mediaAndAttached, MediaAndAttachedVo::new)).collect(Collectors.toList());
        //根据GUID服务调用查询
        Result<List<FileDto>> result = fileCenterClient.getFile(guidList, LoggerModulesConsts.SALECENTER);
        List<FileDto> fileDtos = new ArrayList<>();
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            fileDtos = result.getData();
        }
        //返回结果不为空时
        List<MediaAndAttachedVo> collect = new ArrayList<>();
        System.out.println(fileDtos);
        mediaAndAttachedVos.removeIf(Objects::isNull);
        fileDtos.removeIf(Objects::isNull);
        if (GeneralTool.isNotEmpty(fileDtos) && GeneralTool.isNotEmpty(mediaAndAttachedVos)) {
            //遍历查询GUID是否一致
            List<FileDto> finalFileDtos = fileDtos;
            collect = mediaAndAttachedVos.stream().map(mediaAndAttachedDto -> finalFileDtos
                    .stream()
                    .filter(fileDto -> fileDto.getFileGuid().equals(mediaAndAttachedDto.getFkFileGuid()))
                    .findFirst()
                    .map(fileDto -> {
                        mediaAndAttachedDto.setFilePath(fileDto.getFilePath());
                        mediaAndAttachedDto.setFileNameOrc(fileDto.getFileNameOrc());
                        mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedDto.getTypeKey()));
                        mediaAndAttachedDto.setFileKey(fileDto.getFileKey());
                        /*mediaAndAttachedDto.setFkTableName(null);*/
                        return mediaAndAttachedDto;
                    }).orElse(null)
            ).collect(Collectors.toList());
        }
        if (GeneralTool.isNotEmpty(mediaAndAttachedList)) {
            for (MediaAndAttachedVo mediaAndAttachedVo : mediaAndAttachedList) {
                if (mediaAndAttachedVo.getIsAppFile().equals(true)) {
                    //TODO 改过
//                    MediaAndAttachedDto mediaAndAttachedVo = mediaAndAttachedVo;
//                    mediaAndAttachedVo.setTypeValue("Issue学生资料");
//                    collect.add(mediaAndAttachedVo);
                    MediaAndAttachedVo mediaAndAttached = mediaAndAttachedVo;
                    mediaAndAttached.setTypeValue("Issue学生资料");
                    collect.add(mediaAndAttached);
                }
            }
        }
        return collect;
    }

    /**
     * 获取媒体附件
     *
     * @param attachedVo
     * @return
     * @throws GetServiceException
     */
    private List<SaleMediaAndAttached> getMediaAndAttacheds(MediaAndAttachedDto attachedVo) throws GetServiceException {
        if (GeneralTool.isEmpty(attachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        LambdaQueryWrapper<SaleMediaAndAttached> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
            lambdaQueryWrapper.eq(SaleMediaAndAttached::getTypeKey, attachedVo.getTypeKey());
        }
        lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableName, attachedVo.getFkTableName());
        lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableId, attachedVo.getFkTableId());
        lambdaQueryWrapper.orderByDesc(SaleMediaAndAttached::getIndexKey);
        return attachedMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public List<MediaAndAttachedVo> getFileMedia(List<SaleMediaAndAttached> mediaAndAttachedList) {
        if (GeneralTool.isEmpty(mediaAndAttachedList)) {
            return Collections.emptyList();
        }
        mediaAndAttachedList.removeIf(Objects::isNull);
        if (GeneralTool.isEmpty(mediaAndAttachedList)) {
            return Collections.emptyList();
        }
        //获取guid集合
        List<String> guidList = mediaAndAttachedList.stream().map(SaleMediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        //转换成DTO
        List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedList.stream()
                .map(mediaAndAttached -> BeanCopyUtils.objClone(mediaAndAttached, MediaAndAttachedVo::new)).collect(Collectors.toList());
        //根据GUID服务调用查询
        Map<String, List<String>> guidListWithTypeMap = new HashMap<>();
        guidListWithTypeMap.put(LoggerModulesConsts.SALECENTER, guidList);
        Result<List<FileDto>> fileDtoResult = fileCenterClient.findFileByGuid(guidListWithTypeMap);
        if (!fileDtoResult.isSuccess()) {
            return Collections.emptyList();
        }
        List<FileDto> fileDtos = fileDtoResult.getData();
        if (fileDtos.isEmpty()) {
            return Collections.emptyList();
        }
        //返回结果不为空时
        List<MediaAndAttachedVo> collect;
        //遍历查询GUID是否一致
        collect = mediaAndAttachedVos.stream().map(mediaAndAttachedDto -> fileDtos
                .stream()
                .filter(fileDto -> fileDto.getFileGuid().equals(mediaAndAttachedDto.getFkFileGuid()))
                .findFirst()
                .map(fileDto -> {
                    mediaAndAttachedDto.setFilePath(fileDto.getFilePath());
                    mediaAndAttachedDto.setFileNameOrc(fileDto.getFileNameOrc());
                    mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedDto.getTypeKey()));
                    mediaAndAttachedDto.setFileKey(fileDto.getFileKey());
                    mediaAndAttachedDto.setFileTypeOrc(fileDto.getFileTypeOrc());
                    /*mediaAndAttachedDto.setFkTableName(null);*/
                    return mediaAndAttachedDto;
                }).orElse(null)
        ).collect(Collectors.toList());
        collect.removeIf(Objects::isNull);
        return collect;
    }

    @Override
    public void deleteMediaAndAttached(String fkTableName, Long fkTableId) {
//        Example example = new Example(MediaAndAttached.class);
//        example.createCriteria().andEqualTo("fkTableName", fkTableName).andEqualTo("fkTableId", fkTableId);
//        attachedMapper.deleteByExample(example);
        attachedMapper.delete(Wrappers.<SaleMediaAndAttached>lambdaQuery().eq(SaleMediaAndAttached::getFkTableName, fkTableName)
                .eq(SaleMediaAndAttached::getFkTableId, fkTableId));
    }

    @Override
    public List<SaleMediaAndAttached> getMediaAndAttachedByIaeCrm() {
        LambdaQueryWrapper<SaleMediaAndAttached> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(SaleMediaAndAttached::getRemark,"AttachmentFileID");//iae文件标记
        lambdaQueryWrapper.ge(SaleMediaAndAttached::getFkTableId,17478);//大于等于17478
//        lambdaQueryWrapper.last("limit 5");//临时测试只读5条
        return attachedMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public Boolean updateMediaAndAttachedById(SaleMediaAndAttached mediaAndAttached) {
        attachedMapper.updateById(mediaAndAttached);
        return true;
    }

    @Override
    public List<MediaAndAttachedVo> getMediaAndAttachedByAgentIds(List<String> fkAgentIds_) {
        List<SaleMediaAndAttached> saleMediaAndAttacheds = new ArrayList<>();
        LambdaQueryWrapper<AgentContract> agentContractLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(GeneralTool.isNotEmpty(fkAgentIds_))
        {
            agentContractLambdaQueryWrapper.in(AgentContract::getFkAgentId,fkAgentIds_);
        }
        List<AgentContract> agentContracts = agentContractMapper.selectList(agentContractLambdaQueryWrapper);
        LambdaQueryWrapper<SaleMediaAndAttached> lambdaQueryWrapper = null;
        if(GeneralTool.isNotEmpty(agentContracts))
        {
            //按合同结束日期获取文件，如文件为空，则根据最新时间获取获取，如无，则取最新一个
            Optional<AgentContract> agentContract_endTime = agentContracts.stream().filter(item->item.getEndTime()!=null).max(Comparator.comparing(AgentContract::getEndTime));
            if(agentContract_endTime.isPresent()) {
                AgentContract agentContract_endTime_ = agentContract_endTime.get();
                List<Long> fkTableIds = new ArrayList<>();
                fkTableIds.add(agentContract_endTime_.getId());
                lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableName,"m_agent_contract");
                lambdaQueryWrapper.in(SaleMediaAndAttached::getFkTableId,fkTableIds);
                saleMediaAndAttacheds = attachedMapper.selectList(lambdaQueryWrapper);
                //移除
                if(saleMediaAndAttacheds.size()==0)
                {
                    agentContracts.remove(agentContract_endTime_);
                }
            }
            if(saleMediaAndAttacheds.size()==0)
            {
                //按修改时间获取
                Optional<AgentContract> agentContract = agentContracts.stream().filter(item->item.getGmtModified()!=null).max(Comparator.comparing(AgentContract::getGmtModified));
                if(agentContract.isPresent()) {
                    AgentContract agentContract_ = agentContract.get();
                    List<Long> fkTableIds = new ArrayList<>();
                    fkTableIds.add(agentContract_.getId());
                    lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableName, "m_agent_contract");
                    lambdaQueryWrapper.in(SaleMediaAndAttached::getFkTableId, fkTableIds);
                    saleMediaAndAttacheds = attachedMapper.selectList(lambdaQueryWrapper);
                    //移除
                    if(saleMediaAndAttacheds.size()==0)
                    {
                        agentContracts.remove(agentContract_);
                    }
                }
            }
            if(saleMediaAndAttacheds.size()==0)
            {
                //根据最新时间获取
                Optional<AgentContract> agentContract = agentContracts.stream().filter(item->item.getGmtCreate()!=null).max(Comparator.comparing(AgentContract::getGmtCreate));
                if(agentContract.isPresent())
                {
                    AgentContract agentContract_ = agentContract.get();
                    List<Long> fkTableIds = new ArrayList<>();
                    fkTableIds.add(agentContract_.getId());
                    lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableName,"m_agent_contract");
                    lambdaQueryWrapper.in(SaleMediaAndAttached::getFkTableId,fkTableIds);
                    saleMediaAndAttacheds = attachedMapper.selectList(lambdaQueryWrapper);
                }
            }
            //如有多个附件，则返回最新的
            if(saleMediaAndAttacheds.size()>1)
            {
                Optional<SaleMediaAndAttached> value = saleMediaAndAttacheds.stream().max(Comparator.comparing(SaleMediaAndAttached::getGmtCreate));
                SaleMediaAndAttached saleMediaAndAttached = value.get();
                saleMediaAndAttacheds.clear();
                saleMediaAndAttacheds.add(saleMediaAndAttached);
            }

            //如一个代理有多个合同，则取最新的合同，按最新创建时间获取
//            Optional<AgentContract> agentContract = agentContracts.stream().max(Comparator.comparing(AgentContract::getGmtCreate));
//            AgentContract agentContract_ = agentContract.get();
//
//            List<Long> fkTableIds = new ArrayList<>();
//            fkTableIds.add(agentContract_.getId());
//            lambdaQueryWrapper = new LambdaQueryWrapper<>();
//            lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableName,"m_agent_contract");
//            lambdaQueryWrapper.in(SaleMediaAndAttached::getFkTableId,fkTableIds);
//            saleMediaAndAttacheds = attachedMapper.selectList(lambdaQueryWrapper);
//            //如有多个附件，则返回最新的
//            if(saleMediaAndAttacheds.size()>1)
//            {
//                Optional<SaleMediaAndAttached> value = saleMediaAndAttacheds.stream().max(Comparator.comparing(SaleMediaAndAttached::getGmtCreate));
//                SaleMediaAndAttached saleMediaAndAttached = value.get();
//                saleMediaAndAttacheds.clear();
//                saleMediaAndAttacheds.add(saleMediaAndAttached);
//            }
        }
        return getFileMedia(saleMediaAndAttacheds);
    }

    /**
     * Author Cream
     * Description : //删除学生附件
     * Date 2023/5/11 14:25
     * Params:
     * Return
     */
    @Override
    public void deleteByStudentId(Long mergedStudentId) {
        if (GeneralTool.isNotEmpty(mergedStudentId)) {
            if (attachedMapper.selectCount(Wrappers.<SaleMediaAndAttached>lambdaQuery().eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_STUDENT.key)
                    .eq(SaleMediaAndAttached::getFkTableId, mergedStudentId)) > 0) {
                attachedMapper.delete(Wrappers.<SaleMediaAndAttached>lambdaQuery().eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_STUDENT.key)
                        .eq(SaleMediaAndAttached::getFkTableId, mergedStudentId));
            }
        }
    }

    @Override
    public List<FileDto> uploadHtiPublicFile(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
//        List<FileDto> fileDtos = null;
//        try {
//            ListResponseBo responseBo = uploadService.uploadAppendix(multipartFiles, LoggerModulesConsts.SALECENTER);
//            if (!ErrorCodeEnum.REQUEST_OK.getCode().equals(responseBo.getCode())) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
//            }
//            JSONArray jsonArray = JSONArray.fromObject(responseBo.getDatas());
//            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
//        } catch (IOException e) {
//            e.printStackTrace();
//        }

        List<FileDto> fileDtos = null;
        Result<List<FileDto>> result = fileCenterClient.uploadHtiPublicFile(multipartFiles, LoggerModulesConsts.SALECENTER, null, null);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        } else {
            throw new GetServiceException(result.getMessage());
        }
        return fileDtos;
    }

    /**
     * 批量新增附件信息
     * @param mediaAttachedVos
     * @return
     */
    @Override
    public List<MediaAndAttachedVo> addMediaAndAttachedList(List<MediaAndAttachedDto> mediaAttachedVos) {
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAttachedVo : mediaAttachedVos) {
            SaleMediaAndAttached andAttached = BeanCopyUtils.objClone(mediaAttachedVo, SaleMediaAndAttached::new);
            Integer nextIndexKey = attachedMapper.getNextIndexKey(mediaAttachedVo.getFkTableId(), mediaAttachedVo.getFkTableName());
            nextIndexKey = GeneralTool.isNotEmpty(nextIndexKey) ? nextIndexKey : 0;
            //实例化对象
            andAttached.setIndexKey(nextIndexKey);
            if (GeneralTool.isEmpty(andAttached.getGmtCreateUser())) {
                utilService.updateUserInfoToEntity(andAttached);
            }
            attachedMapper.insert(andAttached);
            MediaAndAttachedVo mediaAndAttachedVo = BeanCopyUtils.objClone(andAttached, MediaAndAttachedVo::new);
            mediaAndAttachedVo.setFilePath(mediaAttachedVo.getFilePath());
            mediaAndAttachedVo.setFileNameOrc(mediaAttachedVo.getFileNameOrc());
            mediaAndAttachedVo.setTypeValue(FileTypeEnum.getValue(mediaAttachedVo.getTypeKey()));
            mediaAndAttachedVo.setId(andAttached.getId());
            mediaAndAttachedVo.setFileKey(mediaAttachedVo.getFileKey());
            mediaAndAttachedVos.add(mediaAndAttachedVo);
        }
        return mediaAndAttachedVos;
    }

    /**
     * 复制partner学生附件
     * @param studentId
     * @param partnerStudentId
     * @return
     */
    @Override
    public Boolean copyPartnerStudentAttached(Long studentId, Long partnerStudentId) {
        List<SaleMediaAndAttached> saleMediaAndAttacheds = attachedMapper.selectList(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                .eq(SaleMediaAndAttached::getFkTableId, partnerStudentId)
                .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_APP_STUDENT.key));
        if (GeneralTool.isNotEmpty(saleMediaAndAttacheds)) {
            for (SaleMediaAndAttached saleMediaAndAttached : saleMediaAndAttacheds) {
                saleMediaAndAttached.setId(null);
                saleMediaAndAttached.setFkTableName(TableEnum.SALE_STUDENT.key);
                saleMediaAndAttached.setFkTableId(studentId);
                utilService.setCreateInfo(saleMediaAndAttached);
                attachedMapper.insert(saleMediaAndAttached);
            }
        }
        return true;
    }

}
