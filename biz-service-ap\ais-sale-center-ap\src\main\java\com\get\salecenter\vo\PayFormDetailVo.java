package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * Time: 10:07
 * Date: 2022/5/13
 * Description:付款详情Dto
 */
@Data
public class PayFormDetailVo {
    @ApiModelProperty("币种名称")
    private String payableCurrencyTypeName;
    @ApiModelProperty("实收金额")
    private BigDecimal actualPayableAmount;
    @ApiModelProperty("付款时间")
    private Date actualPayTime;
    @ApiModelProperty("申请计划id")
    private Long fkStudentOfferItemId;

}
