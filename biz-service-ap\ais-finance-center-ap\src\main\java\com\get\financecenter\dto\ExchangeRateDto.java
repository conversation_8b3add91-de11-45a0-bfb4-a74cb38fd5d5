package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: Sea
 * @create: 2020/12/21 16:53
 * @verison: 1.0
 * @description:
 */
@Data
public class ExchangeRateDto  extends BaseVoEntity {
    /**
     * 参照币种编号
     */
    @ApiModelProperty(value = "参照币种编号")
    private String fkCurrencyTypeNumFrom;

    /**
     * 目标币种编号
     */
    @ApiModelProperty(value = "目标币种编号")
    private String fkCurrencyTypeNumTo;

    /**
     * 获取日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "获取日期")
    private Date getDate;

    /**
     * 汇率
     */
    @ApiModelProperty(value = "汇率")
    private BigDecimal exchangeRate;
}
