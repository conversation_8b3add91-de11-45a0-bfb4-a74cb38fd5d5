package com.get.financecenter.service;


/**
 * <AUTHOR>
 * @DATE: 2020/11/25
 * @TIME: 14:39
 * @Description:
 **/
public interface IDeleteService {

    /**
     * @return java.lang.Boolean
     * @Description :删除供应商数据校验
     * @Param [providerId]
     * <AUTHOR>
     */
    Boolean deleteValidateProvider(Long providerId);


    /**
     * @return void
     * @Description :删除关联附件
     * @Param [agentId, tableName]
     * <AUTHOR>
     */
    void deleteMedia(Long id, String tableName);
}
