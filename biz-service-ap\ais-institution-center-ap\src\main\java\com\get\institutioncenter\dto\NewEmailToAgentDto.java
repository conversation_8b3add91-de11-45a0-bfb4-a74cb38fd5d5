package com.get.institutioncenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/7/4 15:32
 * @desciption:
 */
@Data
public class NewEmailToAgentDto extends BaseVoEntity {
    @ApiModelProperty(value = "步骤ids")
    @NotNull(message = "步骤ids不能为空", groups = {Update.class})
    private Set<Long> stepIds;

    @ApiModelProperty(value = "入学时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date intakeTimeStart;

    @ApiModelProperty(value = "入学时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date intakeTimeEnd;

}
