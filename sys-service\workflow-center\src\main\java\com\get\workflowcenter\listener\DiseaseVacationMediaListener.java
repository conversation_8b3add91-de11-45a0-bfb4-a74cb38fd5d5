package com.get.workflowcenter.listener;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.officecenter.entity.LeaveApplicationForm;
import com.get.officecenter.feign.IOfficeCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.workflowcenter.component.IWorkFlowHelper;
import lombok.SneakyThrows;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2022/8/19 14:48
 * @verison: 1.0
 * @description:
 */
public class DiseaseVacationMediaListener implements TaskListener {


    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @SneakyThrows
    @Override
    public void notify(DelegateTask delegateTask) {

        System.out.println("进入有无附件监听--------------------------------");
        //获取feign调用service
        IPermissionCenterClient permissionCenterClient = SpringUtil.getBean(IPermissionCenterClient.class);
        IOfficeCenterClient officeCenterClient = SpringUtil.getBean(IOfficeCenterClient.class);
        IWorkFlowHelper workFlowHelper = SpringUtil.getBean(IWorkFlowHelper.class);

        TaskService taskService = SpringUtil.getBean(TaskService.class);
        RuntimeService runtimeService = SpringUtil.getBean(RuntimeService.class);

        ProcessEngine processEngine = SpringUtil.getBean(ProcessEngine.class);

        ProcessDefinition processDefinition = processEngine.getRepositoryService()
                .createProcessDefinitionQuery()
                .processDefinitionId(delegateTask.getProcessDefinitionId())
                .singleResult();
        StaffVo staffVo = workFlowHelper.getStaffDto(delegateTask);


        //判断是否进入资料审批员
        String businessKey = "";
        String processInstanceBusinessKey = delegateTask.getExecution().getProcessInstanceBusinessKey();
        if (GeneralTool.isEmpty(processInstanceBusinessKey)){
            List<ProcessInstance> processInstances = runtimeService.createProcessInstanceQuery().processInstanceId(delegateTask.getProcessInstanceId()).list();
            for (ProcessInstance processInstance : processInstances) {
                if (GeneralTool.isNotEmpty(processInstance.getBusinessKey())) {
                    businessKey = processInstance.getBusinessKey();
                    break;
                }
            }
        }else {
            businessKey = processInstanceBusinessKey;
        }
        LeaveApplicationForm data = officeCenterClient.getLeaveApplicationForm(Long.valueOf(businessKey)).getData();
        String typeKey = officeCenterClient.getLeaveTypeKeyById(data.getId());
        if (ProjectKeyEnum.DISEASE_VACATION.key.equals(typeKey)) {
            Boolean b = officeCenterClient.hasMediaAndAttach(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key, data.getId()).getData();
            if (!b) {
                List<String> staffIds =  new ArrayList<>(1);
                staffIds.add(String.valueOf(staffVo.getId()));
                workFlowHelper.sendMessage(staffVo,staffIds,processDefinition,"附件待上传",delegateTask,null,null);

                Set<String> nums = new HashSet<>(1);
                nums.add("ZLSH01");
                List<Long> ids = permissionCenterClient.getStaffIdsByPositionNums(nums);
                if (GeneralTool.isNotEmpty(ids)) {
                    List<String> stringIds = ids.stream().map(String::valueOf).collect(Collectors.toList());
                    workFlowHelper.sendMessage(staffVo,stringIds,processDefinition,"该申请缺少资料",delegateTask,null,null);
                }
            }
        }
    }
}