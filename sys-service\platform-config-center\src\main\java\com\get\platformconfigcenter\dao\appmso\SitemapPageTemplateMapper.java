package com.get.platformconfigcenter.dao.appmso;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.SitemapPageTemplate;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/12/3
 * @TIME: 16:08
 * @Description:
 **/
@Mapper
@DS("appmsodb")
public interface SitemapPageTemplateMapper extends BaseMapper<SitemapPageTemplate> {
    /**
     * 页面模板下拉
     *
     * @return
     */
//    List<SitemapPageTemplate> sitemapPageTemplate();

}
