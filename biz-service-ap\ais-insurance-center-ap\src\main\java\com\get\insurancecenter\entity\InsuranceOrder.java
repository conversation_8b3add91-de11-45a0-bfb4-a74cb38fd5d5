package com.get.insurancecenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:保险订单
 */
@Data
@TableName("m_insurance_order")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsuranceOrder extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "租户Id")
    private Long fkTenantId;

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "学生代理Id")
    private Long fkAgentId;

    @ApiModelProperty(value = "伙伴用户Id")
    private Long fkPartnerUserId;

    @ApiModelProperty(value = "保险公司Id")
    private Long fkInsuranceCompanyId;

    @ApiModelProperty(value = "保险产品类型Id")
    private Long fkProductTypeId;

    @ApiModelProperty(value = "系统订单编号")
    private String orderNum;

    @ApiModelProperty(value = "保险单号")
    private String insuranceNum;

    @ApiModelProperty(value = "保险单类型：Single/Couple/Family")
    private String insuranceType;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "保单金额")
    private BigDecimal insuranceAmount;

    @ApiModelProperty(value = "受保人姓名")
    private String insurantName;

    @ApiModelProperty(value = "受保人姓（英/拼音）")
    private String insurantLastName;

    @ApiModelProperty(value = "受保人名（英/拼音）")
    private String insurantFirstName;

    @ApiModelProperty(value = "受保人性别")
    private String insurantGender;

    @ApiModelProperty(value = "受保人国籍")
    private String insurantNationality;

    @ApiModelProperty(value = "受保人生日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insurantBirthday;

    @ApiModelProperty(value = "受保人护照号")
    private String insurantPassportNum;

    @ApiModelProperty(value = "前往国家Id")
    private Long fkAreaCountryIdTo;

    @ApiModelProperty(value = "保单开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-d", timezone = "GMT+8")
    private Date insuranceStartTime;

    @ApiModelProperty(value = "保单结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceEndTime;

    @ApiModelProperty(value = "入学时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date enrollmentTime;

    @ApiModelProperty(value = "毕业时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date graduationTime;

    @ApiModelProperty(value = "订单明细json")
    private String orderJson;

    @ApiModelProperty(value = "备注（后台用户备注信息）")
    private String remark;

    @ApiModelProperty(value = "保单备注（用户下单时填写）")
    private String orderRemark;

    @ApiModelProperty(value = "订单状态：等待下单0/下单中1/下单成功2/下单失败-2")
    private Integer orderStatus;

    @ApiModelProperty(value = "订单信息（系统反馈记录）")
    private String orderMessage;

    @ApiModelProperty(value = "受保人Email")
    private String insurantEmail;

    @ApiModelProperty(value = "受保人移动电话")
    private String insurantMobile;

    @ApiModelProperty(value = "受保人手机区号")
    private String insurantMobileAreaCode;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "成交时间（保险公司系统成功下单时间）")
    private Date orderTime;

    @ApiModelProperty(value = "保险产品类型Key")
    @TableField(exist = false)
    private String productTypeKey;

    @ApiModelProperty(value = "微信支付的用户openid，支付时生成，和订单一一对应，若支付失败，需要重新生成订单")
    private String mpPaymentOpenid;

    @ApiModelProperty(value = "微信支付状态：未支付0/支付成功1/支付失败-1")
    private Integer mpPaymentStatus;

    @ApiModelProperty(value = "支付类型：1信用卡/2微信支付")
    private Integer paymentType;

    @ApiModelProperty(value = "信用卡Id")
    private Long fkCreditCardId;
}

