package com.get.salecenter.service;

import com.get.salecenter.dto.WechatOrderParamDto;

import java.util.Map;

public interface WechatService {

    /**
     * 生成授权链接
     * @return
     */
    String getAuthUrl();

    /**
     * 获取用户openId
     *
     * @param code
     * @param state
     * @return
     */
    String getOpenId(String code, String state);

    /**
     * JSAPI下单
     * @param wechatOrderParamDto
     * @return
     */
    Map<String, Object> createOrder(WechatOrderParamDto wechatOrderParamDto);

}
