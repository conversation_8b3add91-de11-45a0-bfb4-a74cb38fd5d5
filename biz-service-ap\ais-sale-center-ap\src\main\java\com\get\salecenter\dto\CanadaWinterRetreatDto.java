package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 加拿大冬季Retreat报名
 *
 * <AUTHOR>
 * @date 2023/7/7 15:53
 */
@Data
public class CanadaWinterRetreatDto {

    @ApiModelProperty(value = "联系人姓名")
    @NotNull(message = "联系人姓名不能为空")
    private String contactPersonName;


    @ApiModelProperty(value = "机构名")
    @NotNull(message = "机构名不能为空")
    private String providerName;

    @ApiModelProperty(value = "参加人电邮")
    @NotNull(message = "参加人电邮不能为空")
    private String email;

    @ApiModelProperty(value = "参加人电话")
    @NotNull(message = "电话不能为空")
    private String tel;

    @ApiModelProperty(value = "选项")
    @NotNull(message = "选项不能为空")
    private String itemName;

    @ApiModelProperty(value = "费用摘要")
    @NotNull(message = "摘要不能为空")
    private String summaryFee;



}
