package com.get.salecenter.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import com.get.financecenter.vo.AlreadyPayVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/11/23
 * @TIME: 12:38
 * @Description:
 **/
@Data
public class PayablePlanVo extends BaseEntity {

    @ApiModelProperty(value = "应付类型名称")
    private String fkTypeName;

    @ApiModelProperty(value = "应付类型对应记录名称")
    private String fkTypeTargetName;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    /**
     * 提供商id
     */
    @ApiModelProperty(value = "提供商id")
    private Long fkInstitutionProviderId;


    /**
     * 提供商名称
     */
    @ApiModelProperty(value = "提供商名称")
    private String fkInstitutionProviderName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;
    /**
     * 延迟入学标记
     */
    @ApiModelProperty(value = "延迟入学标记")
    private Boolean isDeferEntrance;


    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String fkCourseName;


    /**
     * 课程长度
     */
    @ApiModelProperty(value = "课程长度")
    private BigDecimal duration;

    /**
     * 课程长度类型(0周、1学期、2年)
     */
    @ApiModelProperty(value = "课程长度类型(0周、1学期、2年)")
    private Integer durationType;

    /**
     * 折合币种名称
     */
    @ApiModelProperty(value = "折合币种名称")
    private String equivalentCurrencyTypeName;
    /**
     * 代理名称
     */
    @ApiModelProperty(value = "应收计划列表代理名称")
    private String fkAgentName;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;

    @ApiModelProperty(value = "实付币种名称")
    private String actualPayableCurrencyName;

    @ApiModelProperty(value = "实付汇率")
    private BigDecimal actualPayableExchangeRate;

    /**
     * 记录总名称
     */
    @ApiModelProperty(value = "记录总名称")
    private String targetNames;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 目标公司名称
     */
    @ApiModelProperty(value = "目标公司名称")
    private String targetCompanyName;

    /**
     * 目标公司Id
     */
    @ApiModelProperty(value = "目标公司Id")
    private List<Long> targetCompanyNameId;

    /**
     * 学生id
     */
    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;

    @ApiModelProperty(value = "学生号")
    private String studentId;

    /**
     * 付款金额（折合应付币种金额）
     */
    @ApiModelProperty(value = "付款金额（折合应付币种金额）")
    private BigDecimal amountPayable;


    @ApiModelProperty(value = "实付折算")
    private BigDecimal paidInConversion;


    /**
     * 差额
     */
    @ApiModelProperty(value = "差额")
    private BigDecimal difference;

    /**
     * 汇率调整（可正可负，为平衡计算应付金额）
     */
    @ApiModelProperty(value = "汇率调整（可正可负，为平衡计算应付金额）")
    private BigDecimal amountExchangeRate;

    /**
     * 实付金额
     */
    @ApiModelProperty(value = "实付金额")
    private BigDecimal actualPayableAmount;

    /**
     * 差额
     */
    @ApiModelProperty(value = "差额")
    private BigDecimal diffPayableAmount;

    /**
     * 付款状态：0/1/2：未付/已付部分/已付齐
     */
    @ApiModelProperty(value = "付款状态：0/1/2：未付/已付部分/已付齐")
    private Integer payableStatus;

    /**
     * 代理id
     */
    @ApiModelProperty(value = "代理id")
    private Long fkAgentId;

    @ApiModelProperty(value = "代理名")
    private String agentName;

    @ApiModelProperty(value = "学生信息")
    private String studentInformation;

    @ApiModelProperty(value = "业务信息")
    private String businessInformation;


    /**
     * 旧系统学校名称
     */
    @ApiModelProperty(value = "旧系统学校名称")
    @Column(name = "old_institution_name")
    private String oldInstitutionName;

    /**
     * 旧系统学校全称
     */
    @ApiModelProperty(value = "旧系统学校全称")
    @Column(name = "old_institution_full_name")
    private String oldInstitutionFullName;

    /**
     * 旧系统课程名称
     */
    @ApiModelProperty(value = "旧系统课程名称")
    @Column(name = "old_course_custom_name")
    private String oldCourseCustomName;


    @ApiModelProperty(value = "银行账户id")
    private Long fkAgentContractAccountId;

    @ApiModelProperty(value = "代理公司id")
    private Long agentCompanyId;

    @ApiModelProperty(value = "银行账户币种")
    private String accountCurrencyTypeNum;

    @ApiModelProperty(value = "渠道信息")
    private String channelInformation;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    private Long fkInstitutionCourseId;

    /**
     * 渠道来源Id
     */
    @ApiModelProperty(value = "渠道来源Id")
    private Long fkInstitutionChannelId;

    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String fkInstitutionChannelName;

    /**
     * 申请方案Id
     */
    @ApiModelProperty(value = "申请方案Id")
    private Long fkStudentOfferId;
    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 应付计划的目标对象对应的应收计划列表（receivableAmount-应收金额、actualReceivableAmount-实收金额、diffReceivableAmount-收款差额）
     */
    @ApiModelProperty(value = "应付计划的目标对象对应的应收计划列表（receivableAmount-应收金额、actualReceivableAmount-实收金额、diffReceivableAmount-收款金额）")
    private List<Map<String,Object>> receivablePlanList;

    @ApiModelProperty(value = "实际支付金额")
    private BigDecimal amountActual;

    /**
     * 是否后续课程：0否/1是
     */
    @ApiModelProperty(value = "是否后续课程")
    private Boolean isFollow;

    /**
     * 父计划id
     */
    @ApiModelProperty(value = "父计划id")
    private Long fkParentStudentOfferItemId;

    @ApiModelProperty(value = "实际手续费金额")
    private BigDecimal serviceFeeActual;

    @ApiModelProperty(value = "申请备注")
    private String appRemark;

    @ApiModelProperty(value = "实付信息")
    private List<PublicPayFormDetailVo> payFormDetailDtos;


    /**
     * 收款单子项Id（比对这个收款记录id，如果存在，不需要再创建）
     */
    @ApiModelProperty(value = "收款单子项Id（比对这个收款记录id，如果存在，不需要再创建）")
    @Column(name = "fk_receipt_form_item_id")
    private Long fkReceiptFormItemId;


    /**
     * 汇率（折合港币）
     */
    @ApiModelProperty(value = "汇率（折合港币）")
    private BigDecimal exchangeRateHkd;

    /**
     * 付款金额（折合港币）
     */
    @ApiModelProperty(value = "付款金额（折合港币）")
    private BigDecimal amountHkd;

    /**
     * 汇率（折合人民币）
     */
    @ApiModelProperty(value = "汇率（折合人民币）")
    private BigDecimal exchangeRateRmb;

    /**
     * 付款金额（折合人民币）
     */
    @ApiModelProperty(value = "付款金额（折合人民币）")
    private BigDecimal amountRmb;

    @ApiModelProperty(value = "已付")
    List<AlreadyPayVo> alreadyPayDtos;

    //===========实体类PayablePlan==================


    /**
     * 应付类型关键字，枚举，如：m_student_offer_item
     */
    @ApiModelProperty(value = "应付类型关键字，枚举，如：m_student_offer_item")
    @Column(name = "fk_type_key")
    private String fkTypeKey;

    /**
     * 应付类型对应记录Id，如：m_student_offer_item.id
     */
    @ApiModelProperty(value = "应付类型对应记录Id，如：m_student_offer_item.id")
    @Column(name = "fk_type_target_id")
    private Long fkTypeTargetId;

    /**
     * 应收计划Id
     */
    @ApiModelProperty(value = "应收计划Id")
    @Column(name = "fk_receivable_plan_id")
    private Long fkReceivablePlanId;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    @Column(name = "summary")
    private String summary;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;

    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    @Column(name = "tuition_amount")
    private BigDecimal tuitionAmount;

    /**
     * 费率%(代理)
     */
    @ApiModelProperty(value = "费率%(代理)")
    @Column(name = "commission_rate")
    private BigDecimal commissionRate;

    /**
     * 代理分成比率%
     */
    @ApiModelProperty(value = "代理分成比率%")
    @Column(name = "split_rate")
    private BigDecimal splitRate;

    /**
     * 佣金金额(代理)
     */
    @ApiModelProperty(value = "佣金金额(代理)")
    @Column(name = "commission_amount")
    private BigDecimal commissionAmount;

    /**
     * 定额金额
     */
    @ApiModelProperty(value = "定额金额")
    @Column(name = "fixed_amount")
    private BigDecimal fixedAmount;

    /**
     * 其他金额
     */
    @ApiModelProperty(value = "其他金额")
    @Column(name = "bonus_amount")
    private BigDecimal bonusAmount;

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    @Column(name = "payable_amount")
    private BigDecimal payableAmount;

    /**
     * 计划付款时间
     */
    @ApiModelProperty(value = "计划付款时间")
    @Column(name = "payable_plan_date")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date payablePlanDate;

    /**
     * 是否预付，0否/1是
     */
    @ApiModelProperty(value = "是否预付，0否/1是")
    @Column(name = "is_pay_in_advance")
    private Boolean isPayInAdvance;

    @ApiModelProperty(value = "预付百分比：50, 100")
    @UpdateWithNull
    private Integer payInAdvancePercent;

    /**
     * 状态：0关闭/1打开/2完成
     */
    @ApiModelProperty(value = "状态：0关闭/1打开/2完成")
    @Column(name = "status")
    private Integer status;

    /**
     * 结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总
     */
    @ApiModelProperty(value = "结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总")
    @Column(name = "status_settlement")
    private Integer statusSettlement;

    /**
     * 旧数据财务id(gea)
     */
    @ApiModelProperty(value = "旧数据财务id(gea)")
    @Column(name = "id_gea_finance")
    private String idGeaFinance;

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("代理标签")
    private List<AgentLabelVo> agentLabelVos;

}
