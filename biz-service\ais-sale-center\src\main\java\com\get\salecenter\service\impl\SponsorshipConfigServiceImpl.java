package com.get.salecenter.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.salecenter.dao.sale.SponsorshipConfigMapper;
import com.get.salecenter.vo.SponsorshipConfigVo;
import com.get.salecenter.entity.SponsorshipConfig;
import com.get.salecenter.service.IAnnualConferenceRegistrationSponsorshipService;
import com.get.salecenter.service.ISponsorshipConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2021/4/30 17:23
 * @verison: 1.0
 * @description:
 */
@Service
public class SponsorshipConfigServiceImpl implements ISponsorshipConfigService {
    @Resource
    private SponsorshipConfigMapper sponsorshipConfigMapper;
    @Resource
    private IAnnualConferenceRegistrationSponsorshipService annualConferenceRegistrationSponsorshipService;

    @Override
    public List<Map<String, List<SponsorshipConfigVo>>> getSponsorshipConfig() {
        //查出全部赞助信息
        List<SponsorshipConfig> sponsorshipConfigs = sponsorshipConfigMapper.selectList(Wrappers.<SponsorshipConfig>lambdaQuery());
        //定义返回结果对象
        List<Map<String, List<SponsorshipConfigVo>>> list = new ArrayList<>();
        Set<String> set = new HashSet<>();
        //收集去重后的类型集合
        sponsorshipConfigs.forEach(sponsorshipConfig -> set.add(sponsorshipConfig.getType()));
        //每个类型作为key创建一个map放入list  key：类型  value：赞助对象集合
        for (String type : set) {
            Map<String, List<SponsorshipConfigVo>> map = new HashMap<>();
            //定义存在map中的集合
            List<SponsorshipConfigVo> sponsorshipConfigVoList = new ArrayList<>();
            map.put(type, sponsorshipConfigVoList);
            list.add(map);
        }
        //获取每一个赞助信息，看他是哪个类型的
        for (SponsorshipConfig sponsorshipConfig : sponsorshipConfigs) {
            SponsorshipConfigVo sponsorshipConfigVo = BeanCopyUtils.objClone(sponsorshipConfig, SponsorshipConfigVo::new);
            //是否售空
            Boolean result = annualConferenceRegistrationSponsorshipService.soldOut(sponsorshipConfig.getId(), sponsorshipConfig.getInitNum());
            sponsorshipConfigVo.setSoldOut(result);
            for (Map<String, List<SponsorshipConfigVo>> listMap : list) {
                //取出map，如果这个赞助对象的类型能从这个map中获取[] 表示这个赞助对象属于这个map，加入value
                List<SponsorshipConfigVo> sponsorshipConfigVos = listMap.get(sponsorshipConfigVo.getType());
                if (sponsorshipConfigVos != null) {
                    sponsorshipConfigVos.add(sponsorshipConfigVo);
                }
            }
        }
        return list;
    }

    @Override
    public String getSponsorshipConfigType(Long fkSponsorshipConfigId) {
        SponsorshipConfig sponsorshipConfig = sponsorshipConfigMapper.selectById(fkSponsorshipConfigId);
        return sponsorshipConfig.getType();
    }
}
