package com.get.salecenter.service;


import com.get.salecenter.dto.AgentContractFormulaMajorLevelDto;

import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2021/1/6 15:08
 * @verison: 1.0
 * @description:
 */
public interface IAgentContractFormulaMajorLevelService {
    /**
     * @return void
     * @Description :新增
     * @Param [agentContractFormulaMajorLevelDto]
     * <AUTHOR>
     */
    Long addAgentContractFormulaMajorLevel(AgentContractFormulaMajorLevelDto agentContractFormulaMajorLevelDto);

    /**
     * @return void
     * @Description :删除
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long agentContractFormulaId);

    /**
     * @Description :通过学生代理合同公式id 查找对应课程等级名称
     * @Param [agentContractFormulaIds]
     * <AUTHOR>
     */
    Map<Long, String> getMajorLevelNameMapByFkids(List<Long> agentContractFormulaIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过学生代理合同公式id 查找对应课程等级ids
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    List<Long> getMajorLevelIdListByFkid(Long agentContractFormulaId);
}
