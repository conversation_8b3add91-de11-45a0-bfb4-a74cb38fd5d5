package com.get.salecenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.salecenter.vo.EventPlanThemeVo;
import com.get.salecenter.entity.EventPlanTheme;
import com.get.salecenter.dto.EventPlanThemeDto;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
public interface EventPlanThemeService extends BaseService<EventPlanTheme> {

    /**
     * 列表数据
     * <AUTHOR>
     * @DateTime 2023/12/14 10:43
     */
    List<EventPlanThemeVo> getEventPlanThemes(Long fkEventPlanId);

    /**
     * 详情
     * <AUTHOR>
     * @DateTime 2023/12/14 12:43
     */
    EventPlanThemeVo findEventPlanThemeById(Long id);

    /**
     * 新增
     * <AUTHOR>
     * @DateTime 2023/12/14 10:57
     */
    Long addEventPlanTheme(EventPlanThemeDto vo);

    /**
     * 删除
     * <AUTHOR>
     * @DateTime 2023/12/14 10:58
     */
    void delete(Long id);

    /**
     * 修改
     * <AUTHOR>
     * @DateTime 2023/12/14 10:59
     */
    EventPlanThemeVo updateEventPlanTheme(EventPlanThemeDto vo);

    /**
     * 移动
     * <AUTHOR>
     * @DateTime 2023/12/14 11:02
     */
    void movingOrder( Long fkEventPlanId,Integer start,Integer end);

}
