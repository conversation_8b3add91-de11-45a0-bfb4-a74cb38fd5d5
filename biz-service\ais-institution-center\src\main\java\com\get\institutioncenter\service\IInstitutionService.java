package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.bo.InstitutionApplicationStaticsQueryBo;
import com.get.institutioncenter.vo.*;
import com.get.institutioncenter.entity.BaseSelectEntityPlus;
import com.get.institutioncenter.entity.Institution;
import com.get.institutioncenter.dto.*;
import com.get.institutioncenter.dto.query.InstitutionQueryDto;
import com.get.institutioncenter.dto.query.InstitutionZoneQueryDto;
import com.get.institutioncenter.dto.query.NewsQueryDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/13 15:38
 * @verison: 1.0
 * @description:
 */
public interface IInstitutionService extends BaseService<Institution> {
    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionVo>
     * @Description: 列表
     * @Param [institutionVo, page]
     * <AUTHOR>
     */
    List<InstitutionVo> datas(InstitutionQueryDto institutionVo, Page page);

    /**
     * @return java.lang.Long
     * @Description: 保存
     * @Param [institutionDto]
     * <AUTHOR>
     */
    Long addInstitution(InstitutionDto institutionDto);

    /**
     * @return com.get.institutioncenter.vo.InstitutionVo
     * @Description: 修改
     * @Param [institutionDto]
     * <AUTHOR>
     */
    InstitutionVo updateInstitution(InstitutionDto institutionDto);


    /**
     * @return com.get.institutioncenter.vo.InstitutionVo
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     **/
    InstitutionVo findInstitutionById(Long id);


    /**
     * @return java.lang.Long
     * @Description: 新增联系人列表
     * @Param [contactPersonDto]
     * <AUTHOR>
     **/
    Long addContactPerson(ContactPersonDto contactPersonDto);


    /**
     * @return java.util.List<com.get.institutioncenter.vo.ContactPersonDto>
     * @Description: 学校联系人列表
     * @Param [data, searchBean]
     * <AUTHOR>
     */
    List<ContactPersonVo> getContactPersonDtos(ContactPersonDto data, Page page);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
    void delete(Long id);


    /**
     * 查询学校附件
     *
     * @param data
     * @param page
     * @return
     * @
     */
    List<MediaAndAttachedVo> getInstitutionMedia(MediaAndAttachedDto data, Page page);

    /**
     * 保存上传的文件
     *
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addInstitutionMedia(List<MediaAndAttachedDto> mediaAttachedVo);


    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 提供商列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<InstitutionProviderVo> getProviderList(InstitutionProviderDto data, Page page);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.ProviderInstitutionRelationVo>
     * @Description: 获取提供商下的学校
     * @Param [fkProviderId]
     * <AUTHOR>
     */
    List<InstitutionVo> getInstitutionDto(InstitutionDto data, Page page);

    /**
     * 全部学校名称下拉框
     *
     * @return
     */
    List<BaseSelectEntity> getInstitutionList();

    /**
     * 模糊搜索学校列表
     *
     * <AUTHOR>
     * @DateTime 2024/4/11 16:54
     */
    List<BaseSelectEntity> getInstitutionListByKeyword(String keyword);


    /**
     * 获取国家和搜索值下面的学校信息
     *
     * @param keyword
     * @param countryId
     * @return
     */
    List<BaseSelectEntity> getInstitutionListByKeyword(String keyword, Long countryId);

    /**
     * 根据国家获取学校下拉框数据
     *
     * @Date 16:29 2021/5/31
     * <AUTHOR>
     */
    List<BaseSelectEntity> getInstitutionListByCountryId(Long countryId);

    /**
     * 根据国家(多选)获取学校下拉框数据
     *
     * @param fkCountryIdList
     * @return
     */
    List<BaseSelectEntity> getInstitutionListByCountryIdList(List<Long> fkCountryIdList);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionVo>
     * @Description:根据国家查询学校下拉框数据
     * @Param [countryId]
     * <AUTHOR>
     **/
    List<BaseSelectEntity> getInstitutionListByProviderId(Long providerId);

    /**
     * 配置学校提供商
     *
     * @param institutionDto
     * @return
     * @
     */
    void addInstitutionProviderInstitution(InstitutionDto institutionDto);


    /**
     * feign调用 根据输入的学校名称 模糊查询对应的学校ids
     *
     * @param institutionName
     * @return
     * @
     */
    List<Long> getInstitutionIdsByName(String institutionName);

    /**
     * @param institutionName
     * @return
     */
    List<Long> getInstitutionIdsByNames(String institutionName);

    /**
     * feign调用 根据学校id查找学校名称
     *
     * @param id
     * @return
     * @
     */
    String getInstitutionNameById(Long id);

    /**
     * 通过id获取学校
     *
     * @param id
     * @return
     */
    Institution getInstitutionById(Long id);

    /**
     * @return java.lang.Long
     * @Description: 添加新闻
     * @Param [newsDto]
     * <AUTHOR>
     */
    Long addNews(NewsDto newsDto);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.NewsVo>
     * @Description: 查询新闻
     * @Param [data, page]
     * <AUTHOR>
     */
    List<NewsVo> getNewsData(NewsQueryDto data, Page page);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionZoneVo>
     * @Description: 查询校区
     * @Param [data, page]
     * <AUTHOR>
     **/
    List<InstitutionZoneVo> getInstitutionZoneData(InstitutionZoneQueryDto data, Page page);

    /**
     * 学校下拉框数据
     *
     * @Date 16:12 2021/7/22
     * <AUTHOR>
     */
    List<BaseSelectEntity> getInstitutionSelect(Long id);

    /**
     * 学校桥梁学校下拉框数据
     *
     * @return
     */
    List<BaseSelectEntity> getBridgeInstitutionSelect(Long id);

    /**
     * 非桥梁学校下拉框数据
     *
     * @Date 16:12 2021/7/22
     * <AUTHOR>
     */
    List<BaseSelectEntity> getNonBridgeInstitutionSelect(Long id);

    /**
     * 课程 非桥梁学校下拉框数据
     *
     * @Date 18:02 2021/7/27
     * <AUTHOR>
     */
    List<BaseSelectEntity> getNonBridgeInstitutionSelectByCourse(Long id);


    /**
     * 课程桥梁学校下拉框数据
     *
     * @return
     */
    List<BaseSelectEntity> getBridgeInstitutionSelectByCourse(Long institutionId);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.AppInfoVo>
     * @Description: 学校特性列表数据
     * @Param [data, page]
     * <AUTHOR>
     **/
    List<CharacterVo> getCharacterDatas(CharacterDto data, Page page);

    /**
     * 保存
     *
     * @param characterDtos
     * @return
     */
    void batchAddCharacter(List<CharacterDto> characterDtos);

    /**
     * @Description :根据学校ids 查找对应名称map
     * @Param [institutionIdSet]
     * <AUTHOR>
     */
    Map<Long, String> getInstitutionNamesByIds(Set<Long> institutionIdSet);

    /**
     * feign调用 根据学校id查找学校国家
     *
     * @Date 18:08 2021/7/13
     * <AUTHOR>
     */
    Map<Long, Long> getCountryIdByInstitutionId(Set<Long> institutionIdSet);


    /**
     * feign调用 根据学校id查找学校名字
     *
     * @Date 16:19 2021/5/24
     * <AUTHOR>
     */
    String getInstitutionNamesById(Long id);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :数据采集等级下拉框
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> getDataLevelSelect();

    /**
     * feign调用 根据学校ids查找国家名字
     *
     * @Date 10:32 2021/8/26
     * <AUTHOR>
     */
    Map<Long, String> getCountryNamesByInstitutionIds(Set<Long> institutionIds);

    /**
     * 根据学校id获取学校国家下拉框数据
     *
     * @return
     * @
     */
    List<BaseSelectEntity> getCountryByInstitutionIdSelect(Long id);

    /**
     * 已绑定提供商学校下拉框数据
     *
     * @return
     */
    List<BaseSelectEntityPlus> getProviderInstitutionList(Long countryId);

    /**
     * 根据学校id 获取对应的RPA地图学校id
     *
     * @Date 20:50 2022/4/23
     * <AUTHOR>
     */
    Map<Long, Long> getInstitutionRpaMappingRelation(Set<Long> institutionIds);

    List<BaseSelectEntity> getInstitutionSfList();

    /**
     * @ Description :根据ids获取对象s
     * @ Param [ids]
     * @ return java.util.Map<java.lang.Long,com.get.institutioncenter.entity.Institution>
     * @ author LEO
     */
    Map<Long, Institution> getInstitutionByIds(Set<Long> ids);

    List<BaseSelectEntity> getInstitutionByName(InstitutionByNameDto institutionByNameDto);

    /**
     * 获取学校背景专业统计结果
     *
     * @param caseStudyQueryDto
     * @return
     */
    CaseStudyResultsDto getCaseStudyResults(CaseStudyQueryDto caseStudyQueryDto);

    /**
     * 获取匹配模糊查询学校名称或则学校名称的学校ids
     *
     * <AUTHOR>
     * @DateTime 2022/12/9 17:28
     */
    Set<Long> getLikeInstitutionIds(InstitutionApplicationStaticsDto institutionApplicationStaticsDto);

    /**
     * 模糊查询学校获取学校ids
     *
     * <AUTHOR>
     * @DateTime 2024/3/25 11:54
     */
    List<Long> getInstitutionIdsByKeyword(String keyword);

    /**
     * 获取学校列表
     *
     * <AUTHOR>
     * @DateTime 2022/12/8 10:15
     */
    List<InstitutionApplicationStatisticsVo> getInstitutionDtoList(InstitutionApplicationStaticsQueryBo staticsQueryBo);

    /**
     * 有新闻数据的 学校下拉框数据包括简称
     *
     * @Date 11:50 2023/7/11
     * <AUTHOR>
     */
    List<BaseSelectEntity> getNewsInstitutionSfList();

    /**
     * 根据学校ids 查找学校对象map
     *
     * @Date 18:22 2023/12/18
     * <AUTHOR>
     */
    Map<Long, InstitutionVo> getInstitutionDtoMapByIds(Set<Long> institutionIdSet);

    /**
     * 根据学校Ids，获取学校信息
     *
     * <AUTHOR>
     * @DateTime 2024/1/22 17:05
     */
    Map<Long, InstitutionVo> getInstitutionDtoByIds(Set<Long> institutionIdSet);

    /**
     * 根据国家ids 获取有效的学校列表
     *
     * @param fkAreaCountryIds 国家ids
     * @return
     */
    List<InstitutionVo> getInstitutionByCountryIds(Set<Long> fkAreaCountryIds);

    /**
     * AI获取学校信息
     *
     * @param aiInstitutionDto
     * @return
     */
    List<AiInstitutionVo> getAiInstitutionInfo(AiInstitutionDto aiInstitutionDto);
}
