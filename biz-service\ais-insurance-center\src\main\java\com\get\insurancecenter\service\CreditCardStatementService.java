package com.get.insurancecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.insurancecenter.dto.card.SaveStatementActualPayDto;
import com.get.insurancecenter.dto.card.SaveStatementDto;
import com.get.insurancecenter.dto.card.TradeRecordDto;
import com.get.insurancecenter.entity.CreditCardStatement;
import com.get.insurancecenter.vo.card.TradeRecordVo;

import java.util.List;


/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
public interface CreditCardStatementService extends IService<CreditCardStatement> {

    /**
     * 交易记录分页
     *
     * @param params
     * @param page
     * @return
     */
    List<TradeRecordVo> tradeRecordPage(TradeRecordDto params, Page page);

    /**
     * 新增交易记录
     *
     * @param saveStatementDto
     */
    void saveStatement(SaveStatementDto saveStatementDto);

    /**
     * 保存实付金额
     *
     * @param actualPayDto
     */
    void saveStatementActualPay(SaveStatementActualPayDto actualPayDto);

}
