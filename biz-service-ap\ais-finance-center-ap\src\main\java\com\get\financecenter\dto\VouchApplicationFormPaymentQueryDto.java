package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 付款列表Dto
 */
@Data
public class VouchApplicationFormPaymentQueryDto {

    @ApiModelProperty(value = "财务单据表名")
    @NotBlank(message = "财务单据表名不能为空")
    private String fkTableName;

    @ApiModelProperty(value = "财务单据表记录Id")
    @NotNull(message = "财务单据表记录Id不能为空")
    private Long fkTableId;

}
