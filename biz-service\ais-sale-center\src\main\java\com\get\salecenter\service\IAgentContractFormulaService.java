package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.AgentContractFormulaVo;
import com.get.salecenter.vo.CompanyTreeVo;
import com.get.salecenter.dto.AgentContractFormulaCompanyDto;
import com.get.salecenter.dto.AgentContractFormulaDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/6 12:21
 * @verison: 1.0
 * @description:
 */
public interface IAgentContractFormulaService {
    /**
     * @return com.get.salecenter.vo.AgentContractFormulaVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    AgentContractFormulaVo findAgentContractFormulaById(Long id);

    /**
     * @return void
     * @Description :新增
     * @Param [agentContractFormulaDto]
     * <AUTHOR>
     */
    Long addAgentContractFormula(AgentContractFormulaDto agentContractFormulaDto);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.AgentContractFormulaVo
     * @Description :修改
     * @Param [agentContractFormulaDto]
     * <AUTHOR>
     */
    AgentContractFormulaVo updateAgentContractFormula(AgentContractFormulaDto agentContractFormulaDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.AgentContractFormulaVo>
     * @Description :列表
     * @Param [agentContractFormulaDto, page]
     * <AUTHOR>
     */
    List<AgentContractFormulaVo> getAgentContractFormulas(AgentContractFormulaDto agentContractFormulaDto, Page page);

    /**
     * @return void
     * @Description :上移下移
     * @Param [agentContractFormulaDtos]
     * <AUTHOR>
     */
    void movingOrder(List<AgentContractFormulaDto> agentContractFormulaDtos);

    /**
     * @return void
     * @Description :学生代理合同公式-安全配置
     * @Param [agentContractFormulaCompanyDtos]
     * <AUTHOR>
     */
    void editAgentContractFormulaCompany(List<AgentContractFormulaCompanyDto> agentContractFormulaCompanyDtos);

    /**
     * @return java.util.List<com.get.salecenter.vo.CompanyTreeVo>
     * @Description :学生代理合同公式-安全配置详情
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    List<CompanyTreeVo> getAgentContractFormulaCompany(Long agentContractFormulaId);
}
