package com.get.platformconfigcenter.service.impl;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.core.mybatis.base.UtilService;
import com.get.platformconfigcenter.dao.appissue.IssueTranslationMapper;
import com.get.platformconfigcenter.service.AttachmentTypeService;
import com.get.platformconfigcenter.service.DeleteService;
import com.get.platformconfigcenter.service.MediaAndAttachedIssueService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 附件类型业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/5/20 15:33
 */
@Service
public class AttachmentTypeServiceImpl implements AttachmentTypeService {
//    @Resource
//    private StudentAttachmentMapper studentAttachmentMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private DeleteService deleteService;
    @Resource
    private MediaAndAttachedIssueService mediaAndAttachedIssueService;
    @Resource
    private IssueTranslationMapper issueTranslationMapper;


    /**
     * 附件类型列表数据
     *
     * @return
     * @Date 15:36 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public List<StudentAttachmentVo> getAttachmentTypeList(StudentAttachmentDto studentAttachmentVo, Page page) {
////        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
////        Example example = new Example(StudentAttachment.class);
////        example.createCriteria().andLike("name", "%" + studentAttachmentVo.getName() + "%");
////        example.orderBy("viewOrder").desc();
////        List<StudentAttachment> studentAttachments = studentAttachmentMapper.selectByExample(example);
////        page.restPage(studentAttachments);
//
//        LambdaQueryWrapper<StudentAttachment> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.like(StudentAttachment::getName, studentAttachmentVo.getName());
//        lambdaQueryWrapper.orderByDesc(StudentAttachment::getViewOrder);
//
//        IPage<StudentAttachment> pages = this.studentAttachmentMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
//        List<StudentAttachment> studentAttachments = pages.getRecords();
//        page.setAll((int) pages.getTotal());
//
//        List<StudentAttachmentVo> studentAttachmentDtoList = new ArrayList<>();
//        for (StudentAttachment studentAttachment : studentAttachments) {
//            StudentAttachmentVo studentAttachmentDto = BeanCopyUtils.objClone(studentAttachment, StudentAttachmentVo::new);
//            studentAttachmentDto.setFkTableName(TableEnum.U_STUDENT_ATTACHMENT.key);
//            studentAttachmentDtoList.add(studentAttachmentDto);
//        }
//        return studentAttachmentDtoList;
//    }

    /**
     * 新增附件类型
     *
     * @Date 16:13 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public Long addAttachmentType(StudentAttachmentDto studentAttachmentVo) {
//        if (GeneralTool.isEmpty(studentAttachmentVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
//        }
////        Example example = new Example(StudentAttachment.class);
////        example.createCriteria().andEqualTo("typeKey", studentAttachmentVo.getTypeKey());
////        Example.Criteria criteria = example.createCriteria().andEqualTo("name", studentAttachmentVo.getName());
////        example.or(criteria);
////        List<StudentAttachment> studentAttachments = studentAttachmentMapper.selectByExample(example);
//        List<StudentAttachment> studentAttachments = studentAttachmentMapper.selectList(Wrappers.<StudentAttachment>query().lambda()
//                .eq(StudentAttachment::getTypeKey, studentAttachmentVo.getTypeKey())
//                .or().eq(StudentAttachment::getName, studentAttachmentVo.getName()));
//        if (GeneralTool.isNotEmpty(studentAttachments)) {
//            throw new GetServiceException(ResultCode.INVALID_PARAM, LocaleMessageUtils.getMessage("type_name_exists"));
//        }
//        StudentAttachment studentAttachment = BeanCopyUtils.objClone(studentAttachmentVo, StudentAttachment::new);
//        studentAttachment.setViewOrder(studentAttachmentMapper.getMaxViewOrder());
//        utilService.updateUserInfoToEntity(studentAttachment);
//        studentAttachmentMapper.insert(studentAttachment);
//        //插入附件模板
//        if (GeneralTool.isNotEmpty(studentAttachmentVo.getAttachmentTypeTemplate())) {
//            addMediaAndAttached(studentAttachmentVo, studentAttachment);
//        }
//        return studentAttachment.getId();
//    }

    /**
     * 修改附件类型
     *
     * @Date 16:29 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public StudentAttachmentVo updateAttachmentType(StudentAttachmentDto studentAttachmentVo) {
//        if (GeneralTool.isEmpty(studentAttachmentVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
////        Example example = new Example(StudentAttachment.class);
////        example.createCriteria().andEqualTo("typeKey", studentAttachmentVo.getTypeKey()).andNotEqualTo("id", studentAttachmentVo.getId());
////        Example.Criteria criteria = example.createCriteria().andEqualTo("name", studentAttachmentVo.getName()).andNotEqualTo("id", studentAttachmentVo.getId());
////        example.or(criteria);
////        example.orderBy("viewOrder").desc();
////        List<StudentAttachment> studentAttachments = studentAttachmentMapper.selectByExample(example);
//
//        List<StudentAttachment> studentAttachments = studentAttachmentMapper.selectList(Wrappers.lambdaQuery(StudentAttachment.class)
//                .ne(StudentAttachment::getId, studentAttachmentVo.getId())
//                .and(wrapper -> wrapper.eq(StudentAttachment::getTypeKey, studentAttachmentVo.getTypeKey())
//                        .or().eq(StudentAttachment::getName, studentAttachmentVo.getName())));
//        if (GeneralTool.isNotEmpty(studentAttachments)) {
//            throw new GetServiceException(ResultCode.INVALID_PARAM, LocaleMessageUtils.getMessage("type_name_exists"));
//        }
//        StudentAttachment studentAttachment = studentAttachmentMapper.selectById(studentAttachmentVo.getId());
//        if (GeneralTool.isEmpty(studentAttachment)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        studentAttachment = BeanCopyUtils.objClone(studentAttachmentVo, StudentAttachment::new);
//        utilService.updateUserInfoToEntity(studentAttachment);
//        studentAttachmentMapper.updateByPrimaryKeySelective(studentAttachment);
//
//        //先删除模板
//        mediaAndAttachedIssueService.deleteMediaAndAttached(TableEnum.U_STUDENT_ATTACHMENT.key, studentAttachment.getId());
//        //插入附件模板
//        if (GeneralTool.isNotEmpty(studentAttachmentVo.getAttachmentTypeTemplate())) {
//            addMediaAndAttached(studentAttachmentVo, studentAttachment);
//        }
//        return findAttachmentTypeById(studentAttachment.getId());
//    }

    /**
     * 更新模板类型 模板文件
     *
     * @Date 15:50 2021/7/5
     * <AUTHOR>
     */
//    private void addMediaAndAttached(StudentAttachmentDto studentAttachmentVo, StudentAttachment studentAttachment) {
//        MediaAndAttachedVo mediaAndAttachedVo = studentAttachmentVo.getAttachmentTypeTemplate();
//        mediaAndAttachedVo.setTypeKey(FileTypeEnum.PLATFORM_ISSUE_ATTACHMENT_TYPE_TEMPLATE.key);
//        mediaAndAttachedVo.setFkTableId(studentAttachment.getId());
//        mediaAndAttachedVo.setFkTableName(TableEnum.U_STUDENT_ATTACHMENT.key);
//        mediaAndAttachedIssueService.addMediaAndAttached(mediaAndAttachedVo);
//    }


    /**
     * 附件类型详情
     *
     * @Date 16:34 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public StudentAttachmentVo findAttachmentTypeById(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        StudentAttachment studentAttachment = studentAttachmentMapper.selectById(id);
//        if (GeneralTool.isEmpty(studentAttachment)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        StudentAttachmentVo studentAttachmentDto = BeanCopyUtils.objClone(studentAttachment, StudentAttachmentVo::new);
//        MediaAndAttachedVo attachedVo = new MediaAndAttachedVo();
//        attachedVo.setFkTableName(TableEnum.U_STUDENT_ATTACHMENT.key);
//        attachedVo.setFkTableId(id);
//        attachedVo.setTypeKey(FileTypeEnum.PLATFORM_ISSUE_ATTACHMENT_TYPE_TEMPLATE.key);
//        List<MediaAndAttachedDto> mediaAndAttachedDto = mediaAndAttachedIssueService.getMediaAndAttachedVo(attachedVo);
//        if (GeneralTool.isNotEmpty(mediaAndAttachedDto)) {
//            studentAttachmentDto.setAttachmentTypeTemplate(mediaAndAttachedDto.get(0));
//        }
//        return studentAttachmentDto;
//    }

    /**
     * 删除附件类型
     *
     * @Date 16:38 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public void delete(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        deleteService.deleteValidateStudentAttachment(id);
//        studentAttachmentMapper.deleteById(id);
//        mediaAndAttachedIssueService.deleteMediaAndAttached(FileTypeEnum.PLATFORM_ISSUE_ATTACHMENT_TYPE_TEMPLATE.key, id);
//
//        //删除翻译内容
//        issueTranslationMapper.deleteTranslations(TableEnum.U_STUDENT_ATTACHMENT.key, id);
//    }

    /**
     * 上移下移
     *
     * @Date 17:04 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public void movingOrder(List<StudentAttachmentDto> studentAttachmentVos) {
//        if (GeneralTool.isEmpty(studentAttachmentVos)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
//        StudentAttachment ro = BeanCopyUtils.objClone(studentAttachmentVos.get(0), StudentAttachment::new);
//        Integer oneorder = ro.getViewOrder();
//        StudentAttachment rt = BeanCopyUtils.objClone(studentAttachmentVos.get(1), StudentAttachment::new);
//        Integer twoorder = rt.getViewOrder();
//        ro.setViewOrder(twoorder);
//        utilService.updateUserInfoToEntity(ro);
//        rt.setViewOrder(oneorder);
//        utilService.updateUserInfoToEntity(rt);
//        studentAttachmentMapper.updateByPrimaryKeySelective(ro);
//        studentAttachmentMapper.updateByPrimaryKeySelective(rt);
//    }

    /**
     * 附件类型下拉框数据
     *
     * @Date 15:06 2021/5/26
     * <AUTHOR>
     */
//    @Override
//    public List<StudentAttachmentVo> getAttachmentTypeSelect() {
////        Example example = new Example(StudentAttachment.class);
////        example.orderBy("viewOrder").desc();
////        List<StudentAttachment> studentAttachments = studentAttachmentMapper.selectByExample(example);
//        List<StudentAttachment> studentAttachments = studentAttachmentMapper.selectList(Wrappers.<StudentAttachment>lambdaQuery().orderByDesc(StudentAttachment::getViewOrder));
//        return studentAttachments.stream().map(studentAttachment -> BeanCopyUtils.objClone(studentAttachment, StudentAttachmentVo::new)).collect(Collectors.toList());
//    }

    /**
     * 附件文件类型下拉框数据
     *
     * @Date 17:09 2021/5/26
     * <AUTHOR>
     */
    @Override
    public List<Map<String, Object>> getFileExtensionSelect() {
        return ProjectKeyEnum.enumsTranslation2Arrays(ProjectKeyEnum.FILE_EXTENSION);
    }
}
