package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.component.TranslationHelp;
import com.get.institutioncenter.dao.InfoTypeMapper;
import com.get.institutioncenter.dao.InstitutionInfoMapper;
import com.get.institutioncenter.dao.InstitutionMapper;
import com.get.institutioncenter.dto.InstitutionInfoDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.dto.query.InstitutionInfoQueryDto;
import com.get.institutioncenter.entity.InstitutionInfo;
import com.get.institutioncenter.entity.MediaAndAttached;
import com.get.institutioncenter.po.InstitutionInfoPo;
import com.get.institutioncenter.service.*;
import com.get.institutioncenter.vo.InstitutionInfoVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/8/11
 * @TIME: 10:41
 * @Description:
 **/
@Service
public class InstitutionInfoServiceImpl extends BaseServiceImpl<InstitutionInfoMapper, InstitutionInfo> implements IInstitutionInfoService {
    @Resource
    private InfoTypeMapper infoTypeMapper;
    @Resource
    private InstitutionInfoMapper institutionInfoMapper;
    @Resource
    private InstitutionMapper institutionMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IInstitutionService institutionService;
    @Resource
    private IInfoTypeService infoTypeService;
    @Resource
    private ITranslationMappingService translationMappingService;
    @Resource
    private GetRedis redisClient;
    @Resource
    private TranslationHelp translationHelp;

    @Override
    public List<InstitutionInfoVo> datas(InstitutionInfoQueryDto institutionInfoVo, Page page) {
        //获取分页数据
        LambdaQueryWrapper<InstitutionInfo> wrapper = new LambdaQueryWrapper();
        IPage<InstitutionInfo> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<InstitutionInfo> institutionInfos = institutionInfoMapper.datas(pages, institutionInfoVo);
        page.setAll((int) pages.getTotal());
        List<InstitutionInfoVo> convertDatas = new ArrayList<>();

        //学校ids
        Set<Long> institutionIds = institutionInfos.stream().map(InstitutionInfo::getFkInstitutionId).collect(Collectors.toSet());
        //根据学校ids获取名称
        Map<Long, String> institutionNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(institutionIds)) {
            institutionNamesByIds = institutionService.getInstitutionNamesByIds(institutionIds);
        }

        //资讯类型ids
        Set<Long> infoTypeIds = institutionInfos.stream().map(InstitutionInfo::getFkInfoTypeId).collect(Collectors.toSet());
        //根据资讯类型ids获取名称map
        Map<Long, String> infoTypeNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(infoTypeIds)) {
            infoTypeNamesByIds = infoTypeService.getInfoTypeNamesByIds(infoTypeIds);
        }

        for (InstitutionInfo institutionInfo : institutionInfos) {
            InstitutionInfoVo institutionInfoDto = BeanCopyUtils.objClone(institutionInfo, InstitutionInfoVo::new);
            if (GeneralTool.isNotEmpty(institutionInfoDto.getFkInstitutionId())) {
                institutionInfoDto.setInstitutionName(institutionNamesByIds.get(institutionInfoDto.getFkInstitutionId()));
            }
            if (GeneralTool.isNotEmpty(institutionInfoDto.getFkInfoTypeId())) {
                institutionInfoDto.setInfoTypeName(infoTypeNamesByIds.get(institutionInfoDto.getFkInfoTypeId()));
            }
            StringJoiner stringJoiner = new StringJoiner(" ");
            if (GeneralTool.isNotEmpty(institutionInfoDto.getPublicLevel())) {
                List<String> result = Arrays.asList(institutionInfoDto.getPublicLevel().split(","));
                for (String name : result) {
                    stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
                }
                institutionInfoDto.setPublicLevelName(stringJoiner.toString());
            }
            convertDatas.add(institutionInfoDto);
        }
        String language = SecureUtil.getLocale();
//        List<InstitutionInfoPo> institutionInfoPos = Collections.singletonList(BeanCopyUtils.objClone(convertDatas, InstitutionInfoPo::new));
        List<InstitutionInfoPo> institutionInfoPos = BeanCopyUtils.copyListProperties(convertDatas, InstitutionInfoPo::new);
        if (GeneralTool.isNotEmpty(convertDatas) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
           translationHelp.translation(institutionInfoPos, ProjectKeyEnum.getInitialValue(language));
        }
        if (GeneralTool.isNotEmpty(institutionInfoPos)){
//            convertDatas = Collections.singletonList(BeanCopyUtils.objClone(institutionInfoPos, InstitutionInfoVo::new));
            convertDatas = BeanCopyUtils.copyListProperties(institutionInfoPos, InstitutionInfoVo::new);
        }
        return convertDatas;
    }

    @Override
    public Long addInstitutionInfo(InstitutionInfoDto institutionInfoDto) {
        InstitutionInfo institutionInfo = BeanCopyUtils.objClone(institutionInfoDto, InstitutionInfo::new);
        utilService.updateUserInfoToEntity(institutionInfo);
        int i = institutionInfoMapper.insertSelective(institutionInfo);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return institutionInfo.getId();
    }

    @Override
    public List<MediaAndAttachedVo> getInstitutionInfoMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.INSTITUTION_INFO.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    public InstitutionInfoVo findInstitutionInfoById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionInfo institutionInfo = institutionInfoMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionInfo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionInfoVo institutionInfoVo = BeanCopyUtils.objClone(institutionInfo, InstitutionInfoVo::new);
        String tableName = TableEnum.INSTITUTION_INFO.key;
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(tableName);
        attachedVo.setFkTableId(id);
        List<MediaAndAttachedVo> mediaAndAttachedVo = attachedService.getMediaAndAttachedDto(attachedVo);
        institutionInfoVo.setMediaAndAttachedDtos(mediaAndAttachedVo);
        if (GeneralTool.isNotEmpty(institutionInfoVo.getFkInstitutionId())) {
            institutionInfoVo.setInstitutionName(institutionMapper.getInstitutionNameById(institutionInfoVo.getFkInstitutionId()));
        }
        if (GeneralTool.isNotEmpty(institutionInfoVo.getFkInfoTypeId())) {
            institutionInfoVo.setInfoTypeName(infoTypeMapper.getInfoTypeNameById(institutionInfoVo.getFkInfoTypeId()));
        }
        institutionInfoVo.setFkTableName(TableEnum.INSTITUTION_INFO.key);
        StringJoiner stringJoiner = new StringJoiner(" ");
        if (GeneralTool.isNotEmpty(institutionInfoVo.getPublicLevel())) {
            List<String> result = Arrays.asList(institutionInfoVo.getPublicLevel().split(","));
            for (String name : result) {
                stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
            }
            institutionInfoVo.setPublicLevelName(stringJoiner.toString());
        }
        String language = SecureUtil.getLocale();
        InstitutionInfoPo institutionInfoPo = BeanCopyUtils.objClone(institutionInfoVo, InstitutionInfoPo::new);
        if (GeneralTool.isNotEmpty(institutionInfoPo) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(Collections.singletonList(institutionInfoPo), ProjectKeyEnum.getInitialValue(language));
        }
        if (GeneralTool.isNotEmpty(institutionInfoPo)) {
            institutionInfoVo = BeanCopyUtils.objClone(institutionInfoPo, InstitutionInfoVo::new);
        }
        return institutionInfoVo;
    }

    /**
     * 删除学校资讯
     *
     * @param id
     * @
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        //TODO 改过
        //InstitutionInfo institutionInfo = findInstitutionInfoById(id);
        InstitutionInfoVo institutionInfo = findInstitutionInfoById(id);
        if (institutionInfo == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        int i = institutionInfoMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        //同时删除该表id下的所有媒体附件
        List<MediaAndAttached> mediaAndAttachedByTableIds = attachedService.findMediaAndAttachedByTableId(id, TableEnum.INSTITUTION_INFO.key, null);
        for (MediaAndAttached mediaAndAttached : mediaAndAttachedByTableIds) {
            attachedService.deleteMediaAttached(mediaAndAttached.getId());
        }
        //删除资讯翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_INFO.key, id);
    }

    @Override
    public InstitutionInfoVo updateInstitutionInfo(InstitutionInfoDto institutionInfoDto) {
        if (institutionInfoDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(institutionInfoDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionInfo rs = institutionInfoMapper.selectById(institutionInfoDto.getId());
        if (rs == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionInfo institutionInfo = BeanCopyUtils.objClone(institutionInfoDto, InstitutionInfo::new);
        utilService.updateUserInfoToEntity(institutionInfo);
        institutionInfoMapper.updateById(institutionInfo);
        return findInstitutionInfoById(institutionInfo.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MediaAndAttachedVo> addInstitutionInfoMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            String tableName = TableEnum.INSTITUTION_INFO.key;
            mediaAndAttachedDto.setFkTableName(tableName);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public List<Map<String, Object>> findMediaAndAttachedType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.INSTITUTIONINFO);
    }

    @Override
    public List<Map<String, Object>> getPublicObjectsSelect() {
        return ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.PUBLIC_OBJECTS);
    }
}
