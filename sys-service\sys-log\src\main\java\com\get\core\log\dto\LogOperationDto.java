package com.get.core.log.dto;

import com.get.core.log.model.LogOperation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: Sea
 * @create: 2020/6/22 10:43
 * @verison: 1.0
 * @description:
 */
@ApiModel("操作日志返回类")
@Data
public class LogOperationDto extends LogOperation implements Serializable {
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;
}
