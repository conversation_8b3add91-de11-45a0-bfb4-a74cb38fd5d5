package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ExpenseClaimAgentContentDto {
    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "内容名称")
    private String name;

    @ApiModelProperty(value = "内容名称")
    private List<String> names;

    @ApiModelProperty(value = "关键字搜索")
    private String keyWord;

}