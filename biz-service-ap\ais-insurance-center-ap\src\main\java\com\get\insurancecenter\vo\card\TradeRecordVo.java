package com.get.insurancecenter.vo.card;

import com.get.insurancecenter.entity.CreditCardStatement;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/8/1
 * @Version 1.0
 * @apiNote:交易记录实体
 */
@Data
public class TradeRecordVo extends CreditCardStatement {

    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    @ApiModelProperty(value = "保险单号")
    private String insuranceNum;

    @ApiModelProperty(value = "系统订单编号")
    private String orderNum;

    @ApiModelProperty(value = "受保人姓名")
    private String insurantName;

    @ApiModelProperty(value = "交易对象")
    private String productTypeName;

    @ApiModelProperty(value = "差额")
    private BigDecimal differenceAmount;

    @ApiModelProperty(value = "业务类型名称")
    private String relationTargetName;
}
