package com.get.salecenter.service;

import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.dto.GoproNucleicAcidUpdateDto;
import com.get.salecenter.dto.MediaAndAttachedDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2022/5/25 12:28
 * @verison: 1.0
 * @description:
 */
public interface IGoproNucleicAcidService {


    /**
     * 保存核酸信息
     *
     * @param goproNucleicAcidUpdateDto
     * @return
     */
    Long addGoproNucleicAcid(GoproNucleicAcidUpdateDto goproNucleicAcidUpdateDto);

    /**
     * 保存核酸资料附件
     *
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 区域下拉框
     *
     * @return
     */
    List<Map<String, Object>> getRegionSelect();

    /**
     * 区域经理下拉框
     *
     * @param id
     * @return
     */
    List<Map<String, Object>> getStaffSelect(Integer id);

    void exportInfo(HttpServletResponse response) throws Exception;

    void exportAllInfo(HttpServletResponse response, GoproNucleicAcidUpdateDto goproNucleicAcidUpdateDto) throws Exception;

    /**
     * 导出核酸信息excel
     * @param response
     * @param goproNucleicAcidUpdateDto
     * @throws Exception
     */
    void exportAllExcelInfo(HttpServletResponse response, GoproNucleicAcidUpdateDto goproNucleicAcidUpdateDto) throws Exception;

    /**
     * 导出核酸信息图片
     *
     * @param response
     * @param goproNucleicAcidUpdateDto
     * @throws Exception
     */
    void exportAllImageInfo(HttpServletResponse response, GoproNucleicAcidUpdateDto goproNucleicAcidUpdateDto) throws Exception;
}
