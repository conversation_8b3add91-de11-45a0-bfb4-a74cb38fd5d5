<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.votingcenter.dao.appvoting.UserVotingAwardMapper">
  <insert id="insertSelective" parameterType="com.get.votingcenter.entity.UserVotingAward" keyProperty="id" useGeneratedKeys="true">
    insert into r_user_voting_award
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkUserId != null">
        fk_user_id,
      </if>
      <if test="fkVotingId != null">
        fk_voting_id,
      </if>
      <if test="optKey != null">
        opt_key,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkUserId != null">
        #{fkUserId,jdbcType=BIGINT},
      </if>
      <if test="fkVotingId != null">
        #{fkVotingId,jdbcType=BIGINT},
      </if>
      <if test="optKey != null">
        #{optKey,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>