package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/2/9 12:49
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffCommissionConfirmVo {

    @ApiModelProperty("申请步骤id")
    private Long stepId;

    @ApiModelProperty("申请步骤名称")
    private String stepName;

    @ApiModelProperty("国家id")
    private Long fkAreaCountryId;

    @ApiModelProperty("国家名称")
    private String fkAreaCountryName;

    @ApiModelProperty("学校id")
    private Long fkInstitutionId;

    @ApiModelProperty("学校名称")
    private String fkInstitutionName;

    @ApiModelProperty("课程等级ids")
    private String fkInstitutionCourseMajorLevelIds;

    @ApiModelProperty("课程等级名称")
    private String fkInstitutionCourseMajorLevelName;

    @ApiModelProperty("课程id")
    private Long fkInstitutionCourseId;

    @ApiModelProperty("课程名称")
    private String fkInstitutionCourseName;

    @ApiModelProperty("申请方案id")
    private Long fkStudentOfferId;

    @ApiModelProperty("申请计划id")
    private Long fkStudentOfferItemId;

    @ApiModelProperty("公司id")
    private Long fkCompanyId;

    @ApiModelProperty("项目角色")
    private String roleKeys;

    @ApiModelProperty("申请计划步骤ids")
    private String stepIds;

    @ApiModelProperty("课程大类名称")
    private String courseTypeGroupNames;

    @ApiModelProperty("课程大类名称")
    private String courseTypeGroupIds;

    @ApiModelProperty("结算状态：0未确认/1已确认/2已结算")
    private Integer settlementStatus;

    @ApiModelProperty("结算状态名称")
    private String settlementStatusName;

    @ApiModelProperty("是否学校佣金false否/true是")
    private Boolean isInstitutionCommission;

    @ApiModelProperty("是否学校佣金false否/true是")
    private String isInstitutionCommissionName;

    @ApiModelProperty("是否加申false否/true是")
    private Boolean isAddApp;

    @ApiModelProperty("是否加申false否/true是")
    private String isAddAppName;

    @ApiModelProperty("结算日期")
    private String settlementDate;

    @ApiModelProperty("按钮标志")
    private Boolean confirmSettlementButtonType = false;

    @ApiModelProperty("取消结算按钮标志")
    private Boolean cancelSettlementButtonType = false;

    @ApiModelProperty("动态表头数据")
    private List<StaffCommissionDatasVo<BigDecimal>> dynamicDatas;
}

