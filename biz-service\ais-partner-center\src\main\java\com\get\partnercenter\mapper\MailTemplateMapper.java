package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.partnercenter.entity.MailTemplateEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 针对表【m_partner_user】的数据库操作Mapper
 * @createDate 2024-12-20 14:13:45
 * @Entity com.partner.entity.MPartnerUser
 */
@Mapper
@DS("partnerdb")
public interface MailTemplateMapper extends BaseMapper<MailTemplateEntity> {

}




