package com.get.pmpcenter.dto.institution;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/24
 * @Version 1.0
 * @apiNote:
 */
@Data
public class UnbindProviderInstitutionDto {

    @ApiModelProperty(value = "提供商ID不能为空")
    @NotNull(message = "提供商ID不能为空")
    private Long providerId;

    @ApiModelProperty(value = "学校Ids")
    @NotNull(message = "学校ID不能为空")
    private List<Long> institutionIds;
}
