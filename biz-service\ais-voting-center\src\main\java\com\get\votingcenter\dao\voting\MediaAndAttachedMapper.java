package com.get.votingcenter.dao.voting;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.votingcenter.entity.VotingMediaAndAttached;
import com.get.votingcenter.dto.MediaAndAttachedDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MediaAndAttachedMapper extends BaseMapper<VotingMediaAndAttached> {
    int insert(VotingMediaAndAttached record);

    int insertSelective(VotingMediaAndAttached record);

    int updateById(VotingMediaAndAttached record);

    int updateByPrimaryKey(VotingMediaAndAttached record);

    Integer getNextIndexKey(@Param("tableId") Long tableId, @Param("tableName") String tableName);

    /**
     * 获取图片
     *
     * @param mediaAndAttachedDto
     * @return
     */
    List<VotingMediaAndAttached> getMediaAndAttacheds(@Param("mediaAndAttachedDto") MediaAndAttachedDto mediaAndAttachedDto);
}