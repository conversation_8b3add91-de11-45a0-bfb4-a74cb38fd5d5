package com.get.partnercenter.service;

import com.get.partnercenter.dto.wechat.LinkAndQrCodeVo;
import com.get.partnercenter.dto.wechat.MinProgramQrCodeDto;

/**
 * @Author:Oliver
 * @Date: 2025/7/21
 * @Version 1.0
 * @apiNote:微信服务
 */
public interface WeChatService {

    /**
     * 获取微信access_token
     *
     * @return
     */
    String getAccessToken(String appId, String secret);


    /**
     * 获取小程序二维码
     *
     * @param appId
     * @param minProgramQrCodeDto
     * @return
     */
    Object getQrCode(String appId, MinProgramQrCodeDto minProgramQrCodeDto,String accessToken);


    /**
     * 获取小程序URL跳转链接
     *
     * @param appId
     * @param minProgramQrCodeDto
     * @return
     */
    Object getUrlLink(String appId, MinProgramQrCodeDto minProgramQrCodeDto,String accessToken);

    /**
     * 获取小程序URL跳转链接和二维码
     *
     * @param appId
     * @param minProgramQrCodeDto
     * @return
     */
    LinkAndQrCodeVo getLinkAndQrCode(String appId, MinProgramQrCodeDto minProgramQrCodeDto);
}
