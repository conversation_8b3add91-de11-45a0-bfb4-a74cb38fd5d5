package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class BusinessProviderTypeDto extends BaseVoEntity {

    @ApiModelProperty("业务提供商类型名称")
    @NotBlank(message = "业务提供商类型名称不能为空", groups = {Add.class, Update.class})
    private String typeName;

    @ApiModelProperty("排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
