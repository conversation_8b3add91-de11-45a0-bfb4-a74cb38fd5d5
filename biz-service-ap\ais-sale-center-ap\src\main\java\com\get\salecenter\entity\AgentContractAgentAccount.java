package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_agent_contract_agent_account")
public class AgentContractAgentAccount extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学生代理合同Id
     */
    @ApiModelProperty(value = "学生代理合同Id")
    @Column(name = "fk_agent_contract_id")
    private Long fkAgentContractId;
    /**
     * 学生代理合同账户Id
     */
    @ApiModelProperty(value = "学生代理合同账户Id")
    @Column(name = "fk_agent_contract_account_id")
    private Long fkAgentContractAccountId;
}