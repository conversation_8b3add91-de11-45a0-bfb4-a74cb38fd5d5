package com.get.helpcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.helpcenter.vo.HelpTypeVo;
import com.get.helpcenter.vo.tree.HelpTypeTreeVo;
import com.get.helpcenter.service.IHelpTypeService;
import com.get.helpcenter.dto.HelpTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

;

/**
 * @author: Hardy
 * @create: 2021/5/11 15:45
 * @verison: 1.0
 * @description:
 */
@Api(tags = "帮助类型管理")
@RestController
@RequestMapping("/help/helptype")
@Slf4j
public class HelpTypeController {
    @Resource
    private IHelpTypeService helpTypeService;


    /**
     * @return com.get.common.result.ResponseBo<com.get.helpcenter.vo.HelpTypeVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.LIST, description = "帮助中心/帮助类型管理/查询帮助类型")
    @PostMapping("datas")
    public ListResponseBo<HelpTypeVo> datas(@RequestBody SearchBean<HelpTypeDto> page) {
        List<HelpTypeVo> datas = helpTypeService.getHelpTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增
     * @Param [helpTypeDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.ADD, description = "帮助中心/帮助类型管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(HelpTypeDto.Add.class) HelpTypeDto helpTypeDto) {
        return SaveResponseBo.ok(helpTypeService.addHelpType(helpTypeDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.DELETE, description = "帮助中心/帮助类型管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        helpTypeService.deleteHelpType(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @return
     * @Description :修改
     * @Param
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.EDIT, description = "帮助中心/帮助类型管理/修改")
    @PostMapping("update")
    public ResponseBo<HelpTypeVo> update(@RequestBody @Validated(HelpTypeDto.Update.class) HelpTypeDto helpTypeDto) {
        return UpdateResponseBo.ok(helpTypeService.updateHelpTypeVo(helpTypeDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 上移下移
     * @Param [helpTypeDtoList]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.EDIT, description = "帮助中心/帮助类型管理/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<HelpTypeDto> helpTypeDtoList) {
        helpTypeService.movingOrder(helpTypeDtoList);
        return ResponseBo.ok();
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "帮助类型树形数据结构")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.LIST, description = "帮助中心/帮助类型管理/帮助类型树")
    @GetMapping("/getHelpTypeTree")
    public ResponseBo<HelpTypeTreeVo> getHelpTypeTreeDto(@RequestParam(name = "helpTypeId", required = false) Long helpTypeId) {
        return new ListResponseBo<>(helpTypeService.getHelpTypeTreeDto(helpTypeId));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :帮助类型下拉框
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "帮助类型下拉框", notes = "")
    @PostMapping("getHelpTypeSelect")
    public ResponseBo<HelpTypeVo> getHelpTypeSelect() {
        return new ListResponseBo<>(helpTypeService.getHelpTypeSelect());
    }
}



