package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.AgentContractAccountVo;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.entity.AgentContractAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;


@Mapper
public interface AgentContractAccountMapper extends BaseMapper<AgentContractAccount> {

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [agentId]
     * <AUTHOR>
     */
    Boolean isExistByContractId(Long contractId);


    /**
     * @return java.lang.Boolean
     * @Description: 是否有相同的货币
     * @Param [agentId]
     * <AUTHOR>
     */
    Long validateCurrency(@Param("fkAgentId") Long fkAgentId, @Param("currency") String currency, @Param("agentContractAccountId") Long agentContractAccountId);

    /**
     * 根据代理ids 获取银行账户
     *
     * @Date 10:23 2021/12/23
     * <AUTHOR>
     */
    List<AgentContractAccount> selectagentContractAccountByAgentIds(@Param("agentIds") List<Long> agentIds);

    /**
     * 代理银行账户下拉框
     *
     * @return
     * @param fkTargetId
     */
    List<BaseSelectEntity> getAgentContractAccountSelect(Long fkTargetId);

    List<AgentContractAccountVo> getAgentContractAccountSelectById(Long agentId);

    List<AgentContractAccountVo> selectAgentContractAccountByAccountIds(@Param("accountIds") List<Long> accountIds);

    /**
     * feign调用，获取代理账户名称
     *
     * @param id
     * @return
     */
    String getAgentContractBankAccountNameById(@Param("id") Long id);

    /**
     * 根据代理ID查询是否存在首选代理账户
     *
     * @param fkAgentId
     * @return
     */
    Integer getIsDefaultContractAccount(@Param("fkAgentId") Long fkAgentId,@Param("id") Long id);

    List<AgentContractAccountVo> getAgentContractAccountExist(@Param("companyId") Long companyId,
                                                              @Param("bankAccount") String bankAccount,
                                                              @Param("bankAccountNum") String bankAccountNum);

    AgentContractAccountVo findBranchAccountInfoById(@Param("id") Long id);

    AgentContractAccount getFirstAgentContractAccountByAgentId(Long agentId);

    List<SelItem> getAgentContractPersonMobile(@Param("agentIds") Set<Long> agentIds);

    List<Long> getAgentContractAccountPaymentFrom(@Param("accountIds") List<Long> accountIds);

}