package com.get.aisplatformcenterap.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 发版信息用户已读记录 实体类
 */
@Data
@TableName("r_release_info_user_read")
public class RReleaseInfoUserRead extends BaseEntity implements Serializable {
    /**
     * 发版信息Id
     */
    @ApiModelProperty(value = "发版信息Id")
    private Long fkReleaseInfoId;
    /**
     * 平台应用CODE
     */
    @ApiModelProperty(value = "平台应用CODE")
    private String fkPlatformCode;
    /**
     * 平台应用对应的用户Id，AIS=fk_staff_id/PARTNER=fk_partner_user_id
     */
    @ApiModelProperty(value = "平台应用对应的用户Id，AIS=fk_staff_id/PARTNER=fk_partner_user_id")
    private Long fkPlatformUserId;

}

