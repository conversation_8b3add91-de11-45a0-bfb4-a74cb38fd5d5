package com.get.institutioncenter.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.CacheKeyConstants;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.start.constant.AppConstant;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.BeanUtil;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestHeaderHandler;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.institutioncenter.component.TranslationHelp;
import com.get.institutioncenter.dao.NewsCompanyMapper;
import com.get.institutioncenter.dao.NewsMapper;
import com.get.institutioncenter.dao.NewsTypeMapper;
import com.get.institutioncenter.dao.RNewsTypeMapper;
import com.get.institutioncenter.dto.SendNewsMailContext;
import com.get.institutioncenter.dto.*;
import com.get.institutioncenter.dto.query.NewsQueryDto;
import com.get.institutioncenter.entity.*;
import com.get.institutioncenter.service.*;
import com.get.institutioncenter.vo.*;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.remindercenter.dto.AliyunSendMailDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.RemindTemplate;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.remindercenter.vo.AliyunSendMailVo;
import com.get.salecenter.feign.ISaleCenterClient;
import com.google.common.collect.Maps;
import feign.Request;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateTime.now;

/**
 * <AUTHOR>
 * @DATE: 2020/8/18
 * @TIME: 17:48
 * @Description: 新闻业务类
 **/
@Service
@Slf4j
public class NewsServiceImpl extends BaseServiceImpl<NewsMapper, News> implements INewsService {

    //公开用户访问桶网址
    public final static String OSS_IMAGES_DEV_URL = "https://hti-ais-images-dev-1301376564.cos.ap-shanghai.myqcloud.com/";
    //    public final static String OSS_IMAGES_PRD_URL = "https://get-bms-images-prd-1304425382.cos.ap-shanghai.myqcloud.com";
    public final static String OSS_IMAGES_PRD_URL = "https://hti-public-image-prd-1301376564.cos.ap-shanghai.myqcloud.com";
    //    public final static String OSS_IMAGES_TEST_URL = "https://get-bms-images-test-1304425382.cos.ap-shanghai.myqcloud.com";
    public final static String OSS_IMAGES_TEST_URL = "https://hti-ais-images-dev-1301376564.cos.ap-shanghai.myqcloud.com/";

    @Resource
    private NewsMapper newsMapper;
    @Resource
    private NewsTypeMapper newsTypeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private INewsCompanyService newsCompanyService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private NewsCompanyMapper newsCompanyMapper;
    @Resource
    private INewsTypeService newsTypeService;
    @Resource
    private ITranslationMappingService translationMappingService;
    @Resource
    private GetRedis redisClient;
    @Resource
    private TranslationHelp translationHelp;
    @Resource
    private RNewsTypeMapper rNewsTypeMapper;
    @Lazy
    @Resource
    private IAreaCountryService areaCountryService;
    @Lazy
    @Resource
    private IInstitutionProviderService institutionProviderService;
    @Lazy
    @Resource
    private IInstitutionService institutionService;
    @Lazy
    @Resource
    private IInstitutionCourseService institutionCourseService;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private ISaleCenterClient saleCenterClient;

    @Override
    public List<NewsVo> datas(NewsQueryDto newsVo, Page page) {
        if (GeneralTool.isNotEmpty(newsVo.getCompanyId())) {
            if (!SecureUtil.validateCompany(newsVo.getCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }

        }
        //获取分页数据
        LambdaQueryWrapper<News> wrapper = new LambdaQueryWrapper();
        IPage<News> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<NewsVo> newDtos = newsMapper.datas(pages, newsVo);
        page.setAll((int) pages.getTotal());
        //查出新闻关系表
        List<Long> ids = newDtos.stream().map(NewsVo::getId).collect(Collectors.toList());
        List<RNewsTypeVo> rNewsTypeByNewIds = rNewsTypeMapper.getRNewsByNewIds(ids, newsVo.getFkTableName(), newsVo.getFkTableId(), newsVo.getFkNewsTypeId());

        if (GeneralTool.isNotEmpty(rNewsTypeByNewIds)) {
            newDtos.stream().forEach(d -> {
                List<RNewsTypeVo> rNewsTypeVos = new ArrayList<>();
                rNewsTypeByNewIds.stream().forEach(type -> {
                    if (d.getId().equals(type.getFkNewsId())) {
                        //如果不是公告栏新闻
                        if ("institution_news_bulletin_board".equals(type.getFkTableName())) {
                            type.setTargetTypeName(TableEnum.INSTITUTION_NEWS_BULLETIN_BOARD.value);
                            rNewsTypeVos.add(type);
                            return;
                        }
                        //获取对应的名称
                        RNewsTypeVo rNewName = rNewsTypeMapper.getRNewName(type.getFkTableName(), type.getFkTableId());
                        Optional.ofNullable(rNewName).ifPresent(dd -> type.setTargetName(dd.getTargetName()));
                        //目标类型
                        type.setTargetTypeName(TableEnum.getValue(type.getFkTableName()));
                        rNewsTypeVos.add(type);


                    }

                });
                d.setRNewsTypeDtos(rNewsTypeVos);
            });
        }
        //新闻类型ids
        Set<Long> newsTypeIds = newDtos.stream().map(NewsVo::getFkNewsTypeId).collect(Collectors.toSet());
        //根据新闻类型ids获取名称
        Map<Long, String> newTypeNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(newsTypeIds)) {
            newTypeNamesByIds = newsTypeService.getNewTypeNamesByIds(newsTypeIds);
        }

//        for (NewsVo newsDto : newDtos) {
//                     /*if (CheckUtils.isNotEmpty(newsDto.getFkTableName())) {
//                newsDto.setTargetTypeName(TableEnum.getValue(newsDto.getFkTableName()));
//            }
//            if (CheckUtils.isNotEmpty(newsDto.getFkNewsTypeId())) {
//                newsDto.setNewsTypeName(newTypeNamesByIds.get(newsDto.getFkNewsTypeId()));
//            }
//            if (CheckUtils.isNotEmpty(newsDto.getFkTableId())) {
//                newsDto.setTargetName(newsMapper.getTargetName(newsDto.getFkTableName(), newsDto.getFkTableId()));
//            }*/
//        }
        String language = SecureUtil.getLocale();

        if (GeneralTool.isNotEmpty(newDtos) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(newDtos, ProjectKeyEnum.getInitialValue(language));
        }
        setCompanyName(newDtos);

        return newDtos;
    }

    private void setCompanyName(List<NewsVo> collect) {
        if (GeneralTool.isNotEmpty(collect)) {
            Map<Long, String> companyMap = getCompanyMap();
            //新闻ids
            Set<Long> newIds = collect.stream().map(NewsVo::getId).collect(Collectors.toSet());
            //根据新闻ids获取公司ids
            Map<Long, Set<Long>> companyIdsByNewIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(newIds)) {
                companyIdsByNewIds = newsCompanyService.getCompanyIdsByNewIds(newIds);
            }
            for (NewsVo newsVo : collect) {
                StringBuilder builder = new StringBuilder();
                Set<Long> companyIds = companyIdsByNewIds.get(newsVo.getId());
                List<Long> companyIds1 = SecureUtil.getCompanyIds();
                List<Long> companyIds2 = companyIds.stream().filter(companyId -> companyIds1.contains(companyId)).collect(Collectors.toList());
                if (GeneralTool.isNotEmpty(companyIds2)) {
                    for (Long companyId : companyIds2) {
                        String name = companyMap.get(companyId);
                        builder.append(name).append("，");
                    }
                    newsVo.setCompanyName(sub(builder));
                }
                StringJoiner stringJoiner = new StringJoiner(" ");
                if (GeneralTool.isNotEmpty(newsVo.getPublicLevel())) {
                    List<String> result = Arrays.asList(newsVo.getPublicLevel().split(","));
                    for (String name : result) {
                        stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
                    }
                    newsVo.setPublicLevelName(stringJoiner.toString());
                }
            }
        }
    }


    private Map<Long, String> getCompanyMap() {
        Result<List<CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<CompanyTreeVo> companyTreeVos = result.getData();
        //初始为5的map
        Map<Long, String> companyMap = new HashMap<>(5);
        if (GeneralTool.isNotEmpty(companyTreeVos)) {
            companyMap = companyTreeVos.stream().collect(Collectors.toMap(CompanyTreeVo::getId, CompanyTreeVo::getShortName));
        }
        return companyMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addNews(NewsDto newsDto) {

        newsDto.setFkTableName(newsDto.getTableName());
        News news = BeanCopyUtils.objClone(newsDto, News::new);
        utilService.updateUserInfoToEntity(news);
        int i = newsMapper.insert(news);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isNotEmpty(newsDto.getRNewsTypeVo())) {
            for (RNewsTypeDto rNewsTypeDto : newsDto.getRNewsTypeVo()) {
                RNewsType rNewsType = BeanCopyUtils.objClone(rNewsTypeDto, RNewsType::new);
                utilService.updateUserInfoToEntity(rNewsType);
                rNewsType.setFkNewsId(news.getId());
                rNewsTypeMapper.insertSelective(rNewsType);
            }
        }
        NewsCompany newsCompany = new NewsCompany();
        newsCompany.setFkNewsId(news.getId());
        if (GeneralTool.isNotEmpty(newsDto.getCompanyId())) {
            newsCompany.setFkCompanyId(newsDto.getCompanyId());
        } else {
            newsCompany.setFkCompanyId(SecureUtil.getFkCompanyId());
        }
        utilService.updateUserInfoToEntity(newsCompany);
        newsCompanyMapper.insertSelective(newsCompany);
        return news.getId();
    }

    private String sub(StringBuilder sb) {
        if (GeneralTool.isEmpty(sb)) {
            return null;
        }
        String substring = null;
        int i = sb.lastIndexOf("，");
        if (i != -1) {
            substring = sb.substring(0, i);
        }
        return substring;
    }

    @Override
    public NewsVo findNewsById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        News news = newsMapper.selectById(id);
        if (GeneralTool.isEmpty(news)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        NewsVo newsVo = BeanCopyUtils.objClone(news, NewsVo::new);
        String tableName = TableEnum.NEWS.key;
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(tableName);
        attachedVo.setFkTableId(id);
        List<MediaAndAttachedVo> mediaAndAttachedVo = attachedService.getMediaAndAttachedDto(attachedVo);
        newsVo.setMediaAndAttachedDtos(mediaAndAttachedVo);
        List<RNewsTypeVo> rNewsTypeVos = rNewsTypeMapper.getRNewsTypeByNewIds(newsVo.getId());
        if (GeneralTool.isNotEmpty(rNewsTypeVos)) {
            for (RNewsTypeVo rNewsTypeVo : rNewsTypeVos) {
                if (GeneralTool.isNotEmpty(rNewsTypeVo.getFkTableName())) {
                    rNewsTypeVo.setTargetTypeName(TableEnum.getValue(rNewsTypeVo.getFkTableName()));
//                    newsVo.setTableName(rNewsTypeVo.getFkTableName());
                }
                if (GeneralTool.isNotEmpty(rNewsTypeVo.getFkNewsTypeId())) {
                    rNewsTypeVo.setNewsTypeName(newsTypeMapper.getNewNameById(rNewsTypeVo.getFkNewsTypeId()));
                }
                if (GeneralTool.isNotEmpty(rNewsTypeVo.getFkTableId())) {
                    rNewsTypeVo.setTargetName(newsMapper.getTargetName(rNewsTypeVo.getFkTableName(), rNewsTypeVo.getFkTableId()));
                }
            }
            newsVo.setRNewsTypeDtos(rNewsTypeVos);
        }
        StringJoiner stringJoiner = new StringJoiner(" ");
        if (GeneralTool.isNotEmpty(newsVo.getPublicLevel())) {
            List<String> result = Arrays.asList(newsVo.getPublicLevel().split(","));
            for (String name : result) {
                stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
            }
            newsVo.setPublicLevelName(stringJoiner.toString());
        }

        StringJoiner stringJoiner1 = new StringJoiner("; ");
        if (GeneralTool.isNotEmpty(newsVo.getFkNewsTypeIdRecommend())) {
            List<String> newTypeRecomnends = Arrays.asList(newsVo.getFkNewsTypeIdRecommend().split(","));
            for (String typeId : newTypeRecomnends) {
                String typeName = newsTypeMapper.getNewNameById(Long.valueOf(typeId));
                stringJoiner1.add(typeName);
            }
            newsVo.setNewTypeNames(stringJoiner1.toString());
        }


        newsVo.setFkTableName(tableName);


        String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(newsVo) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(Collections.singletonList(newsVo), ProjectKeyEnum.getInitialValue(language));
        }


        return newsVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        //TODO 改过
        //News news = findNewsById(id);
        NewsVo news = findNewsById(id);
        if (news == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        newsMapper.deleteById(id);
        //同时删除该表id下的所有媒体附件
        List<MediaAndAttached> mediaAndAttachedByTableIds = attachedService.findMediaAndAttachedByTableId(id, TableEnum.NEWS.key, null);
        for (MediaAndAttached mediaAndAttached : mediaAndAttachedByTableIds) {
            attachedService.deleteMediaAttached(mediaAndAttached.getId());
        }
        //删除新闻关系表
        rNewsTypeMapper.delete(Wrappers.<RNewsType>lambdaQuery().eq(RNewsType::getFkNewsId, id));
        newsCompanyMapper.delete(Wrappers.<NewsCompany>lambdaQuery().eq(NewsCompany::getFkNewsId, id));
        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.NEWS.key, id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public NewsVo updateNews(NewsDto newsDto) {
        if (newsDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(newsDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        News rs = newsMapper.selectById(newsDto.getId());
        if (rs == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        /*   newsDto.setFkTableName(newsDto.getTableName());*/
        News news = BeanCopyUtils.objClone(newsDto, News::new);
        utilService.updateUserInfoToEntity(news);
        newsMapper.updateById(news);
        //同时删除该表id下的所有媒体附件
        List<MediaAndAttached> mediaAndAttachedByTableIds = attachedService.findMediaAndAttachedByTableId(news.getId(), TableEnum.NEWS.key, FileTypeEnum.INSTITUTION_NEWS_FILE.key);
        for (MediaAndAttached mediaAndAttached : mediaAndAttachedByTableIds) {
            attachedService.deleteMediaAttached(mediaAndAttached.getId());
        }
        MediaAndAttachedDto mediaAttachedVo = newsDto.getMediaAttachedVo();
        if (GeneralTool.isNotEmpty(mediaAttachedVo)) {
            //设置插入的表
            String tableName = TableEnum.NEWS.key;
            mediaAttachedVo.setFkTableName(tableName);
            mediaAttachedVo.setTypeKey(FileTypeEnum.INSTITUTION_NEWS_FILE.key);
            attachedService.addMediaAndAttached(mediaAttachedVo);
        }
        //删除新闻关系表
        List<RNewsTypeVo> rNewsTypeByNewIds = rNewsTypeMapper.getRNewsTypeByNewIds(newsDto.getId());
        if (GeneralTool.isNotEmpty(rNewsTypeByNewIds)) {
            rNewsTypeByNewIds.stream().forEach(d -> rNewsTypeMapper.deleteById(d.getId()));
        }
        //新增新闻关系表
        if (GeneralTool.isNotEmpty(newsDto.getRNewsTypeVo())) {
            BeanCopyUtils.copyListProperties(newsDto.getRNewsTypeVo(), RNewsType::new).stream().forEach(d -> {
                d.setFkNewsId(newsDto.getId());
                rNewsTypeMapper.insertSelective(d);
            });
        }

        return findNewsById(news.getId());
    }


    @Override
    public List<Map<String, Object>> findTargetType() {
        return TableEnum.enumsTranslation2Arrays(TableEnum.NEW_TARGET_TYPE);
    }

    @Override
    public void deleteNewsByTableId(Long id, String tableName) {
        LambdaQueryWrapper<News> wrapper = new LambdaQueryWrapper();
        wrapper.eq(News::getFkTableName, tableName);
        wrapper.eq(News::getFkTableId, id);
        int j = newsMapper.delete(wrapper);
        if (j < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public List<NewsVo> getNewsDtoByTarget(NewsDto newsDto) {
//        LambdaQueryWrapper<News> wrapper = new LambdaQueryWrapper();
//        if (GeneralTool.isNotEmpty(newsDto)) {
//            if (GeneralTool.isNotEmpty(newsDto.getFkNewsTypeId())) {
//                wrapper.eq(News::getFkNewsTypeId,newsDto.getFkNewsTypeId());
//            }
//            if (GeneralTool.isNotEmpty(newsDto.getTitle())) {
//                wrapper.like(News::getTitle, newsDto.getTitle());
//            }
//            wrapper.eq(News::getFkTableName,newsDto.getFkTableName());
//            wrapper.eq(News::getFkTableId,newsDto.getFkTableId());
//        }
//        List<News> newss = newsMapper.selectList(wrapper);
//        List<NewsVo> newsDtos = new ArrayList<>();
//        for (News news : newss) {
//            NewsVo newsDto = BeanCopyUtils.objClone(news, NewsVo::new);
//            if (GeneralTool.isNotEmpty(newsDto.getFkNewsTypeId())) {
//                newsDto.setNewsTypeName(newsTypeMapper.getNewNameById(newsDto.getFkNewsTypeId()));
//            }
//            newsDtos.add(newsDto);
//        }
//        return newsDtos;
        return null;
    }

    @Override
    public List<BaseSelectEntity> findTarget(String tableName) {
        List<Long> companyIds = SecureUtil.getCompanyIds();
        return newsMapper.getNewsTarget(tableName,companyIds);
    }

    @Override
    public List<CompanyTreeVo> getNewRelation(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return newsCompanyService.getNewCompanyRelation(id);
    }

    @Override
    public void editNewsCompany(List<NewsCompanyDto> validList) {
        newsCompanyService.editNewsCompany(validList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MediaAndAttachedVo> addNewsMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            String tableName = TableEnum.NEWS.key;
            mediaAndAttachedDto.setFkTableName(tableName);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public List<MediaAndAttachedVo> getNewsMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.NEWS.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    /**
     * 所有新闻下拉框
     *
     * @Date 11:54 2021/7/28
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getAllNewsSelect() {
        return newsMapper.getAllNewsSelect();
    }

    /**
     * feign调用 查询新闻标题Map
     *
     * @Date 12:40 2021/7/28
     * <AUTHOR>
     */
    @Override
    public Map<Long, String> getNewsTitlesByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        LambdaQueryWrapper<News> wrapper = new LambdaQueryWrapper();
        wrapper.in(News::getId, ids);
        List<News> newsList = newsMapper.selectList(wrapper);
        for (News news : newsList) {
            map.put(news.getId(), news.getTitle());
        }
        return map;
    }

    /**
     * 发送新闻电邮通知
     *
     * @param id
     */
    @Override
    public void sendNewsMail(Long id) throws Exception {

        //获取新闻实体
        SendNewsMailContext sendNewsMailContext = doGetNews(id);

        //点击时，只有类型为【国家】【学校提供商】【学校】【课程】的新闻才可以发送，其他类型，提示：新闻类型不支持发送新闻邮件。
        validateSend(sendNewsMailContext);

        sendNewsMailContext.setCreateDate(new Date());

        //邮件附件才能发送 获取邮件附件
        doGetNewsMediaAndAttached(sendNewsMailContext);

        //获取系统配置参数
        doGetSystemConfig(sendNewsMailContext);

        //根据配置构建连接并发送邮箱
        doCreateUrlAndSendMail(sendNewsMailContext);

        //更新发送时间
        doUpdateSendEmailTime(sendNewsMailContext);
    }

    /**
     * HTI历史新闻导入校验是否存在
     *
     * @param newsDto
     * @return
     */
    @Override
    public Boolean checkNews(NewsDto newsDto) {
        LambdaQueryWrapper<News> newsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        newsLambdaQueryWrapper.eq(News::getTitle, newsDto.getTitle());
        if (GeneralTool.isNotEmpty(newsDto.getDescription())) {
            newsLambdaQueryWrapper.eq(News::getDescription, newsDto.getDescription());
        }
        if (GeneralTool.isNotEmpty(newsDto.getFkNewsTypeId())) {
            newsLambdaQueryWrapper.eq(News::getFkNewsTypeId, newsDto.getFkNewsTypeId());
        }
        if (GeneralTool.isNotEmpty(newsDto.getGmtCreate())) {
            newsLambdaQueryWrapper.eq(News::getGmtCreate, newsDto.getGmtCreate());
        }
        if (GeneralTool.isNotEmpty(newsDto.getGmtCreateUser())) {
            newsLambdaQueryWrapper.eq(News::getGmtCreateUser, newsDto.getGmtCreateUser());
        }
        List<News> list = this.list(newsLambdaQueryWrapper);
        return GeneralTool.isNotEmpty(list) && list.size() > 0 ? true : false;
    }

    /**
     * 发送新闻电邮to代理
     *
     * @param newEmailToAgentDto
     * @param headerMap
     * @param user
     * @param locale
     */
    @Override
    @Async
    public void sendNewsMailtoAgent(NewEmailToAgentDto newEmailToAgentDto, Map<String, String> headerMap, UserInfo user, String locale) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        Boolean flag = redisClient.setNx(CacheKeyConstants.AGENT_NEWS_EMAIL_LOCK_KEY + newEmailToAgentDto.getId(), 1, 1800L);
        if (flag) {
            try {
                //获取新闻实体
                SendNewAgentContextVo sendNewsMailContext = getEmailNew(newEmailToAgentDto);
                //获取代理账号
                getAgentEmail(sendNewsMailContext);
                //发送邮件
                sendNewsMailAgent(BeanCopyUtils.objClone(sendNewsMailContext, SendNewAgentContextDto::new), user, locale);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                //释放锁
                redisClient.del(CacheKeyConstants.AGENT_NEWS_EMAIL_LOCK_KEY + newEmailToAgentDto.getId());
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("REPEATED_PUSH_EMAILS"));
        }
    }

    /**
     * 发送新闻电邮to所有代理
     *
     * @param id
     * @param headerMap
     * @param user
     * @param locale
     * @param type      1Hubs/2市场
     */
    @Override
    @Async
    public void sendNewsMailtoAllAgent(Long id, Map<String, String> headerMap, UserInfo user, String locale, Integer type) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        Boolean flag = redisClient.setNx(CacheKeyConstants.AGENT_NEWS_EMAIL_LOCK_KEY + id, 1, 1800L);
        if (flag) {
            try {
                //封装新闻实体
                //AliyunSendMailVo aliyunSendMailVo = getEmailNewAllAgent(id, type); // 发送方式改为定时任务后，这里就不需要查询模版，把这一步放到定时任务中。
                AliyunSendMailDto aliyunSendMailVo = new AliyunSendMailDto();
                //获取代理账号
                aliyunSendMailVo.setToEmails(getAgentEmailAll(id, type));
                aliyunSendMailVo.setType(type);
                // 根据对应的邮箱，把要发送的信息插入到对应的邮件队列表中
                sendNewsMailAgentAll(id, aliyunSendMailVo, user, type);
            } catch (Exception e) {
                log.error("邮件推送：" + e.getMessage());
                e.printStackTrace();
            } finally {
                //释放锁
                redisClient.del(CacheKeyConstants.AGENT_NEWS_EMAIL_LOCK_KEY + id);
            }
        } else {
            log.error("邮件推送：锁表");
            throw new GetServiceException(LocaleMessageUtils.getMessage("REPEATED_PUSH_EMAILS"));
        }
    }

    /**
     * 发送新闻电邮to自己
     *
     * @param id
     */
    @Override
    public void sendNewsMailtoMe(Long id) {
        //封装新闻实体
        AliyunSendMailDto aliyunSendMailVo = getEmailNewAllAgent(id, 0);
        StaffVo staffVo = permissionCenterClient.getStaffByLoginId(SecureUtil.getLoginId());
        if (GeneralTool.isEmpty(staffVo.getEmail())) {
            return;
        }
        //获取代理账号
        aliyunSendMailVo.setToEmails(new String[]{staffVo.getEmail()});
        aliyunSendMailVo.setNewsId(id);
        Result<Boolean> booleanResult = reminderCenterClient.aliyunSendMailNew(BeanCopyUtils.objClone(aliyunSendMailVo, AliyunSendMailDto::new));
        if (!booleanResult.isSuccess()) {
            log.error("邮件推送：" + booleanResult.getMessage());
        }
    }

    /**
     * 获取代理邮箱
     *
     * @param newsId
     * @param type   1Hubs/2市场
     * @return
     */
    private String[] getAgentEmailAll(Long newsId, Integer type) {
        //封装请求Vo
//        Set<String> agentEmails = saleCenterClient.getNewAgentAllEmails(newsId).getData();
        Set<String> agentEmails = saleCenterClient.getNewAgentAllEmails(newsId, type, new Request.Options(500, TimeUnit.SECONDS, 500, TimeUnit.SECONDS, true)).getData();
        return agentEmails.toArray(new String[agentEmails.size()]);
    }


    /**
     * @param newsId
     * @param
     * @param user
     * @param type   1Hubs/2市场
     */
    private void sendNewsMailAgentAll(Long newsId, AliyunSendMailDto aliyunSendMailVo, UserInfo user, Integer type) {
        //封装邮件信息，发送邮件
        if (GeneralTool.isEmpty(aliyunSendMailVo.getToEmails())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_agent_reminder"));
        }
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.NEWS_EMAIL_STAFF.key, 3).getData();
        //推送人员
        String configValue3 = companyConfigMap.get(user.getFkCompanyId());
        List<String> staffEmailList = new ArrayList<>(JSON.parseArray(configValue3, String.class));
        List<String> agentEmailList = Arrays.stream(aliyunSendMailVo.getToEmails()).collect(Collectors.toList());
        if (GeneralTool.isNotEmpty(staffEmailList)) {
            agentEmailList.addAll(staffEmailList);
        }

        Map<Long, String> companyConfig4Map = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.NEWS_EMAIL_STAFF.key, 4).getData();
        String configValue4 = companyConfig4Map.get(user.getFkCompanyId());
        if (GeneralTool.isNotEmpty(configValue4)) {
            List<String> domainNameList = new ArrayList<>(JSON.parseArray(configValue4, String.class));
            if (GeneralTool.isNotEmpty(domainNameList)) {
                agentEmailList.removeIf(item -> domainNameList.stream().anyMatch(item::contains));
            }
        }
        News news = newsMapper.selectById(newsId);
        StaffVo staffVo = permissionCenterClient.getStaffByLoginId(news.getGmtCreateUser());
        if (GeneralTool.isNotEmpty(staffVo)) {
            if (GeneralTool.isNotEmpty(staffVo.getEmail())) {
                agentEmailList.add(staffVo.getEmail());
            }
        }

        //必发邮箱
        List<String> defaultEmail = reminderCenterClient.getDefaultSendEmail().getData();
        if (GeneralTool.isNotEmpty(defaultEmail)) {
            agentEmailList.addAll(defaultEmail);
        }

        aliyunSendMailVo.setToEmails(agentEmailList.toArray(new String[0]));
        aliyunSendMailVo.setNewsId(newsId);
        // 筛选 （已发送、不合法、退订邮箱)后，保存到待发送新闻邮件队列
        Result<Boolean> booleanResult = reminderCenterClient.addTask2NewsEmailQueen(BeanCopyUtils.objClone(aliyunSendMailVo, AliyunSendMailDto::new));
        if (!booleanResult.isSuccess()) {
            log.error("邮件推送：" + booleanResult.getMessage());
        }
        //设置发送时间
        news.setSendEmailAllAgentTime(new Date());
        utilService.setUpdateInfo(news);
        newsMapper.update(news, new LambdaQueryWrapper<News>().eq(News::getId, newsId));
    }

    private AliyunSendMailDto getEmailNewAllAgent(Long id, Integer type) {
        AliyunSendMailDto aliyunSendMailVo = new AliyunSendMailDto();
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        News news = getById(id);
        if (GeneralTool.isEmpty(news)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        aliyunSendMailVo.setTitle(news.getTitle());
        aliyunSendMailVo.setTemplate(news.getDescription());
        //获取附件
        //邮件附件类型才列出来
        String link = getfileLink(news);
        if (GeneralTool.isNotEmpty(link)) {
            aliyunSendMailVo.setTemplate(aliyunSendMailVo.getTemplate() + link);
        }
        //邮件模板
        RemindTemplate remindTemplate = reminderCenterClient.getRemindTemplateByTypeKey(ProjectKeyEnum.AGENT_NEWS_EMAIL.key).getData();
        aliyunSendMailVo.setTemplate(remindTemplate.getEmailTemplate().replace("#{news}", aliyunSendMailVo.getTemplate()).replace("#{type}", type.toString()).replace("#{display}", "block"));
        return aliyunSendMailVo;
    }

    /**
     * 发送新闻电邮to代理
     *
     * @param sendNewsMailContext
     * @param user
     * @param locale
     */
    private void sendNewsMailAgent(SendNewAgentContextDto sendNewsMailContext, UserInfo user, String locale) {
        //封装邮件信息，发送邮件
        if (GeneralTool.isEmpty(sendNewsMailContext.getAgentEmails())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_agent_reminder"));
        }

        List<String> agentEmailList = Arrays.stream(sendNewsMailContext.getAgentEmails()).collect(Collectors.toList());
        //推送部门
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.NEWS_EMAIL_DP.key, 3).getData();
        String configValue3 = companyConfigMap.get(user.getFkCompanyId());
        List<String> departmentList = new ArrayList<>(JSON.parseArray(configValue3, String.class));
        if (GeneralTool.isNotEmpty(departmentList)) {
            Result<List<String>> result = permissionCenterClient.getPushDepartmentStaffEmail(sendNewsMailContext.getFkCountryId(), sendNewsMailContext.getFkInstitutionId(), departmentList);
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
            List<String> staffEmailList = result.getData();
            if (GeneralTool.isNotEmpty(staffEmailList)) {
                agentEmailList.addAll(staffEmailList);
            }
        }

        //默认发送人
        Map<Long, String> companyConfig2Map = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.NEWS_EMAIL_STAFF.key, 2).getData();
        String configValue2 = companyConfig2Map.get(user.getFkCompanyId());
        List<String> defaultEmailList = new ArrayList<>(JSON.parseArray(configValue2, String.class));
        if (GeneralTool.isNotEmpty(defaultEmailList)) {
            agentEmailList.addAll(defaultEmailList);
        }

        //创建人
        if (GeneralTool.isNotEmpty(sendNewsMailContext.getCreateStaffEmail())) {
            agentEmailList.add(sendNewsMailContext.getCreateStaffEmail());
        }


        Map<Long, String> companyConfig4Map = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.NEWS_EMAIL_STAFF.key, 4).getData();
        String configValue4 = companyConfig4Map.get(user.getFkCompanyId());
        List<String> domainNameList = new ArrayList<>(JSON.parseArray(configValue4, String.class));
        if (GeneralTool.isNotEmpty(domainNameList)) {
            agentEmailList.removeIf(item -> domainNameList.stream().anyMatch(item::contains));
        }
        AliyunSendMailVo aliyunSendMailVo = new AliyunSendMailVo();
        aliyunSendMailVo.setToEmails(agentEmailList.toArray(new String[0]));
        aliyunSendMailVo.setTitle(sendNewsMailContext.getTitle());
        aliyunSendMailVo.setTemplate(sendNewsMailContext.getDescription());
        aliyunSendMailVo.setNewsId(sendNewsMailContext.getId());
        aliyunSendMailVo.setType(0);
//        reminderCenterClient.batchSendEmailCustom(mailVo);
        // 筛选 （已发送、不合法、退订邮箱)后，保存到待发送新闻邮件队列
        Result<Boolean> booleanResult = reminderCenterClient.addTask2NewsEmailQueen(BeanCopyUtils.objClone(aliyunSendMailVo, AliyunSendMailDto::new));
        if (!booleanResult.isSuccess()) {
            log.error("邮件推送：" + booleanResult.getMessage());
        }
        //设置发送时间
        News news = new News();
        news.setSendEmailAgentTime(new Date());

        NewEmailToAgentJsonDto newEmailToAgentJsonDto = new NewEmailToAgentJsonDto();
        newEmailToAgentJsonDto.setStepIds(sendNewsMailContext.getStepIds());
        if (GeneralTool.isNotEmpty(sendNewsMailContext.getIntakeTimeStart())) {
            newEmailToAgentJsonDto.setIntakeTimeStart(DateUtil.formatDate(sendNewsMailContext.getIntakeTimeStart()));
        }
        if (GeneralTool.isNotEmpty(sendNewsMailContext.getIntakeTimeEnd())) {
            newEmailToAgentJsonDto.setIntakeTimeEnd(DateUtil.formatDate(sendNewsMailContext.getIntakeTimeEnd()));
        }
        news.setSendEmailOfferItemSteps(JSONObject.toJSONString(newEmailToAgentJsonDto));
        utilService.setUpdateInfo(news);
        newsMapper.update(news, new LambdaQueryWrapper<News>().eq(News::getId, sendNewsMailContext.getId()));
    }

    /**
     * 获取代理账号
     *
     * @param sendNewAgentContextVo
     */
    private void getAgentEmail(SendNewAgentContextVo sendNewAgentContextVo) {
        //封装请求Vo
        // Set<String> agentEmails = saleCenterClient.getNewAgentEmails(BeanUtil.copy(sendNewAgentContextVo, NewEmailGetAgentDto.class)).getData();
        Set<String> agentEmails = saleCenterClient.getNewAgentEmails(BeanUtil.copy(sendNewAgentContextVo, NewEmailGetAgentDto.class), new Request.Options(500, TimeUnit.SECONDS, 500, TimeUnit.SECONDS, true)).getData();
        if (GeneralTool.isNotEmpty(agentEmails)) {
            sendNewAgentContextVo.setAgentEmails(agentEmails.toArray(new String[agentEmails.size()]));
        }
    }

    /**
     * 配置构建连接并发送
     *
     * @param sendNewsMailContext
     */
//    private void doCreateUrlAndSendMail(SendNewsMailContext sendNewsMailContext) throws Exception {
//        ConfigVo dpConfig = sendNewsMailContext.getDpConfig();
//        ConfigVo iaeApiConfig = sendNewsMailContext.getIaeApiConfig();
//        News news = sendNewsMailContext.getNews();
//        if (GeneralTool.isEmpty(news)
//                || GeneralTool.isEmpty(dpConfig)
//                || GeneralTool.isEmpty(iaeApiConfig)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
//        }
//
//        //http://crm.iaechina.net.cn/Interface/iSendEmail.aspx?mailFromAddress={0}&mailToAddress={1}&mailSubject={2}&mailBody={3}&time={4}&sign={5}，一共6个参数。
//        String key = iaeApiConfig.getValue1();
////        String dpNumStr = dpConfig.getValue3();
//        String apiUrl = dpConfig.getValue4();
//
//        //发件人
//        String mailFromAddress = "";
//        if (GeneralTool.isNotEmpty(SecureUtil.getLoginId())) {
//            StaffVo staffVo = permissionCenterClient.getStaffByCreateUser(SecureUtil.getLoginId()).getData();
//            if (GeneralTool.isNotEmpty(staffVo)) {
//                mailFromAddress = GeneralTool.isEmpty(staffVo.getEmail()) ? "" : staffVo.getEmail();
//            }
//        }
//
////        String[] dpNumArray = dpNumStr.split(",");
////        if (GeneralTool.isEmpty(dpNumArray)){
////            throw new GetServiceException(LocaleMessageUtils.getMessage("missing_required_configuration"));
////        }
//
//        //邮件标题
//        String mailSubject = "[{0}][新增新闻信息：{1}]通知";
//        String title = news.getTitle();
//        Long countryId = null;
//        String countryName = "";
//        String typeName = "";
//
//        //RNewsType rNewsType = rNewsTypeMapper.selectOne(Wrappers.<RNewsType>lambdaQuery().eq(RNewsType::getFkNewsId, news.getId()));
//        List<RNewsType> rNewsTypes = rNewsTypeMapper.selectList(Wrappers.<RNewsType>lambdaQuery().eq(RNewsType::getFkNewsId, news.getId()));
//        RNewsType rNewsType = rNewsTypes.get(0);
//        if (TableEnum.INSTITUTION_COUNTRY.key.equals(news.getFkTableName()) || TableEnum.INSTITUTION_COUNTRY.key.equals(rNewsType.getFkTableName())) {
//            countryName = areaCountryService.getCountryChnNameById(rNewsType.getFkTableId());
//            countryId = rNewsType.getFkTableId();
//            typeName = countryName;
//        } else if (TableEnum.INSTITUTION_PROVIDER.key.equals(news.getFkTableName()) || TableEnum.INSTITUTION_PROVIDER.key.equals(rNewsType.getFkTableName())) {
//            //这里是学校提供商有多个业务国家,需要不同处理
//            List<InstitutionProvidersAndAreaCountryVo> institutionProvidersAndAreaCountry = institutionProviderService.getInstitutionProvidersAndAreaCountryById(rNewsType.getFkTableId());
//            if (GeneralTool.isEmpty(institutionProvidersAndAreaCountry)) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("institution_provider_do_not_hava_area_country"));
//            }
//            institutionProvidersAndAreaCountry.forEach(institutionProvidersAndAreaCountryVo -> {
////                countryName = areaCountryService.getCountryChnNameById(institutionProvidersAndAreaCountryVo.getAreaCountryId());
////                countryId = institutionProvidersAndAreaCountryVo.getAreaCountryId();
////                typeName = countryName;
//
//            });
//
//
////            InstitutionProvider institutionProvider = institutionProviderService.getById(rNewsType.getFkTableId());
////            countryName = areaCountryService.getCountryChnNameById(institutionProvider.getFkAreaCountryId());
////            countryId = institutionProvider.getFkAreaCountryId();
////            typeName = institutionProvider.getName();
//        } else if (TableEnum.INSTITUTION.key.equals(news.getFkTableName()) || TableEnum.INSTITUTION.key.equals(rNewsType.getFkTableName())) {
//            Institution institution = institutionService.getById(rNewsType.getFkTableId());
//            countryName = areaCountryService.getCountryChnNameById(institution.getFkAreaCountryId());
//            countryId = institution.getFkAreaCountryId();
//            typeName = institution.getName() + (GeneralTool.isNotEmpty(institution.getNameChn()) ? "（" + institution.getNameChn() + "）" : "");
//
//        } else if (TableEnum.INSTITUTION_COURSE.key.equals(news.getFkTableName()) || TableEnum.INSTITUTION_COURSE.key.equals(rNewsType.getFkTableName())) {
//            InstitutionCourse institutionCourse = institutionCourseService.getById(rNewsType.getFkTableId());
//            Institution institution = institutionService.getById(institutionCourse.getFkInstitutionId());
//            countryName = areaCountryService.getCountryChnNameById(institution.getFkAreaCountryId());
//            countryId = institution.getFkAreaCountryId();
//            typeName = institutionCourse.getName();
//
//        }
//
//        mailSubject = MessageFormat.format(mailSubject, countryName, title);
//
//        //收件人
//
//        //推送部门
//        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.NEWS_EMAIL_DP.key, 3).getData();
//        String configValue3 = companyConfigMap.get(SecureUtil.getFkCompanyId());
//        List<String> departmentList = new ArrayList<>(JSON.parseArray(configValue3, String.class));
////        List<String> numList = Arrays.asList(dpNumArray);
//        Set<String> numSet = new HashSet<>(departmentList);
//        List<StaffVo> staffVos = permissionCenterClient.getStaffDtosByDepartmentNums(countryId, numSet).getData();
//        if (GeneralTool.isEmpty(staffVos)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("missing_required_configuration"));
//        }
//        //过滤离职
//        staffVos = staffVos.stream().filter(s -> GeneralTool.isNotEmpty(s.getIsOnDuty())).filter(StaffVo::getIsOnDuty).collect(Collectors.toList());
//        if (GeneralTool.isEmpty(staffVos)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("missing_required_configuration"));
//        }
//        String mailToAddress = staffVos.stream().map(StaffVo::getEmail).filter(Objects::nonNull).collect(Collectors.joining(";"));
//
//
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss");
//        Date createDate = GeneralTool.isNotEmpty(sendNewsMailContext.getCreateDate()) ? sendNewsMailContext.getCreateDate() : new Date();
//        String createDateStr = simpleDateFormat.format(createDate);
//
//
//        String mediaAndAttachedLinkPrefix = "";
//        RemindTemplate remindTemplate = reminderCenterClient.getRemindTemplateByTypeKey(ProjectKeyEnum.NEWS_EMAIL_NOTICE.key).getData();
//        String emailTemplate = remindTemplate.getEmailTemplate();
//
//        Map<String, String> replaceMap = Maps.newHashMap();
//        replaceMap.put("createTime", createDateStr);
//        replaceMap.put("typeName", typeName);
//        replaceMap.put("countryName", countryName);
//        replaceMap.put("newsTitle", news.getTitle());
//        replaceMap.put("newsContent", news.getDescription());
//
//        List<MediaAndAttached> mediaAndAttacheds = sendNewsMailContext.getMediaAndAttacheds();
//        if (GeneralTool.isEmpty(mediaAndAttacheds)) {
//            replaceMap.put("mediaAndAttachedLinks", " ");
//        }
//        Properties props = System.getProperties();
//        String profile = props.getProperty("spring.profiles.active");
//        if (GeneralTool.isNotEmpty(profile)) {
//            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.GRAY_CODE) || profile.equals(AppConstant.TW_CODE)) {
//                mediaAndAttachedLinkPrefix = OSS_IMAGES_PRD_URL;
//            } else if (profile.equals(AppConstant.IAE_CODE)) {
//                mediaAndAttachedLinkPrefix = OSS_IMAGES_PRD_URL;
//            } else if (profile.equals(AppConstant.TEST_CODE)) {
//                mediaAndAttachedLinkPrefix = OSS_IMAGES_TEST_URL;
//            } else {
//                mediaAndAttachedLinkPrefix = OSS_IMAGES_DEV_URL;
//            }
//        }
//
//        List<String> guids = mediaAndAttacheds.stream().map(MediaAndAttached::getFkFileGuid).collect(Collectors.toList());
//        List<FileDto> fileDtos = fileCenterClient.getFile(guids, LoggerModulesConsts.INSTITUTIONCENTER).getData();
//        Map<String, FileDto> fileDtoMap = Maps.newHashMap();
//        if (GeneralTool.isNotEmpty(fileDtos)) {
//            fileDtoMap = fileDtos.stream().collect(HashMap::new, (m, v) -> m.put(v.getFileGuid(), v), HashMap::putAll);
//        }
//        StringBuilder sb = new StringBuilder();
//        //TODO NEWS_EMAIL_NOTICE 邮件模板
//        if (GeneralTool.isNotEmpty(fileDtoMap)) {
//            sb.append("<h4>附件链接列表</h4><ol>");
//            for (MediaAndAttached mediaAndAttached : mediaAndAttacheds) {
//                FileDto fileDto = fileDtoMap.get(mediaAndAttached.getFkFileGuid());
//                if (GeneralTool.isNotEmpty(fileDto)) {
//                    String fileKey = fileDto.getFileKey();
//                    String fileName = fileDto.getFileNameOrc();
//                    String link = mediaAndAttachedLinkPrefix + fileKey;
//                    sb.append("<li><a class=\"link\" href=\"")
//                            .append(link)
//                            .append("\">")
//                            .append(fileName)
//                            .append("</a></li>");
//                }
//            }
//            sb.append("</ol>");
//        }
//        replaceMap.put("mediaAndAttachedLinks", GeneralTool.isNotEmpty(sb.toString()) ? sb.toString() : " ");
//        emailTemplate = doCreateMailBody(emailTemplate, replaceMap);
//
//        // TODO: 2022/12/13 添加邮件模板  模板中拼接附件的连接
//        String mailbody = emailTemplate;
//
//        //获取十位时间戳
//        long time = System.currentTimeMillis() / 1000;
//
//        //获取令牌sign
//        if (GeneralTool.isEmpty(key)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("missing_required_configuration"));
//        }
//        String painText = time + "&" + key;
//        MD5 md5 = MD5.create();
//        String sign = md5.digestHex(painText);
//
//        // TODO: 2022/12/9 拼接连接 并调用iae的接口
//        String requestApi = MessageFormat.format(apiUrl, mailFromAddress, mailToAddress, mailSubject, mailbody, String.valueOf(time), sign);
//        String url = requestApi.substring(0, 52);
//        Map<String, Object> paramMap = new HashMap<>();
//        paramMap.put("mailFromAddress", mailFromAddress);
//        paramMap.put("mailToAddress", mailToAddress);
//        paramMap.put("mailSubject", mailSubject);
//        paramMap.put("mailbody", mailbody);
//        paramMap.put("time", String.valueOf(time));
//        paramMap.put("sign", sign);
//
//        String resultStr = HttpUtil.post(url, paramMap);
//        JSONObject result = JSONObject.parseObject(resultStr);
//        if (!result.getBoolean("success")) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("news_emil_send_fail"));
//        }
//
//
//
//    }
    private void doCreateUrlAndSendMail(SendNewsMailContext sendNewsMailContext) throws Exception {
        //基本配置
        ConfigVo dpConfig = sendNewsMailContext.getDpConfig();
        ConfigVo iaeApiConfig = sendNewsMailContext.getIaeApiConfig();
        News news = sendNewsMailContext.getNews();
        if (GeneralTool.isEmpty(news)
                || GeneralTool.isEmpty(dpConfig)
                || GeneralTool.isEmpty(iaeApiConfig)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        //http://crm.iaechina.net.cn/Interface/iSendEmail.aspx?mailFromAddress={0}&mailToAddress={1}&mailSubject={2}&mailBody={3}&time={4}&sign={5}，一共6个参数。
        String key = iaeApiConfig.getValue1();
        String apiUrlTemplate = dpConfig.getValue4();
        if (GeneralTool.isEmpty(key) || GeneralTool.isEmpty(apiUrlTemplate)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("missing_required_configuration"));
        }


        //发件人
        String mailFromAddress = "";
        if (GeneralTool.isNotEmpty(SecureUtil.getLoginId())) {
            StaffVo staffVo = permissionCenterClient.getStaffByCreateUser(SecureUtil.getLoginId()).getData();
            if (GeneralTool.isNotEmpty(staffVo)) {
                mailFromAddress = GeneralTool.isEmpty(staffVo.getEmail()) ? "" : staffVo.getEmail();
            }
        }

//        String[] dpNumArray = dpNumStr.split(",");
//        if (GeneralTool.isEmpty(dpNumArray)){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("missing_required_configuration"));
//        }

        //国家信息
        class CountryInfo {
            final Long countryId;
            final String countryName;
            final String typeName;

            CountryInfo(Long countryId, String countryName, String typeName) {
                this.countryId = countryId;
                this.countryName = countryName;
                this.typeName = typeName;
            }
        }
        List<CountryInfo> countries = new ArrayList<>();

        //RNewsType rNewsType = rNewsTypeMapper.selectOne(Wrappers.<RNewsType>lambdaQuery().eq(RNewsType::getFkNewsId, news.getId()));
        List<RNewsType> rNewsTypes = rNewsTypeMapper.selectList(Wrappers.<RNewsType>lambdaQuery().eq(RNewsType::getFkNewsId, news.getId()));
        if (GeneralTool.isEmpty(rNewsTypes)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("news_type_mapping_not_found"));
        }
        RNewsType rNewsType = rNewsTypes.get(0);

        if (TableEnum.INSTITUTION_COUNTRY.key.equals(news.getFkTableName()) || TableEnum.INSTITUTION_COUNTRY.key.equals(rNewsType.getFkTableName())) {
            Long countryId = rNewsType.getFkTableId();
            String countryName = areaCountryService.getCountryChnNameById(rNewsType.getFkTableId());
            countries.add(new CountryInfo(countryId, countryName, countryName));
//            countryName = areaCountryService.getCountryChnNameById(rNewsType.getFkTableId());
//            countryId = rNewsType.getFkTableId();
//            typeName = countryName;
        } else if (TableEnum.INSTITUTION_PROVIDER.key.equals(news.getFkTableName()) || TableEnum.INSTITUTION_PROVIDER.key.equals(rNewsType.getFkTableName())) {
            //这里是学校提供商有多个业务国家,需要不同处理
            InstitutionProvider provider = institutionProviderService.getById(rNewsType.getFkTableId());
            if (GeneralTool.isEmpty(provider)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("institution_provider_not_found"));
            }
            List<InstitutionProvidersAndAreaCountryVo> institutionProvidersAndAreaCountry = institutionProviderService.getInstitutionProvidersAndAreaCountryById(rNewsType.getFkTableId());
            if (GeneralTool.isEmpty(institutionProvidersAndAreaCountry)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("institution_provider_do_not_hava_area_country"));
            }
            for (InstitutionProvidersAndAreaCountryVo vo : institutionProvidersAndAreaCountry) {
                Long countryId = vo.getAreaCountryId();
                String countryName = areaCountryService.getCountryChnNameById(countryId);
                countries.add(new CountryInfo(countryId, countryName, vo.getName()));
            }

//            InstitutionProvider institutionProvider = institutionProviderService.getById(rNewsType.getFkTableId());
//            countryName = areaCountryService.getCountryChnNameById(institutionProvider.getFkAreaCountryId());
//            countryId = institutionProvider.getFkAreaCountryId();
//            typeName = institutionProvider.getName();
        } else if (TableEnum.INSTITUTION.key.equals(news.getFkTableName()) || TableEnum.INSTITUTION.key.equals(rNewsType.getFkTableName())) {
            Institution institution = institutionService.getById(rNewsType.getFkTableId());
            String countryName = areaCountryService.getCountryChnNameById(institution.getFkAreaCountryId());
            Long countryId = institution.getFkAreaCountryId();
            String typeName = institution.getName() + (GeneralTool.isNotEmpty(institution.getNameChn()) ? "（" + institution.getNameChn() + "）" : "");
            countries.add(new CountryInfo(countryId, countryName, typeName));

        } else if (TableEnum.INSTITUTION_COURSE.key.equals(news.getFkTableName()) || TableEnum.INSTITUTION_COURSE.key.equals(rNewsType.getFkTableName()))
        {
            InstitutionCourse institutionCourse = institutionCourseService.getById(rNewsType.getFkTableId());
            Institution institution = institutionService.getById(institutionCourse.getFkInstitutionId());
            String countryName = areaCountryService.getCountryChnNameById(institution.getFkAreaCountryId());
            Long countryId = institution.getFkAreaCountryId();
            String typeName = institutionCourse.getName();
            countries.add(new CountryInfo(countryId, countryName, typeName));
        }
        if (GeneralTool.isEmpty(countries)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_valid_country_context"));
        }

        //公共参数处理
        // 邮件标题
        String title = news.getTitle();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss");
        Date createDate = GeneralTool.isNotEmpty(sendNewsMailContext.getCreateDate()) ? sendNewsMailContext.getCreateDate() : new Date();
        String createDateStr = simpleDateFormat.format(createDate);


        //处理附件
        StringBuilder mediaLinks = new StringBuilder();
        List<MediaAndAttached> mediaAndAttachedList = sendNewsMailContext.getMediaAndAttacheds();
        if (GeneralTool.isNotEmpty(mediaAndAttachedList)) {
            List<String> guids = mediaAndAttachedList.stream()
                    .map(MediaAndAttached::getFkFileGuid)
                    .collect(Collectors.toList());
            Result<List<FileDto>> fileResponse = fileCenterClient.getFile(guids, LoggerModulesConsts.INSTITUTIONCENTER);
            if (fileResponse == null || !fileResponse.isSuccess() || fileResponse.getData() == null) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("file_not_found"));
            } else {
                Map<String, FileDto> fileMap = fileResponse.getData().stream()
                        .collect(Collectors.toMap(FileDto::getFileGuid, Function.identity()));
                String ossPrefix = determineOssPrefix();
                mediaLinks.append("<h4>附件链接列表</h4><ol>");
                for (MediaAndAttached media : mediaAndAttachedList) {
                    FileDto file = fileMap.get(media.getFkFileGuid());
                    if (file != null && GeneralTool.isNotEmpty(file.getFileKey())) {
                        mediaLinks.append("<li><a class=\"link\" href=\"")
                                .append(ossPrefix).append(file.getFileKey())
                                .append("\">")
                                .append(GeneralTool.isEmpty(file.getFileNameOrc()) ? "未命名文件" : file.getFileNameOrc())
                                .append("</a></li>");
                    }
                }
                mediaLinks.append("</ol>");
            }

        }
        // 获取部门配置
        Result<Map<Long, String>> companyConfigMap = permissionCenterClient.
                getCompanyConfigMap(ProjectKeyEnum.NEWS_EMAIL_DP.key, 3);
        if (companyConfigMap == null || !companyConfigMap.isSuccess() || GeneralTool.isEmpty(companyConfigMap.getData())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("department_config_not_found"));
        }
        String configValue3 = companyConfigMap.getData().get(SecureUtil.getFkCompanyId());
        if (GeneralTool.isEmpty(configValue3)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("department_config_not_found"));
        }
        Set<String> departmentNums = new HashSet<>(JSON.parseArray(configValue3, String.class));
        if (GeneralTool.isEmpty(departmentNums)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("department_nums_not_found"));
        }
        // 获取邮件模板
        Result<RemindTemplate> templateResponse = reminderCenterClient
                .getRemindTemplateByTypeKey(ProjectKeyEnum.NEWS_EMAIL_NOTICE.key);
        if (templateResponse == null || !templateResponse.isSuccess() || templateResponse.getData() == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("news_emil_template_not_found"));
        }

        String emailTemplate = templateResponse.getData().getEmailTemplate();
        if (GeneralTool.isEmpty(emailTemplate)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("news_emil_template_not_found"));
        }

        List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();

        // 遍历国家发送邮件
        for (CountryInfo country : countries) {
            try {
                // 构造邮件主题
//                String mailSubject = MessageFormat.format("[{0}][新增新闻信息：{1}]通知",
//                        country.countryName, title);
                // 对主题进行MIME编码（不影响URL其他参数）
                String rawSubject = MessageFormat.format("[{0}][新增新闻信息：{1}]通知",
                        country.countryName, title);
                String mailSubject;
                try {
                    mailSubject = "=?UTF-8?B?" + Base64.getEncoder()
                            .encodeToString(rawSubject.getBytes(StandardCharsets.UTF_8)) + "?=";
                } catch (Exception e) {
                    mailSubject = rawSubject;
                    log.error("主题编码失败", e);
                }

                // 获取收件人列表
                Result<List<StaffVo>> staffResponse = permissionCenterClient
                        .getStaffDtosByDepartmentNums(country.countryId, departmentNums);
                if (staffResponse == null || !staffResponse.isSuccess()) {
                    log.error("Failed to get staff list for country {}: {}", country.countryName,
                            (staffResponse != null) ? staffResponse.getMessage() : "null response");
                    continue;
                }

                List<StaffVo> staffList = staffResponse.getData();
                if (GeneralTool.isEmpty(staffList)) {
                    log.warn("No staff found for country: {}", country.countryName);
                    continue;
                }

                // 过滤有效邮箱
                List<String> validEmails = staffList.stream()
                        .filter(staff -> staff != null)
                        .filter(staff -> staff.getIsOnDuty() != null && staff.getIsOnDuty())
                        .map(StaffVo::getEmail)
                        .filter(email -> GeneralTool.isNotEmpty(email) && email.contains("@"))
                        .distinct()
                        .collect(Collectors.toList());

                if (GeneralTool.isEmpty(validEmails)) {
                    log.warn("No valid emails for country: {}", country.countryName);
                    continue;
                }

                //String mailToAddress = String.join(";", validEmails);

                // 替换模板参数
                Map<String, String> templateParams = new LinkedHashMap<>();
                templateParams.put("createTime", createDateStr);
                templateParams.put("typeName", country.typeName);
                templateParams.put("countryName", country.countryName);
                templateParams.put("newsTitle", title);
                templateParams.put("newsContent", news.getDescription());
                templateParams.put("mediaAndAttachedLinks", mediaLinks.length() > 0 ? mediaLinks.toString() : "无附件");

                String mailBody = doCreateMailBody(emailTemplate, templateParams);
                if (GeneralTool.isEmpty(mailBody)) {
                    log.warn("Generated mail body is empty for country: {}", country.countryName);
                    continue;
                }

                EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                emailSenderQueue.setEmailTitle(mailSubject);
                emailSenderQueue.setEmailTo(validEmails.toString());
                emailSenderQueue.setEmailParameter(JSON.toJSONString(templateParams));
                emailSenderQueue.setOperationTime(now());
                emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.NEWS_EMAIL_NOTICE.getEmailTemplateKey());
                emailSenderQueue.setFkTableName(TableEnum.NEWS.key);
                emailSenderQueue.setFkTableId(news.getId());
                emailSenderQueue.setFkDbName(ProjectKeyEnum.INSTITUTION_CENTER.key);
                emailSenderQueueList.add(emailSenderQueue);
                // 生成签名
//                long timestamp = System.currentTimeMillis() / 1000;
//                String plainText = timestamp + "&" + key;
//                String sign = DigestUtils.md5Hex(plainText);
//
//                String apiUrl = MessageFormat.format(apiUrlTemplate,
//                        mailFromAddress, mailToAddress, rawSubject, mailBody,
//                        String.valueOf(timestamp), sign);
//                String url = apiUrl.substring(0, 52);
//                Map<String, Object> paramMap = new HashMap<>();
//                paramMap.put("mailFromAddress", mailFromAddress);
//                paramMap.put("mailToAddress", mailToAddress);
//                paramMap.put("mailSubject", mailSubject);
//                paramMap.put("mailbody", mailBody);
//                paramMap.put("time", String.valueOf(timestamp));
//                paramMap.put("sign", sign);
//
//
//
//                // 发送HTTP请求
//                String response = HttpUtil.post(url,paramMap);
//                if (GeneralTool.isEmpty(response)) {
//                   throw new GetServiceException(LocaleMessageUtils.getMessage("news_email_send_fail"));
//                }
//
//                JSONObject result = JSON.parseObject(response);
//                if (result == null || !result.getBooleanValue("success")) {
//                    log.error("邮件发送失败 | 国家: {} | 响应: {}", country.countryName, response);
//                    throw new GetServiceException(LocaleMessageUtils.getMessage("news_email_send_fail"));
//                }
//
                log.info("邮件发送成功 | 国家: {} | 收件人数: {}", country.countryName, validEmails.size());

            } catch (Exception e) {
                log.warn("国家[{}]邮件发送异常: {}", country.countryName, e.getMessage(), e);
            }
        }
        Result<Boolean> bolleanResult =reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
        if (!bolleanResult.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(bolleanResult.getMessage()));
        }
    }

    // 环境相关的OSS前缀判断
    private String determineOssPrefix() {
        String profile = System.getProperty("spring.profiles.active");
        if (profile == null) return OSS_IMAGES_DEV_URL;
        switch (profile) {
            case AppConstant.PROD_CODE:
            case AppConstant.GRAY_CODE:
            case AppConstant.TW_CODE:
            case AppConstant.IAE_CODE:
                return OSS_IMAGES_PRD_URL;
            case AppConstant.TEST_CODE:
                return OSS_IMAGES_TEST_URL;
            default:
                return OSS_IMAGES_DEV_URL;
        }
    }

    // 邮件模板生成方法
    private String doCreateMailBody(String template, Map<String, String> params) {
        Set<String> keys = params.keySet();
        for (String key : keys) {
            template = template.replace("#{" + key + "}", params.get(key));
        }
        return template;
    }


    /**
     * 获取新闻实体
     *
     * @param newEmailToAgentDto
     * @return
     */
    private SendNewAgentContextVo getEmailNew(NewEmailToAgentDto newEmailToAgentDto) {
        SendNewAgentContextVo sendNewAgentContextVo = new SendNewAgentContextVo();
        if (GeneralTool.isEmpty(newEmailToAgentDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        News news = getById(newEmailToAgentDto.getId());
        if (GeneralTool.isEmpty(news)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        StaffVo staffVo = permissionCenterClient.getStaffByLoginId(news.getGmtCreateUser());
        if (GeneralTool.isNotEmpty(staffVo)) {
            if (GeneralTool.isNotEmpty(staffVo.getEmail())) {
                sendNewAgentContextVo.setCreateStaffEmail(staffVo.getEmail());
            }
        }
        sendNewAgentContextVo.setTitle(news.getTitle());
        sendNewAgentContextVo.setDescription(news.getDescription());
        sendNewAgentContextVo.setId(newEmailToAgentDto.getId());
        sendNewAgentContextVo.setStepIds(newEmailToAgentDto.getStepIds());
        sendNewAgentContextVo.setIntakeTimeStart(newEmailToAgentDto.getIntakeTimeStart());
        sendNewAgentContextVo.setIntakeTimeEnd(newEmailToAgentDto.getIntakeTimeEnd());
        RNewsType rNewsType = rNewsTypeMapper.selectOne(Wrappers.<RNewsType>lambdaQuery().eq(RNewsType::getFkNewsId, news.getId()));
        if (!TableEnum.INSTITUTION_COUNTRY.key.equals(rNewsType.getFkTableName())
                && !TableEnum.INSTITUTION.key.equals(rNewsType.getFkTableName())
        ) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("non_target_type"));
        }
        if (rNewsType.getFkTableName().equals(TableEnum.INSTITUTION_COUNTRY.key)) {
            sendNewAgentContextVo.setFkCountryId(rNewsType.getFkTableId());
        } else if (rNewsType.getFkTableName().equals(TableEnum.INSTITUTION.key)) {
            sendNewAgentContextVo.setFkInstitutionId(rNewsType.getFkTableId());
        }
        //获取附件
        //邮件附件类型才列出来 -> 改为定时任务后不用获取模板 和 link
//        String link = getfileLink(news);
//        if (GeneralTool.isNotEmpty(link)){
//            sendNewAgentContextDto.setDescription(sendNewAgentContextDto.getDescription()+link);
//        }
        //邮件模板 -> 改为定时任务后不用获取模板 和 link
//        RemindTemplate remindTemplate = reminderCenterClient.getRemindTemplateByTypeKey(ProjectKeyEnum.AGENT_NEWS_EMAIL.key).getData();
//        sendNewAgentContextDto.setDescription(remindTemplate.getEmailTemplate().replace("#{news}",sendNewAgentContextDto.getDescription()).replace("#{display}", "none"));
        return sendNewAgentContextVo;
    }

    /**
     * 获取附件
     *
     * @param news
     * @return
     */
    private String getfileLink(News news) {
        StringBuilder sb = new StringBuilder();
        List<MediaAndAttached> mediaAndAttacheds = attachedService.list(Wrappers.lambdaQuery(MediaAndAttached.class)
                .eq(MediaAndAttached::getFkTableName, TableEnum.NEWS.key)
                .eq(MediaAndAttached::getFkTableId, news.getId())
                .eq(MediaAndAttached::getTypeKey, FileTypeEnum.INSTITUTION_NEWS_MAIL_APPENDIX.key));

        String mediaAndAttachedLinkPrefix = "";
        Properties props = System.getProperties();
        String profile = props.getProperty("spring.profiles.active");
        if (GeneralTool.isNotEmpty(profile)) {
            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.GRAY_CODE) || profile.equals(AppConstant.TW_CODE)) {
                mediaAndAttachedLinkPrefix = OSS_IMAGES_PRD_URL;
            } else if (profile.equals(AppConstant.IAE_CODE)) {
                mediaAndAttachedLinkPrefix = OSS_IMAGES_PRD_URL;
            } else if (profile.equals(AppConstant.TEST_CODE)) {
                mediaAndAttachedLinkPrefix = OSS_IMAGES_TEST_URL;
            } else {
                mediaAndAttachedLinkPrefix = OSS_IMAGES_DEV_URL;
            }
        }

        List<String> guids = mediaAndAttacheds.stream().map(MediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        List<FileDto> fileDtos = fileCenterClient.getFile(guids, LoggerModulesConsts.INSTITUTIONCENTER).getData();
        Map<String, FileDto> fileDtoMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(fileDtos)) {
            fileDtoMap = fileDtos.stream().collect(HashMap::new, (m, v) -> m.put(v.getFileGuid(), v), HashMap::putAll);
        }
        if (GeneralTool.isNotEmpty(fileDtoMap)) {
            sb.append("<h4>附件链接列表</h4><ol>");
            for (MediaAndAttached mediaAndAttached : mediaAndAttacheds) {
                FileDto fileDto = fileDtoMap.get(mediaAndAttached.getFkFileGuid());
                if (GeneralTool.isNotEmpty(fileDto)) {
                    String fileKey = fileDto.getFileKey();
                    String fileName = fileDto.getFileNameOrc();
                    String link = mediaAndAttachedLinkPrefix + fileKey;
                    sb.append("<li><a class=\"link\" href=\"")
                            .append(link)
                            .append("\">")
                            .append(fileName)
                            .append("</a></li>");
                }
            }
            sb.append("</ol>");
        }

        return sb.toString();
    }

    /**
     * 更新发送时间
     *
     * @param sendNewsMailContext
     */
    private void doUpdateSendEmailTime(SendNewsMailContext sendNewsMailContext) {
        News news = sendNewsMailContext.getNews();
        if (GeneralTool.isEmpty(news)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Date createDate = sendNewsMailContext.getCreateDate();
        news.setSendEmailTime(GeneralTool.isNotEmpty(createDate) ? createDate : new Date());
        utilService.setUpdateInfo(news);
        boolean b = updateById(news);
        if (!b) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

    }

    /**
     * 拼接模板
     *
     * @param emailTemplate
     * @param replaceMap
     * @return
     */
//    private String doCreateMailBody(String emailTemplate, Map<String, String> replaceMap) {
//        Set<String> keySet = replaceMap.keySet();
//        for (String key : keySet) {
//            emailTemplate = emailTemplate.replace("#{" + key + "}", replaceMap.get(key));
//        }
//        return emailTemplate;
//    }


    /**
     * 获取发送邮件参数
     *
     * @param sendNewsMailContext
     */
    private void doGetSystemConfig(SendNewsMailContext sendNewsMailContext) {
        //设置参数值
        ConfigVo dpConfig = permissionCenterClient.getConfigByKey(ProjectKeyEnum.NEWS_EMAIL_DP.key).getData();

        ConfigVo IaeApiConfig = permissionCenterClient.getConfigByKey(ProjectKeyEnum.IAE_CRM_SECRETID.key).getData();
        if (GeneralTool.isEmpty(dpConfig)
                || GeneralTool.isEmpty(dpConfig.getValue3())
                || GeneralTool.isEmpty(dpConfig.getValue4())
                || GeneralTool.isEmpty(IaeApiConfig)
                || GeneralTool.isEmpty(IaeApiConfig.getValue1())
        ) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("missing_required_configuration"));
        }

        sendNewsMailContext.setDpConfig(BeanCopyUtils.objClone(dpConfig, ConfigVo::new));
        sendNewsMailContext.setIaeApiConfig(BeanCopyUtils.objClone(IaeApiConfig, ConfigVo::new));
    }

    /**
     * 获取邮件附件
     *
     * @param sendNewsMailContext
     */
    private void doGetNewsMediaAndAttached(SendNewsMailContext sendNewsMailContext) {
        News news = sendNewsMailContext.getNews();
        if (GeneralTool.isEmpty(news)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //邮件附件类型才列出来
        List<MediaAndAttached> mediaAndAttacheds = attachedService.list(Wrappers.lambdaQuery(MediaAndAttached.class)
                .eq(MediaAndAttached::getFkTableName, TableEnum.NEWS.key)
                .eq(MediaAndAttached::getFkTableId, news.getId())
                .eq(MediaAndAttached::getTypeKey, FileTypeEnum.INSTITUTION_NEWS_MAIL_APPENDIX.key));

        sendNewsMailContext.setMediaAndAttacheds(mediaAndAttacheds);

    }

    /**
     * 验证能否发送
     *
     * @param sendNewsMailContext
     */
    private void validateSend(SendNewsMailContext sendNewsMailContext) {
        News news = sendNewsMailContext.getNews();
        if (GeneralTool.isEmpty(news)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        List<RNewsType> rNewsTypes = rNewsTypeMapper.selectList(Wrappers.<RNewsType>lambdaQuery().eq(RNewsType::getFkNewsId, news.getId()));
        if (GeneralTool.isNotEmpty(rNewsTypes)) {
            for (RNewsType rNewsType : rNewsTypes) {
//                if ((!TableEnum.INSTITUTION_COUNTRY.key.equals(news.getFkTableName())
//                        &&!TableEnum.INSTITUTION_PROVIDER.key.equals(news.getFkTableName())
//                        &&!TableEnum.INSTITUTION.key.equals(news.getFkTableName())
//                        &&!TableEnum.INSTITUTION_COURSE.key.equals(news.getFkTableName())
//                )&&(!TableEnum.INSTITUTION_COUNTRY.key.equals(rNewsType.getFkTableName())
//                        &&!TableEnum.INSTITUTION_PROVIDER.key.equals(rNewsType.getFkTableName())
//                        &&!TableEnum.INSTITUTION.key.equals(rNewsType.getFkTableName())
//                        &&!TableEnum.INSTITUTION_COURSE.key.equals(rNewsType.getFkTableName()))
//                ){
                if ((!TableEnum.INSTITUTION_COUNTRY.key.equals(rNewsType.getFkTableName())
                        && !TableEnum.INSTITUTION_PROVIDER.key.equals(rNewsType.getFkTableName())
                        && !TableEnum.INSTITUTION.key.equals(rNewsType.getFkTableName())
                        && !TableEnum.INSTITUTION_COURSE.key.equals(rNewsType.getFkTableName()))
                ) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("news_type_not_news_type_not_support_email"));
                } else {
                    return;
                }
            }
        }


    }

    /**
     * 获取新闻实体
     *
     * @param id
     * @return
     */
    private SendNewsMailContext doGetNews(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        News news = getById(id);
        if (GeneralTool.isEmpty(news)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        SendNewsMailContext sendNewsMailContext = new SendNewsMailContext();
        sendNewsMailContext.setNews(news);
        return sendNewsMailContext;
    }


    /**
     * 给新闻设置邮件标签（ 统计发送数据用 ）
     *
     * @param newsEmailTagDto
     * @return
     */
    @Override
    @Transactional
    public Long addNewsEmailTage(NewsEmailTagDto newsEmailTagDto) {

        //判断该新闻是否有标签
        //如果tagId 和 tagName 都不为空，直接返回tagId。（已经有存在的tag）

//        //调用阿里云api添加标签并且拿到该标签的id
//        Long tagId = reminderCenterClient.aliyunAddTag(newsEmailTagDto.getTagName()).getData();
//        if (GeneralTool.isEmpty(tagId)) //如果标签名重复 或者异常
//            throw new  GetServiceException(LocaleMessageUtils.getMessage("TAGNAME_MAYBE_BE_DUPLICATE"));//这里还没有配这个语言键（配置后删除该注释）
//        //（被删除）  如果有标签而且tagId为null（被定时任务清除）要重置tagId
//
//        // （不存在） 数据库保存该新闻的tagName和tagId
        return null;
    }

    /**
     * @param id   新闻id
     * @param type 发送类型 0不区分/1hubs/2市场
     * @return 获取新闻模板
     */
    @Override
    public AliyunSendMailDto getNewEmailTmpelte(Long id, Integer type) {
        return getEmailNewAllAgent(id, type);
    }
}
