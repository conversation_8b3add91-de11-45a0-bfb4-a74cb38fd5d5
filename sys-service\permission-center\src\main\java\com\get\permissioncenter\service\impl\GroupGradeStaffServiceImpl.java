package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.PermissionGradeMapper;
import com.get.permissioncenter.dao.PermissionGroupGradeNameMapper;
import com.get.permissioncenter.dao.PermissionGroupGradeStaffMapper;
import com.get.permissioncenter.dao.PermissionGroupMapper;
import com.get.permissioncenter.vo.GroupGradeStaffVo;
import com.get.permissioncenter.vo.GroupGradeStaffForMovingOrCopyContext;
import com.get.permissioncenter.vo.PermissionGroupGradeStaffVo;
import com.get.permissioncenter.entity.PermissionGrade;
import com.get.permissioncenter.entity.PermissionGroup;
import com.get.permissioncenter.entity.PermissionGroupGradeStaff;
import com.get.permissioncenter.service.IGroupGradeResourceService;
import com.get.permissioncenter.service.IGroupGradeStaffService;
import com.get.permissioncenter.dto.PermissionGroupGradeForMovingAndCopyingDto;
import com.get.permissioncenter.dto.PermissionGroupGradeStaffDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: jack
 * @create: 2020/7/13
 * @verison: 1.0
 * @description: 权限人员配置业务实现类
 */
@Service
public class GroupGradeStaffServiceImpl extends BaseServiceImpl<PermissionGroupGradeStaffMapper, PermissionGroupGradeStaff> implements IGroupGradeStaffService {
    @Resource
    private PermissionGradeMapper permissionGradeMapper;
    @Resource
    private PermissionGroupMapper permissionGroupMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private PermissionGroupGradeStaffMapper permissionGroupGradeStaffMapper;
    @Resource
    private IGroupGradeResourceService groupGradeResourceService;
    @Resource
    private PermissionGroupGradeNameMapper permissionGroupGradeNameMapper;

    @Override
    public PermissionGroupGradeStaffVo getGroupGradeStaff(Long companyId) {
        if (GeneralTool.isEmpty(companyId)) {
            companyId = SecureUtil.getFkCompanyId();
        }
        PermissionGroupGradeStaffVo permissionGroupGradeStaffVo = new PermissionGroupGradeStaffVo();
        List<GroupGradeStaffVo> staffnums = new ArrayList<GroupGradeStaffVo>();
//        Example examplegrade = new Example(PermissionGrade.class);
//        Example.Criteria criteria = examplegrade.createCriteria();
//        Example examplegroup = new Example(PermissionGroup.class);
//        Example.Criteria criteria1 = examplegroup.createCriteria();
//        criteria.andEqualTo("fkCompanyId", companyId);
//        examplegrade.orderBy("viewOrder").desc();
//        criteria1.andEqualTo("fkCompanyId", companyId);
//        examplegroup.orderBy("viewOrder").desc();
//        List<PermissionGrade> permissionGrades = permissionGradeMapper.selectByExample(examplegrade);
//        List<PermissionGroup> permissionGroups = permissionGroupMapper.selectByExample(examplegroup);

        List<PermissionGrade> permissionGrades = this.permissionGradeMapper.selectList(Wrappers.<PermissionGrade>query().lambda().eq(PermissionGrade::getFkCompanyId, companyId).orderByDesc(PermissionGrade::getViewOrder));
        List<PermissionGroup> permissionGroups = this.permissionGroupMapper.selectList(Wrappers.<PermissionGroup>query().lambda().eq(PermissionGroup::getFkCompanyId, companyId).orderByDesc(PermissionGroup::getViewOrder));


        if (GeneralTool.isNotEmpty(permissionGrades) && GeneralTool.isNotEmpty(permissionGroups)) {
            for (PermissionGroup permissionGroup : permissionGroups) {
                for (PermissionGrade permissionGrade : permissionGrades) {
                    Integer staffnum = permissionGroupGradeStaffMapper.CountByGroupAndGrade(permissionGroup.getId(), permissionGrade.getId());
                    String name = permissionGroupGradeNameMapper.selectNameByGroupAndGrade(permissionGroup.getId(), permissionGrade.getId());
                    GroupGradeStaffVo dto = new GroupGradeStaffVo();
                    dto.setGroupGradeStaffCount(staffnum);
                    dto.setGroupGradeStaffName(name);
                    staffnums.add(dto);
                }
            }
        }
        permissionGroupGradeStaffVo.setGroupGradeStaffDto(staffnums);
        permissionGroupGradeStaffVo.setPermissionGrades(permissionGrades);
        permissionGroupGradeStaffVo.setPermissionGroups(permissionGroups);
        return permissionGroupGradeStaffVo;
    }

    @Override
    public List<Long> getGroupGradeStaffs(PermissionGroupGradeStaffDto permissionGroupGradeStaffDto) {
        if (GeneralTool.isEmpty(permissionGroupGradeStaffDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(permissionGroupGradeStaffDto.getFkPermissionGradeId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("grade_null"));
        }
        if (GeneralTool.isEmpty(permissionGroupGradeStaffDto.getFkPermissionGroupId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("group_null"));
        }
        List<Long> ids = permissionGroupGradeStaffMapper.selectStaffByGroupAndGrade(permissionGroupGradeStaffDto.getFkPermissionGroupId(), permissionGroupGradeStaffDto.getFkPermissionGradeId());
        return ids;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateGroupGradeStaffs(PermissionGroupGradeStaffDto permissionGroupGradeStaffDto) {
        if (GeneralTool.isEmpty(permissionGroupGradeStaffDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(permissionGroupGradeStaffDto.getFkPermissionGradeId()) || GeneralTool.isEmpty(permissionGroupGradeStaffDto.getFkPermissionGroupId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        permissionGroupGradeStaffMapper.deleteByGroupAndGrade(permissionGroupGradeStaffDto.getFkPermissionGroupId(), permissionGroupGradeStaffDto.getFkPermissionGradeId());
        if (GeneralTool.isNotEmpty(permissionGroupGradeStaffDto.getStaffIds())) {
            for (Long staffId : permissionGroupGradeStaffDto.getStaffIds()) {
                PermissionGroupGradeStaff permissionGroupGradeStaff = new PermissionGroupGradeStaff();
                permissionGroupGradeStaff.setFkPermissionGroupId(permissionGroupGradeStaffDto.getFkPermissionGroupId());
                permissionGroupGradeStaff.setFkPermissionGradeId(permissionGroupGradeStaffDto.getFkPermissionGradeId());
                permissionGroupGradeStaff.setFkStaffId(staffId);
//                utilService.setCreateInfo(permissionGroupGradeStaff);
                utilService.updateUserInfoToEntity(permissionGroupGradeStaff);
                permissionGroupGradeStaffMapper.insert(permissionGroupGradeStaff);
            }
        }
        groupGradeResourceService.updateStaffSession(permissionGroupGradeStaffDto.getStaffIds());
    }

    @Override
    public List<PermissionGroupGradeStaffVo> getPermissionGroupGradeStaffDtosByStaffId(Long id) {
//        Example example = new Example(PermissionGroupGradeStaff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStaffId", id);
//        List<PermissionGroupGradeStaffVo> permissionGroupGradeStaffVos = new ArrayList<>();
//        List<PermissionGroupGradeStaff> permissionGroupGradeStaffs = permissionGroupGradeStaffMapper.selectByExample(example);

        List<PermissionGroupGradeStaffVo> permissionGroupGradeStaffVos = new ArrayList<>();
        List<PermissionGroupGradeStaff> permissionGroupGradeStaffs = permissionGroupGradeStaffMapper.selectList(Wrappers.<PermissionGroupGradeStaff>query().lambda().eq(PermissionGroupGradeStaff::getFkStaffId, id));
        for (PermissionGroupGradeStaff permissionGroupGradeStaff : permissionGroupGradeStaffs) {
            PermissionGroupGradeStaffVo permissionGroupGradeStaffVo = new PermissionGroupGradeStaffVo();
            if (GeneralTool.isNotEmpty(permissionGroupGradeStaff.getFkPermissionGradeId())) {
//                PermissionGrade permissionGrade = permissionGradeMapper.selectByPrimaryKey(permissionGroupGradeStaff.getFkPermissionGradeId());
                PermissionGrade permissionGrade = permissionGradeMapper.selectById(permissionGroupGradeStaff.getFkPermissionGradeId());
                if (GeneralTool.isNotEmpty(permissionGrade)) {
                    permissionGroupGradeStaffVo.setPermissionGradeName(permissionGrade.getGradeName());
                }
            }
            if (GeneralTool.isNotEmpty(permissionGroupGradeStaff.getFkPermissionGroupId())) {
//                PermissionGroup permissionGroup = permissionGroupMapper.selectByPrimaryKey(permissionGroupGradeStaff.getFkPermissionGroupId());
                PermissionGroup permissionGroup = permissionGroupMapper.selectById(permissionGroupGradeStaff.getFkPermissionGroupId());
                if (GeneralTool.isNotEmpty(permissionGroup)) {
                    permissionGroupGradeStaffVo.setPermissionGroupName(permissionGroup.getGroupName());
                }
            }
            String name = permissionGroupGradeNameMapper.selectNameByGroupAndGrade(permissionGroupGradeStaff.getFkPermissionGroupId(), permissionGroupGradeStaff.getFkPermissionGradeId());
            permissionGroupGradeStaffVo.setPermissionName(name);
            permissionGroupGradeStaffVos.add(permissionGroupGradeStaffVo);
        }
        return permissionGroupGradeStaffVos;
    }

    /**
     * 移动网点人员
     *
     * @param permissionGroupGradeForMovingAndCopyingDto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void movePermissionGroupGradeStaff(PermissionGroupGradeForMovingAndCopyingDto permissionGroupGradeForMovingAndCopyingDto) {
        //验证原网点和目标网点资源是否为空
        GroupGradeStaffForMovingOrCopyContext groupGradeStaffForMovingOrCopyContext = doValidatePermissionGroupGradeStaff(permissionGroupGradeForMovingAndCopyingDto);

        //移动网点信息
        doMovePermissionGroupGradeStaff(groupGradeStaffForMovingOrCopyContext);

    }

    private void doMovePermissionGroupGradeStaff(GroupGradeStaffForMovingOrCopyContext groupGradeStaffForMovingOrCopyContext) {
        List<PermissionGroupGradeStaff> sourcePermissionGroupGradeStaffs = groupGradeStaffForMovingOrCopyContext.getSourcePermissionGroupGradeStaffs();
        Long targetGroupId = groupGradeStaffForMovingOrCopyContext.getTargetGroupId();
        Long targetGradeId = groupGradeStaffForMovingOrCopyContext.getTargetGradeId();
        int updateCount = sourcePermissionGroupGradeStaffs.size();
        for (PermissionGroupGradeStaff sourcePermissionGroupGradeStaff : sourcePermissionGroupGradeStaffs) {
            sourcePermissionGroupGradeStaff.setFkPermissionGroupId(targetGroupId);
            sourcePermissionGroupGradeStaff.setFkPermissionGradeId(targetGradeId);
            utilService.setUpdateInfo(sourcePermissionGroupGradeStaff);
        }

        while (updateCount>0){
            //批量更新 每次更新DEFAULT_BATCH_SIZE = 1000
            boolean b = updateBatchById(sourcePermissionGroupGradeStaffs, DEFAULT_BATCH_SIZE);
            if (!b){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
            updateCount = updateCount - DEFAULT_BATCH_SIZE;
        }
    }


    /**
     * 复制网点资源人员
     *
     * @param permissionGroupGradeForMovingAndCopyingDto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void copyPermissionGroupGradeStaff(PermissionGroupGradeForMovingAndCopyingDto permissionGroupGradeForMovingAndCopyingDto) {
        //验证原网点和目标网点资源是否为空
        GroupGradeStaffForMovingOrCopyContext groupGradeStaffForMovingOrCopyContext = doValidatePermissionGroupGradeStaff(permissionGroupGradeForMovingAndCopyingDto);

        //复制网点信息
        doCopyPermissionGroupGradeStaff(groupGradeStaffForMovingOrCopyContext);

    }

    private void doCopyPermissionGroupGradeStaff(GroupGradeStaffForMovingOrCopyContext groupGradeStaffForMovingOrCopyContext) {
        List<PermissionGroupGradeStaff> sourcePermissionGroupGradeStaffs = groupGradeStaffForMovingOrCopyContext.getSourcePermissionGroupGradeStaffs();
        Long targetGroupId = groupGradeStaffForMovingOrCopyContext.getTargetGroupId();
        Long targetGradeId = groupGradeStaffForMovingOrCopyContext.getTargetGradeId();
        int insertCount = sourcePermissionGroupGradeStaffs.size();
        for (PermissionGroupGradeStaff sourcePermissionGroupGradeStaff : sourcePermissionGroupGradeStaffs) {
            sourcePermissionGroupGradeStaff.setId(null);
            sourcePermissionGroupGradeStaff.setFkPermissionGroupId(targetGroupId);
            sourcePermissionGroupGradeStaff.setFkPermissionGradeId(targetGradeId);
            sourcePermissionGroupGradeStaff.setGmtModified(null);
            sourcePermissionGroupGradeStaff.setGmtModifiedUser(null);
            utilService.setCreateInfo(sourcePermissionGroupGradeStaff);
        }

        while (insertCount>0){
            //批量插入 每次插入DEFAULT_BATCH_SIZE = 1000
            boolean b = saveBatch(sourcePermissionGroupGradeStaffs, DEFAULT_BATCH_SIZE);
            if (!b){
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
            insertCount = insertCount - DEFAULT_BATCH_SIZE;
        }
    }

    private GroupGradeStaffForMovingOrCopyContext doValidatePermissionGroupGradeStaff(PermissionGroupGradeForMovingAndCopyingDto permissionGroupGradeForMovingAndCopyingDto) {
        //原网点
        List<PermissionGroupGradeStaff> sourcePermissionGroupGradeStaffs = permissionGroupGradeStaffMapper.selectList(Wrappers.<PermissionGroupGradeStaff>lambdaQuery()
                .eq(PermissionGroupGradeStaff::getFkPermissionGroupId, permissionGroupGradeForMovingAndCopyingDto.getSourceGroupId())
                .eq(PermissionGroupGradeStaff::getFkPermissionGradeId, permissionGroupGradeForMovingAndCopyingDto.getSourceGradeId()));

        if(GeneralTool.isEmpty(sourcePermissionGroupGradeStaffs)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("source_permission_group_grade_null"));

        }

        //目标网点
        List<PermissionGroupGradeStaff> targetPermissionGroupGradeStaffs = permissionGroupGradeStaffMapper.selectList(Wrappers.<PermissionGroupGradeStaff>lambdaQuery()
                .eq(PermissionGroupGradeStaff::getFkPermissionGroupId, permissionGroupGradeForMovingAndCopyingDto.getTargetGroupId())
                .eq(PermissionGroupGradeStaff::getFkPermissionGradeId, permissionGroupGradeForMovingAndCopyingDto.getTargetGradeId()).last("limit 1"));

        if(GeneralTool.isNotEmpty(targetPermissionGroupGradeStaffs)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("target_permission_group_grade_null"));
        }

        return new GroupGradeStaffForMovingOrCopyContext(permissionGroupGradeForMovingAndCopyingDto.getTargetGroupId(), permissionGroupGradeForMovingAndCopyingDto.getTargetGradeId(), sourcePermissionGroupGradeStaffs);

    }

//    private void updateStaffredis(List<Long> staffIds) {
//        for (Long staffId : staffIds) {
//            Staff staff = staffMapper.selectByPrimaryKey(staffId);
//            Long expire = getRedis.pttl(this.keySerializer.serialize("shiro:session:" + staff.getSessionId()));
//            if (expire <= 30 * 60 * 1000 && expire > 0) {
//                getRedis.pexpire(this.keySerializer.serialize("shiro:session:" + staff.getSessionId()), 0L);
//            }
//        }
//    }
}
