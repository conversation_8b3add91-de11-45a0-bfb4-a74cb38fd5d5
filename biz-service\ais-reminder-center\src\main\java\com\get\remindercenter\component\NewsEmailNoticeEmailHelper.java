package com.get.remindercenter.component;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.NewsEmailNoticeEmailDto;
import com.get.remindercenter.dto.SystemSendEmailDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

@Component("newsEmailNoticeEmailHelper")
@Slf4j
public class NewsEmailNoticeEmailHelper extends EmailAbstractHelper{

    @Resource
    private EmailTemplateMapper emailTemplateMapper;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;


    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private RemindTaskQueueService remindTaskQueueService;


    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        StringJoiner failedEmails = new StringJoiner("; "); // 新增：记录失败邮箱
        try {
            NewsEmailNoticeEmailDto newsEmailNoticeEmailDto = assembleEmailData(emailSenderQueue);

            EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
            emailSystemMQMessageDto.setEmailSenderQueueId(emailSenderQueue.getId());
            emailSystemMQMessageDto.setTitle(emailSenderQueue.getEmailTitle());

            StringJoiner emailsCombined = new StringJoiner(", ");
            List<StaffVo> staff = permissionCenterClient.getStaffByIds(newsEmailNoticeEmailDto.getStaffEmailSet());
            Map<Long, StaffVo> data = staff.stream()
                    .collect(Collectors.toMap(
                            StaffVo::getId,  // Key: StaffVo 的 ID
                            staffVo -> staffVo  // Value: StaffVo 本身
                    ));
            for (Long id : newsEmailNoticeEmailDto.getStaffEmailSet()){
                try {
                    String staffName = data.get(id).getFullName();
                    newsEmailNoticeEmailDto.getMap().put("staffName", staffName);
                    String template = setEmailTemplate(newsEmailNoticeEmailDto);
                    emailSystemMQMessageDto.setEmailSenderQueueId(emailSenderQueue.getId());
                    emailSystemMQMessageDto.setTitle(newsEmailNoticeEmailDto.getEmailTitle());
                    emailSystemMQMessageDto.setContent(template);
                    emailSystemMQMessageDto.setToEmail(data.get(id).getEmail());
                    //emailSystemMQMessageDto.setToEmail("<EMAIL>");
                    remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                    if (GeneralTool.isNotEmpty(data.get(id).getEmail())) {
                        emailsCombined.add(data.get(id).getEmail());
                    }
                } catch (Exception e) {
                    // 记录发送失败的邮箱
                    String failedEmail = data.get(id) != null &&data.get(id).getEmail() != null ? data.get(id).getEmail() : "staffId:" + id;
                    failedEmails.add(failedEmail + "(" + (e.getMessage() != null ? e.getMessage() : "发送失败") + ")");
                    log.error("邮件发送失败 - staffId: {}, email: {}, 原因: {}",  id, failedEmail, e.getMessage());
                }
            }
            LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                    .set(EmailSenderQueue::getEmailTo, emailsCombined.toString());  // 只更新 emailTo 字段
            // 如果 failedEmails 不为空，则额外更新 errorMessage
            if (failedEmails.length() > 0) {
                updateWrapper.set(EmailSenderQueue::getErrorMessage, "失败邮箱: " + failedEmails.toString());
            }
            emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
        }catch (Exception e){
            log.error("newsEmailNoticeEmailDto error:{}", e);
            emailSenderQueue.setErrorMessage(e.getMessage());
            emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
    }

    @Override
    public NewsEmailNoticeEmailDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        return null;
    }


    private String setEmailTemplate(NewsEmailNoticeEmailDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();

        if (GeneralTool.isNotEmpty(reminderDto.getFkEmailTypeKey())) {
            remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, reminderDto.getFkEmailTypeKey()));
        }

        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
        //把emailTemplate插入到父模板里
        String parentEmailTemplate = null;
        if(GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId())&&remindTemplates.get(0).getFkParentEmailTemplateId()!=0){
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
            Map map = new HashMap();
            map.put("subtemplate",emailTemplate);
            if (reminderDto.getLanguageCode().equals("en")) {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            }else {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }

        }
        return parentEmailTemplate;
    }
}
