package com.get.remindercenter.component;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.start.constant.AppConstant;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.institutioncenter.entity.MediaAndAttached;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.NewsEmailNoticeEmailDto;
import com.get.remindercenter.dto.StudyPlanSameSchoolCourseReminderDto;
import com.get.remindercenter.dto.SystemSendEmailDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component("newsEmailNoticeEmailHelper")
@Slf4j
public class NewsEmailNoticeEmailHelper extends EmailAbstractHelper{

    @Resource
    private EmailTemplateMapper emailTemplateMapper;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;


    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private RemindTaskQueueService remindTaskQueueService;


    @Resource
    private IFileCenterClient fileCenterClient;


    //公开用户访问桶网址
    public final static String OSS_IMAGES_DEV_URL = "https://hti-ais-images-dev-1301376564.cos.ap-shanghai.myqcloud.com/";

    public final static String OSS_IMAGES_PRD_URL = "https://hti-public-image-prd-1301376564.cos.ap-shanghai.myqcloud.com";

    public final static String OSS_IMAGES_TEST_URL = "https://hti-ais-images-dev-1301376564.cos.ap-shanghai.myqcloud.com/";


    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        try {
            //组装数据
            NewsEmailNoticeEmailDto newsEmailNoticeEmailDto = assembleEmailData(emailSenderQueue);
            if (GeneralTool.isNotEmpty(newsEmailNoticeEmailDto.getStaffEmail())) {
                //设置邮件模板
                String template = setEmailTemplate(newsEmailNoticeEmailDto);
                EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
                emailSystemMQMessageDto.setEmailSenderQueueId(newsEmailNoticeEmailDto.getId());
                emailSystemMQMessageDto.setTitle(newsEmailNoticeEmailDto.getEmailTitle());
                emailSystemMQMessageDto.setContent(template);
                emailSystemMQMessageDto.setToEmail(newsEmailNoticeEmailDto.getStaffEmail());
                //rocKetMqCenterClient.getSystemSendEmail(emailSystemMQMessageDto);
                remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                emailSenderQueue.setEmailTo(newsEmailNoticeEmailDto.getStaffEmail());
                LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                        .set(EmailSenderQueue::getEmailTo,newsEmailNoticeEmailDto.getStaffEmail());  // 只更新 emailTo 字段
                emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
            }
        }catch (Exception e){
            log.error("newsEmailNoticeEmailHelper error:{}", e);
            emailSenderQueue.setErrorMessage(e.getMessage());
            emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }

    }

    @Override
    public NewsEmailNoticeEmailDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        NewsEmailNoticeEmailDto reminderDto = new NewsEmailNoticeEmailDto();
        BeanUtils.copyProperties(emailSenderQueue, reminderDto);

        //查询收件人
        Long staffId = Long.valueOf(emailSenderQueue.getEmailTo());
        StaffVo staffVo = permissionCenterClient.getStaffById(staffId).getData();

        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(staffVo.getFkCompanyId());
        if(GeneralTool.isEmpty(versionValue2)){
            versionValue2 = "zh";
        }
        String emailParameter = emailSenderQueue.getEmailParameter();
        Map<String, String> parsedMap = new HashMap<>();
        if(GeneralTool.isNotEmpty(emailSenderQueue.getEmailParameter())) {
            ObjectMapper mapper = new ObjectMapper();
            try {
                parsedMap = mapper.readValue(emailParameter, Map.class);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }

            String mediaAndAttachedListStr = parsedMap.get("mediaAndAttachedList");
            //处理附件
            StringBuilder mediaLinks = new StringBuilder();
            if (GeneralTool.isNotEmpty(mediaAndAttachedListStr)) {
                try {
                    List<MediaAndAttached> mediaAndAttachedList = JSON.parseArray(mediaAndAttachedListStr, MediaAndAttached.class);
                    List<String> guids = mediaAndAttachedList.stream()
                            .map(MediaAndAttached::getFkFileGuid)
                            .collect(Collectors.toList());
                    Result<List<FileDto>> fileResponse = fileCenterClient.getFile(guids, LoggerModulesConsts.INSTITUTIONCENTER);
                    if (fileResponse == null || !fileResponse.isSuccess() || fileResponse.getData() == null) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("file_not_found"));
                    } else {

                        Map<String, FileDto> fileMap = fileResponse.getData().stream()
                                .collect(Collectors.toMap(FileDto::getFileGuid, Function.identity()));
                        String ossPrefix = determineOssPrefix();
                        if(versionValue2.equals("zh")){
                            mediaLinks.append("<h4>附件链接列表</h4><ol>");
                        }else {
                           mediaLinks.append("<h4>Attachment link list</h4><ol>");
                        }

                        for (MediaAndAttached media : mediaAndAttachedList) {
                            FileDto file = fileMap.get(media.getFkFileGuid());
                            if (file != null && GeneralTool.isNotEmpty(file.getFileKey())) {
                                mediaLinks.append("<li><a class=\"link\" href=\"")
                                        .append(ossPrefix).append(file.getFileKey())
                                        .append("\">")
                                        .append(GeneralTool.isEmpty(file.getFileNameOrc()) ? "未命名文件" : file.getFileNameOrc())
                                        .append("</a></li>");
                            }
                        }
                        mediaLinks.append("</ol>");
                    }
                    if(versionValue2.equals("zh")){
                        parsedMap.put("mediaAndAttachedLinks", mediaLinks.length() > 0 ? mediaLinks.toString() : "无附件");
                    }else {
                        parsedMap.put("mediaAndAttachedLinks", mediaLinks.length() > 0 ? mediaLinks.toString() : "No attachment");
                    }

                } catch (Exception e) {
                    log.error("JSON转换失败: {}", mediaAndAttachedListStr, e);
                    parsedMap.put("mediaAndAttachedLinks", "");
                }

            }

        }
        reminderDto.setMap(parsedMap);
        reminderDto.setLanguageCode(versionValue2);
        reminderDto.setStaffEmail(staffVo.getEmail());

        return reminderDto;
    }


    private String setEmailTemplate(NewsEmailNoticeEmailDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();

        if (GeneralTool.isNotEmpty(reminderDto.getFkEmailTypeKey())) {
            remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, reminderDto.getFkEmailTypeKey()));
        }

        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        return emailTemplate;

    }


    private String determineOssPrefix() {
        String profile = System.getProperty("spring.profiles.active");
        if (profile == null) return OSS_IMAGES_DEV_URL;
        switch (profile) {
            case AppConstant.PROD_CODE:
            case AppConstant.GRAY_CODE:
            case AppConstant.TW_CODE:
            case AppConstant.IAE_CODE:
                return OSS_IMAGES_PRD_URL;
            case AppConstant.TEST_CODE:
                return OSS_IMAGES_TEST_URL;
            default:
                return OSS_IMAGES_DEV_URL;
        }
    }

}
