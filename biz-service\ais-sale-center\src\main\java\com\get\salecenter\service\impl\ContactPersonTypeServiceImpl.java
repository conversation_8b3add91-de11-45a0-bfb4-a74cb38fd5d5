package com.get.salecenter.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ContactPersonMapper;
import com.get.salecenter.dao.sale.ContactPersonTypeMapper;
import com.get.salecenter.dto.ContactPersonTypeDto;
import com.get.salecenter.entity.ContactPersonType;
import com.get.salecenter.enums.ContactPersonTypeEnum;
import com.get.salecenter.service.IAppAgentContactPersonService;
import com.get.salecenter.service.IContactPersonService;
import com.get.salecenter.service.IContactPersonTypeService;
import com.get.salecenter.vo.ContactPersonTypeVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 联系人类型管理实现类
 */
@Service
public class ContactPersonTypeServiceImpl extends GetServiceImpl<ContactPersonTypeMapper, ContactPersonType>
        implements IContactPersonTypeService {

    @Resource
    private ContactPersonTypeMapper contactPersonTypeMapper;

    @Resource
    private UtilService utilService;

    @Resource
    private ContactPersonMapper contactPersonMapper;

    @Resource
    private IAppAgentContactPersonService appAgentContactPersonService;

    @Resource
    private IContactPersonService contactPersonService;

    @Override
    public ContactPersonTypeVo findContactPersonTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ContactPersonType contactPersonType = contactPersonTypeMapper.selectById(id);
        return BeanCopyUtils.objClone(contactPersonType, ContactPersonTypeVo::new);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(ValidList<ContactPersonTypeDto> contactPersonTypeDtos) {
        if (GeneralTool.isEmpty(contactPersonTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        // 获取最大排序(防止每次都要去查最大值，所以在外面查出一次，循环几次 就自增几次)
        Integer maxViewOrder = contactPersonTypeMapper.getMaxViewOrder();
        for (ContactPersonTypeDto contactPersonTypeDto : contactPersonTypeDtos) {
            if (GeneralTool.isEmpty(contactPersonTypeDto.getId())) {
                if (validateAdd(contactPersonTypeDto)) {
                    ContactPersonType contactPersonType = BeanCopyUtils.objClone(contactPersonTypeDto,
                            ContactPersonType::new);
                    contactPersonType.setViewOrder(maxViewOrder);
                    utilService.updateUserInfoToEntity(contactPersonType);
                    int i = contactPersonTypeMapper.insert(contactPersonType);
                    if (i < 0) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
                    }
                    maxViewOrder++;
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("type_already_exists"));
                }
            } else {
                if (validateUpdate(contactPersonTypeDto)) {
                    ContactPersonType contactPersonType = BeanCopyUtils.objClone(contactPersonTypeDto,
                            ContactPersonType::new);
                    utilService.updateUserInfoToEntity(contactPersonType);
                    contactPersonTypeMapper.updateById(contactPersonType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("type_already_exists"));
                }
            }

        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findContactPersonTypeById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        // 校验该类型是否绑定相关联系人
        Integer count = contactPersonMapper.getContactPersonByType(id);
        if (count > 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        contactPersonTypeMapper.deleteById(id);
    }

    @Override
    public ContactPersonTypeVo updateContactPersonType(ContactPersonTypeDto contactPersonTypeDto) {
        ContactPersonType contactPersonType = BeanCopyUtils.objClone(contactPersonTypeDto, ContactPersonType::new);
        if (contactPersonTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_obj_null"));
        }
        ContactPersonType result = contactPersonTypeMapper.selectById(contactPersonTypeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_obj_null"));
        }
        if (validateUpdate(contactPersonTypeDto)) {
            utilService.updateUserInfoToEntity(contactPersonType);
            contactPersonTypeMapper.updateById(contactPersonType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_already_exists"));
        }
        return findContactPersonTypeById(contactPersonType.getId());
    }

    @Override
    public List<ContactPersonTypeVo> getContactPersonTypes(ContactPersonTypeDto contactPersonTypeDto, Page page) {
        LambdaQueryWrapper<ContactPersonType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(contactPersonTypeDto)) {
            if (GeneralTool.isNotEmpty(contactPersonTypeDto.getTypeName())) {
                lambdaQueryWrapper.like(ContactPersonType::getTypeName, contactPersonTypeDto.getTypeName());
            }
        }
        lambdaQueryWrapper.orderByDesc(ContactPersonType::getViewOrder);
        IPage<ContactPersonType> iPage = contactPersonTypeMapper.selectPage(
                GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())),
                lambdaQueryWrapper);
        List<ContactPersonType> contactPersonTypes = iPage.getRecords();
        page.setAll((int) iPage.getTotal());

        List<ContactPersonTypeVo> convertDatas = new ArrayList<>();
        for (ContactPersonType contactPersonType : contactPersonTypes) {
            ContactPersonTypeVo contactPersonTypeVo = BeanCopyUtils.objClone(contactPersonType,
                    ContactPersonTypeVo::new);
            convertDatas.add(contactPersonTypeVo);
        }
        return convertDatas;
    }

    @Override
    public List<ContactPersonTypeVo> getContactPersonTypes() {
        List<ContactPersonType> contactPersonTypes = contactPersonTypeMapper
                .selectList(Wrappers.<ContactPersonType>lambdaQuery()
                        .ne(ContactPersonType::getTypeKey, "CONTACT_AGENT_CONTRACT")
                        .orderByDesc(ContactPersonType::getViewOrder));
        return contactPersonTypes.stream()
                .map(contactPersonType -> BeanCopyUtils.objClone(contactPersonType, ContactPersonTypeVo::new))
                .collect(Collectors.toList());
    }

    @Override
    public List<ContactPersonType> getContactContactTypes() {
        return contactPersonTypeMapper.selectList(Wrappers.<ContactPersonType>lambdaQuery()
                .eq(ContactPersonType::getTypeKey, "CONTACT_AGENT_CONTRACT"));
    }

    @Override
    public void movingOrder(List<ContactPersonTypeDto> contactPersonTypeDtos) {
        if (GeneralTool.isEmpty(contactPersonTypeDtos)) {
            throw new GetServiceException(ResultCode.INVALID_PARAM, "传入值为空");
        }
        ContactPersonType ro = BeanCopyUtils.objClone(contactPersonTypeDtos.get(0), ContactPersonType::new);
        Integer oneorder = ro.getViewOrder();
        ContactPersonType rt = BeanCopyUtils.objClone(contactPersonTypeDtos.get(1), ContactPersonType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        contactPersonTypeMapper.updateById(ro);
        contactPersonTypeMapper.updateById(rt);
    }

    /**
     * 获取联系人类型
     *
     * @Override
     * @param isNewType 是否是新增类型
     * @return
     */
    @Override
    public List<ContactPersonTypeVo> getContactPersonTypes(boolean isNewType) {
        List<ContactPersonType> contactPersonTypes = contactPersonTypeMapper
                .selectList(Wrappers.<ContactPersonType>lambdaQuery()
                        .ne(ContactPersonType::getTypeKey, "CONTACT_AGENT_CONTRACT")
                        .orderByDesc(ContactPersonType::getViewOrder));
        if (CollUtil.isEmpty(contactPersonTypes)) {
            return Collections.emptyList();
        }

        List<ContactPersonTypeVo> contactPersonTypeVos = BeanCopyUtils.copyListProperties(contactPersonTypes,
                ContactPersonTypeVo::new);
        return contactPersonTypeVos.stream()
                .filter(contactPersonTypeVo -> isNewType == ContactPersonTypeEnum
                        .isNewContactPersonType(contactPersonTypeVo.getTypeKey()))
                .collect(Collectors.toList());
    }

    /**
     * 获取代理可用联系人类型下拉框（排除已选择的类型）
     *
     * @param fkAppAgentId 代理ID
     * @return 可用的联系人类型列表
     */
    @Override
    public List<ContactPersonTypeVo> getAvailableContactPersonTypes(Long fkAppAgentId) {
        // 获取所有联系人类型
        List<ContactPersonTypeVo> allContactPersonTypes = this.getContactPersonTypes();
        if (CollectionUtil.isEmpty(allContactPersonTypes)) {
            return Collections.emptyList();
        }
        // 判断当前代理是否选择过新的联系人类型
        Set<String> usedNewTypeKeys = this.appAgentContactPersonService.getUsedNewTypeKeys(fkAppAgentId);
        if (CollectionUtil.isEmpty(usedNewTypeKeys)) {
            return allContactPersonTypes;
        }

        // 过滤掉已使用的特定类型
        return allContactPersonTypes.stream()
                .filter(type -> !usedNewTypeKeys.contains(type.getTypeKey()))
                .collect(Collectors.toList());
    }

    /**
     * 获取联系人表关联可用联系人类型下拉框（排除已选择的新类型）
     *
     * @param fkTableId 表ID
     * @return 可用的联系人类型列表
     */
    @Override
    public List<ContactPersonTypeVo> getAvailableTypesByContactPersonTableId(Long fkTableId) {
        // 获取所有联系人类型
        List<ContactPersonTypeVo> allContactPersonTypes = this.getContactPersonTypes();
        if (CollectionUtil.isEmpty(allContactPersonTypes)) {
            return Collections.emptyList();
        }

        // 判断当前表关联是否选择过新的联系人类型
        Set<String> usedNewTypeKeys = contactPersonService.getUsedNewTypeKeysByTableId(fkTableId);
        if (CollectionUtil.isEmpty(usedNewTypeKeys)) {
            return allContactPersonTypes;
        }

        // 过滤掉已使用的新类型
        return allContactPersonTypes.stream()
                .filter(type -> !usedNewTypeKeys.contains(type.getTypeKey()))
                .collect(Collectors.toList());
    }

    private boolean validateAdd(ContactPersonTypeDto contactPersonTypeDto) {
        List<ContactPersonType> list = this.contactPersonTypeMapper.selectList(Wrappers.<ContactPersonType>lambdaQuery()
                .eq(ContactPersonType::getTypeKey, contactPersonTypeDto.getTypeKey()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(ContactPersonTypeDto contactPersonTypeDto) {
        List<ContactPersonType> list = this.contactPersonTypeMapper.selectList(Wrappers.<ContactPersonType>lambdaQuery()
                .eq(ContactPersonType::getTypeKey, contactPersonTypeDto.getTypeKey()));
        System.out.println(list);
        return list.size() <= 0 || list.get(0).getId().equals(contactPersonTypeDto.getId());
    }
}
