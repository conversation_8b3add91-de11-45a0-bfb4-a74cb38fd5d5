package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/12/15
 * @TIME: 17:57
 * @Description:
 **/
@Data
public class TranslationVo extends BaseVoEntity {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @NotBlank(message = "表名不能为空", groups = {Add.class, Update.class})
    private String fkTableName;

    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @NotNull(message = "表Id不能为空", groups = {Add.class, Update.class})
    private Long fkTableId;

    /**
     * 翻译配置Id
     */
    @ApiModelProperty(value = "翻译配置Id")
    private Long fkTranslationMappingId;

    /**
     * 语言枚举code：zh-hk/en-us
     */
    @ApiModelProperty(value = "语言枚举code：zh-hk/en-us")
    private String languageCode;

    /**
     * 翻译内容
     */
    @ApiModelProperty(value = "翻译内容")
    private String translation;

    /**
     * 翻译内容位置
     */
    @ApiModelProperty(value = "翻译内容位置：1 左边 2 右边")
    private Integer type;
}
