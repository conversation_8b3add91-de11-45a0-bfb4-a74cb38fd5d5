package com.get.schoolGateCenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import java.util.Date;
import java.util.List;

@ApiModel("系统资源返回类")
@Data
public class ResourceDto {
    /**
     * 父系统资源Id
     */
    @ApiModelProperty(value = "父系统资源Id")
    private Long fkParentResourceId;

    /**
     * 系统资源Key（主要前端识别）
     */
    @ApiModelProperty(value = "系统资源Key（主要前端识别）")
    private String resourceKey;

    /**
     * 系统资源名称
     */
    @ApiModelProperty(value = "系统资源名称")
    private String resourceName;

    /**
     * api交互_key（主要后端识别）
     */
    @ApiModelProperty(value = "api交互_key（主要后端识别）")
    private String apiKey;

    /**
     * 是否菜单，0否/1是
     */
    @ApiModelProperty(value = "是否菜单，0否/1是")
    private Boolean isMenu;


    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;
    private Boolean isSelected;
    private List<ResourceDto> resourceDtos;

    @ApiModelProperty("id，主键")
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtModified;
    @ApiModelProperty("创建人")
    private String gmtCreateUser;
    @ApiModelProperty("修改人")
    private String gmtModifiedUser;
}
