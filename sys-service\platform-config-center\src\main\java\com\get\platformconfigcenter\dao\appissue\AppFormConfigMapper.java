package com.get.platformconfigcenter.dao.appissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.AppFormConfig;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("issuedb")
public interface AppFormConfigMapper extends BaseMapper<AppFormConfig> {

//    int insert(AppFormConfig record);
//
//    int
//    insertSelective(AppFormConfig record);
//
//    int updateByPrimaryKeySelective(AppFormConfig record);
//
//    int updateByPrimaryKey(AppFormConfig record);
}