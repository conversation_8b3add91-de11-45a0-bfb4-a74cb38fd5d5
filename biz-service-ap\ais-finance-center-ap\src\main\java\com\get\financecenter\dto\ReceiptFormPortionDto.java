package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 修改收款单部分信息参数类
 */
@Data
public class ReceiptFormPortionDto{
    /**
     * 收款单Id
     */
    @ApiModelProperty(value = "收款单Id")
    @NotNull(message = "收款单Id不能为空")
    private Long receiptFormId;

    /**
     * 银行帐号Id（公司）
     */
    @ApiModelProperty(value = "银行帐号Id（公司）")
    @NotNull(message = "银行帐号Id（公司）不能为空")
    private Long fkBankAccountId;

    /**
     * 收款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "收款日期")
    @NotNull(message = "收款日期不能为空")
    private Date receiptDate;

}
