package com.get.votingcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: Hardy
 * @create: 2021/9/23 10:59
 * @verison: 1.0
 * @description:
 */
@Data
public class VotingUpdateDto extends BaseVoEntity implements Serializable {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司Id不能为空", groups = {Add.class, Update.class})
    @Min(value = 1, message = "缺少公司id参数", groups = {Add.class, Update.class})
    private Long fkCompanyId;

    /**
     * 主题名称
     */
    @ApiModelProperty(value = "主题名称")
    @NotBlank(message = "主题名称不能为空", groups = {Add.class, Update.class})
    private String themeName;

    /**
     * 主题Key（系统唯一Key，外部功能模块接入时使用）
     */
    @ApiModelProperty(value = "主题Key（系统唯一Key，外部功能模块接入时使用）")
    @NotBlank(message = "主题Key不能为空", groups = {Add.class, Update.class})
    private String themeKey;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
}
