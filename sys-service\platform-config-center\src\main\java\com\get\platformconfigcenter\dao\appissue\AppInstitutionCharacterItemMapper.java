package com.get.platformconfigcenter.dao.appissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.AppInstitutionCharacterItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("issuedb")
public interface AppInstitutionCharacterItemMapper extends BaseMapper<AppInstitutionCharacterItem> {
//    int insert(AppInstitutionCharacterItem record);
//
//    int insertSelective(AppInstitutionCharacterItem record);
//
//    int updateByPrimaryKeySelective(AppInstitutionCharacterItem record);
//
//    int updateByPrimaryKey(AppInstitutionCharacterItem record);
//
//    /**
//     * 根据ids删除数据
//     *
//     * @Date 11:51 2021/5/24
//     * <AUTHOR>
//     */
//    int deleteByIds(@Param("ids") List<Long> ids);
//
//    /**
//     * 获取最大排序值
//     *
//     * @return
//     */
//    Integer getMaxViewOrder();
}