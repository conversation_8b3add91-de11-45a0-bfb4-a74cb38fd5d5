package com.get.mpscenter.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.institutioncenter.vo.InstitutionVo;
import com.get.mpscenter.dao.*;
import com.get.mpscenter.dto.InstitutionProviderMpsCommissionDto;
import com.get.mpscenter.dto.KeyWordMajorLevelDto;
import com.get.mpscenter.dto.MpsCommissionDto;
import com.get.mpscenter.entity.*;
import com.get.mpscenter.service.InstitutionProviderMpsCommissionService;
import com.get.mpscenter.vo.InstitutionProviderBingVo;
import com.get.mpscenter.vo.InstitutionProviderCommissionVo;
import com.get.mpscenter.vo.InstitutionProviderMpsCommissionVo;
import com.get.mpscenter.vo.KeyWordVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Year;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class InstitutionProviderMpsCommissionServiceImpl extends GetServiceImpl<InstitutionProviderMpsCommissionMapper, InstitutionProviderMpsCommission> implements InstitutionProviderMpsCommissionService {

    @Resource
    private InstitutionProviderMpsCommissionMapper institutionProviderMpsCommissionMapper;

    @Resource
    private InstitutionProviderMpsCommissionInstitutionMapper institutionProviderMpsCommissionInstitutionMapper;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private InstitutionProviderMpsCommissionCompanyMapper institutionProviderMpsCommissionCompanyMapper;

    @Resource
    private UtilService utilService;

    @Resource
    private MpsKeyWordMapper mpsKeyWordMapper;

    @Resource
    private MpsKeyWordMajorLevelMapper mpsKeyWordMajorLevelMapper;

    @Resource
    private InstitutionProviderMpsCommissionAreaCountryMapper institutionProviderMpsCommissionAreaCountryMapper;

    @Resource
    private InstitutionProviderMpsCommissionMajorLevelMapper institutionProviderMpsCommissionMajorLevelMapper;

    @Override
    public List<InstitutionProviderMpsCommissionVo> getCommissionList(InstitutionProviderMpsCommissionDto institutionProviderMpsCommissionDto, Page page) {

        if (GeneralTool.isEmpty(institutionProviderMpsCommissionDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }

//        if (GeneralTool.isNotEmpty(institutionProviderMpsCommissionVo.getFkInstitutionId())){
//            Set<Long> institutionProviderIds = institutionCenterClient.getInstitutionProviderByInstitution(institutionProviderMpsCommissionVo.getFkInstitutionId());
//            institutionProviderMpsCommissionVo.setSuitInstitutionProviderIds(institutionProviderIds);
//        }


        IPage<InstitutionProviderMpsCommissionVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<InstitutionProviderMpsCommissionVo> institutionProviderMpsCommissionVos = institutionProviderMpsCommissionMapper.getCommissionList(iPage, institutionProviderMpsCommissionDto);
        page.setAll((int) iPage.getTotal());

        //赋值
        setName(institutionProviderMpsCommissionVos);

        return institutionProviderMpsCommissionVos;
    }

    private void setName(List<InstitutionProviderMpsCommissionVo> institutionProviderMpsCommissionVos) {
        if (GeneralTool.isNotEmpty(institutionProviderMpsCommissionVos)){
            Set<Long> ids = institutionProviderMpsCommissionVos.stream().map(InstitutionProviderMpsCommissionVo::getId).collect(Collectors.toSet());

            List<InstitutionProviderMpsCommissionInstitution> institutionProviderMpsCommissionInstitutions = institutionProviderMpsCommissionInstitutionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderMpsCommissionInstitution>()
                    .in(InstitutionProviderMpsCommissionInstitution::getFkInstitutionProviderMpsCommissionId, ids));
            HashMap<Long, List<Long>> institutionProviderMpsCommissionInstitutionMap = institutionProviderMpsCommissionInstitutions.stream()
                    .collect(HashMap<Long, List<Long>>::new, (m, v) -> m.computeIfAbsent(v.getFkInstitutionProviderMpsCommissionId(),
                            k -> new ArrayList<>()).add(v.getFkInstitutionId()), HashMap::putAll);

            Set<Long> institutionIds = institutionProviderMpsCommissionInstitutions.stream().map(InstitutionProviderMpsCommissionInstitution::getFkInstitutionId).collect(Collectors.toSet());
            Map<Long, String> institutionNameMap = institutionCenterClient.getInstitutionNamesByIds(institutionIds).getData();


            List<InstitutionProviderMpsCommissionCompany> institutionProviderMpsCommissionCompanies = institutionProviderMpsCommissionCompanyMapper.selectList(new LambdaQueryWrapper<InstitutionProviderMpsCommissionCompany>()
                    .in(InstitutionProviderMpsCommissionCompany::getFkInstitutionProviderMpsCommissionId, ids));
            Set<Long> companyIds = institutionProviderMpsCommissionCompanies.stream().map(InstitutionProviderMpsCommissionCompany::getFkCompanyId).collect(Collectors.toSet());
            Map<Long, String> companyMap = permissionCenterClient.getCompanyNamesByIds(companyIds).getData();
            HashMap<Long, List<Long>> mpsCommissionCompanyMap = institutionProviderMpsCommissionCompanies.stream()
                    .collect(HashMap<Long, List<Long>>::new, (m, v) -> m.computeIfAbsent(v.getFkInstitutionProviderMpsCommissionId(),
                            k -> new ArrayList<>()).add(v.getFkCompanyId()), HashMap::putAll);

            Set<Long> institutionProviderIds = institutionProviderMpsCommissionVos.stream().map(InstitutionProviderMpsCommissionVo::getFkInstitutionProviderId).collect(Collectors.toSet());
            Map<Long, String> institutionProviderNameMap = institutionCenterClient.getInstitutionProviderNamesByIds(institutionProviderIds).getData();
            for (InstitutionProviderMpsCommissionVo institutionProviderMpsCommissionVo : institutionProviderMpsCommissionVos) {

                //学校名称
                List<Long> institutionIdList = institutionProviderMpsCommissionInstitutionMap.get(institutionProviderMpsCommissionVo.getId());
                List<String> institutionNames = new ArrayList<>();
                if (GeneralTool.isNotEmpty(institutionIdList)){
                    for (Long institutionId : institutionIdList) {
                        institutionNames.add(institutionNameMap.get(institutionId));
                    }
                }
                institutionProviderMpsCommissionVo.setFkInstitutionNames(institutionNames);

                //公司名
                if (GeneralTool.isNotEmpty(companyMap) && GeneralTool.isNotEmpty(mpsCommissionCompanyMap)
                        && GeneralTool.isNotEmpty(mpsCommissionCompanyMap.get(institutionProviderMpsCommissionVo.getId()))){
                    StringBuilder companyStringBuilder = new StringBuilder();
                    Set<Long> fkCompanyIds = new HashSet<>();

                    List<Long> companyList = mpsCommissionCompanyMap.get(institutionProviderMpsCommissionVo.getId());
                    for (Long company : companyList) {
                        fkCompanyIds.add(company);
                        String companyName = companyMap.get(company);
                        if (GeneralTool.isEmpty(companyStringBuilder)){
                            companyStringBuilder.append(companyName);
                        }else {
                            companyStringBuilder.append(",").append(companyName);
                        }
                    }
                    institutionProviderMpsCommissionVo.setFkCompanyNames(companyStringBuilder.toString());
                    institutionProviderMpsCommissionVo.setFkCompanyIds(fkCompanyIds);
                }

                //学校提供商名字
                if (GeneralTool.isNotEmpty(institutionProviderNameMap)){
                    institutionProviderMpsCommissionVo.setFkInstitutionProviderName(institutionProviderNameMap.get(institutionProviderMpsCommissionVo.getFkInstitutionProviderId()));
                }
            }

        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long add(MpsCommissionDto mpsCommissionDto) {
        if (GeneralTool.isEmpty(mpsCommissionDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        InstitutionProviderMpsCommission institutionProviderMpsCommission = BeanCopyUtils.objClone(mpsCommissionDto, InstitutionProviderMpsCommission::new);

        //合约编号取最新合约
        String contractNum = institutionCenterClient.getContractNewByProviderId(institutionProviderMpsCommission.getFkInstitutionProviderId());
        if (StringUtils.isNotEmpty(contractNum)){
            institutionProviderMpsCommission.setContractNum(contractNum);
        }

        utilService.setCreateInfo(institutionProviderMpsCommission);
        institutionProviderMpsCommissionMapper.insert(institutionProviderMpsCommission);



        //学校添加
        if (GeneralTool.isNotEmpty(mpsCommissionDto.getFkInstitutionIds())){
            for (Long fkInstitutionId : mpsCommissionDto.getFkInstitutionIds()) {
                InstitutionProviderMpsCommissionInstitution institutionProviderMpsCommissionInstitution = new InstitutionProviderMpsCommissionInstitution();
                institutionProviderMpsCommissionInstitution.setFkInstitutionProviderMpsCommissionId(institutionProviderMpsCommission.getId());
                institutionProviderMpsCommissionInstitution.setFkInstitutionId(fkInstitutionId);
                utilService.setCreateInfo(institutionProviderMpsCommissionInstitution);
                institutionProviderMpsCommissionInstitutionMapper.insert(institutionProviderMpsCommissionInstitution);
            }
        }


        //合约覆盖国家/地区
        if (GeneralTool.isNotEmpty(mpsCommissionDto.getFkAreaCountryIds())){
            for (Long fkAreaCountryId : mpsCommissionDto.getFkAreaCountryIds()) {
                InstitutionProviderMpsCommissionAreaCountry institutionProviderMpsCommissionAreaCountry = new InstitutionProviderMpsCommissionAreaCountry();
                institutionProviderMpsCommissionAreaCountry.setFkInstitutionProviderMpsCommissionId(institutionProviderMpsCommission.getId());
                institutionProviderMpsCommissionAreaCountry.setIsInclude(mpsCommissionDto.getIsInclude());
                institutionProviderMpsCommissionAreaCountry.setFkAreaCountryId(fkAreaCountryId);
                utilService.setCreateInfo(institutionProviderMpsCommissionAreaCountry);
                institutionProviderMpsCommissionAreaCountryMapper.insert(institutionProviderMpsCommissionAreaCountry);

            }
        }


        //课程等级
        if (GeneralTool.isNotEmpty(mpsCommissionDto.getFkMajorLevelIds())){
            for (Long fkMajorLevelId : mpsCommissionDto.getFkMajorLevelIds()) {
                InstitutionProviderMpsCommissionMajorLevel institutionProviderMpsCommissionMajorLevel = new InstitutionProviderMpsCommissionMajorLevel();
                institutionProviderMpsCommissionMajorLevel.setFkInstitutionProviderMpsCommissionId(institutionProviderMpsCommission.getId());
                institutionProviderMpsCommissionMajorLevel.setFkMajorLevelId(fkMajorLevelId);
                utilService.setCreateInfo(institutionProviderMpsCommissionMajorLevel);
                institutionProviderMpsCommissionMajorLevelMapper.insert(institutionProviderMpsCommissionMajorLevel);
            }
        }


        //公司添加
        if (GeneralTool.isNotEmpty(mpsCommissionDto.getFkCompanyIds())){
            for (Long fkCompanyId : mpsCommissionDto.getFkCompanyIds()) {
                InstitutionProviderMpsCommissionCompany institutionProviderMpsCommissionCompany = new InstitutionProviderMpsCommissionCompany();
                institutionProviderMpsCommissionCompany.setFkCompanyId(fkCompanyId);
                institutionProviderMpsCommissionCompany.setFkInstitutionProviderMpsCommissionId(institutionProviderMpsCommission.getId());
                utilService.setCreateInfo(institutionProviderMpsCommissionCompany);
                institutionProviderMpsCommissionCompanyMapper.insert(institutionProviderMpsCommissionCompany);
            }
        }

        return institutionProviderMpsCommission.getId();

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id){
        if (GeneralTool.isEmpty(id))
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        InstitutionProviderMpsCommission institutionProviderMpsCommission = institutionProviderMpsCommissionMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionProviderMpsCommission)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("data_not_exist"));
        }
        // 删除佣金学校关系表
        institutionProviderMpsCommissionInstitutionMapper.delete(Wrappers.<InstitutionProviderMpsCommissionInstitution>lambdaQuery()
                .eq(InstitutionProviderMpsCommissionInstitution::getFkInstitutionProviderMpsCommissionId,id));
        // 删除佣金公司关系表
        institutionProviderMpsCommissionCompanyMapper.delete(Wrappers.<InstitutionProviderMpsCommissionCompany>lambdaQuery()
                .eq(InstitutionProviderMpsCommissionCompany::getFkInstitutionProviderMpsCommissionId,id));
        // 删除佣金国家/地区
        institutionProviderMpsCommissionAreaCountryMapper.delete(Wrappers.<InstitutionProviderMpsCommissionAreaCountry>lambdaQuery()
                        .eq(InstitutionProviderMpsCommissionAreaCountry::getFkInstitutionProviderMpsCommissionId,id));
        // 删除佣金
        baseMapper.deleteById(id);
        //删除课程等级
        institutionProviderMpsCommissionMajorLevelMapper.delete(Wrappers.<InstitutionProviderMpsCommissionMajorLevel>lambdaQuery()
                .eq(InstitutionProviderMpsCommissionMajorLevel::getFkInstitutionProviderMpsCommissionId,id));

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addKeyWord(KeyWordMajorLevelDto keyWordMajorLevelDto) {

        MpsKeyWord mpsKeyWord = BeanCopyUtils.objClone(keyWordMajorLevelDto, MpsKeyWord::new);
        utilService.setCreateInfo(mpsKeyWord);
        if (GeneralTool.isEmpty(mpsKeyWord.getTypeKey())){
            mpsKeyWord.setTypeKey("INPUT");
        }
        if (GeneralTool.isNotEmpty(mpsKeyWordMapper.selectList(Wrappers.<MpsKeyWord>lambdaQuery().eq(MpsKeyWord::getKeyWord, keyWordMajorLevelDto.getKeyWord())))){
            throw new GetServiceException(LocaleMessageUtils.getMessage("key_word_exist"));
        }
        mpsKeyWordMapper.insert(mpsKeyWord);
        mpsKeyWord.setViewOrder(mpsKeyWordMapper.getMaxOrder());
        mpsKeyWordMapper.updateById(mpsKeyWord);
        Set<Long> fkMajorLevelIds = keyWordMajorLevelDto.getFkMajorLevelIds();

        for (Long fkMajorLevelId : fkMajorLevelIds) {
            MpsKeyWordMajorLevel mpsKeyWordMajorLevel = new MpsKeyWordMajorLevel();
            mpsKeyWordMajorLevel.setFkMpsKeyWordId(mpsKeyWord.getId());
            mpsKeyWordMajorLevel.setFkMajorLevelId(fkMajorLevelId);
            utilService.setCreateInfo(mpsKeyWordMajorLevel);
            mpsKeyWordMajorLevelMapper.insert(mpsKeyWordMajorLevel);
        }

    }

    @Override
    public InstitutionProviderBingVo getProviderInstitutionList(Long fkInstitutionId, Long fkInstitutionProviderId) {
        InstitutionProviderBingVo institutionProviderBingVo = new InstitutionProviderBingVo();
        List<InstitutionProviderCommissionVo> data=institutionProviderMpsCommissionMapper.getProviderInstitutionList(fkInstitutionId,fkInstitutionProviderId);

        if (GeneralTool.isNotEmpty(data)){

            Set<Long> fkInstitutionProviderIds = data.stream().map(InstitutionProviderCommissionVo::getFkInstitutionProviderId).collect(Collectors.toSet());
            Map<Long, String> providerMap = institutionCenterClient.getInstitutionProviderNamesByIds(fkInstitutionProviderIds).getData();

            for (InstitutionProviderCommissionVo institutionProviderCommissionDto1 : data) {
                if (GeneralTool.isNotEmpty(providerMap)){
                    institutionProviderCommissionDto1.setFkInstitutionProviderName(providerMap.get(institutionProviderCommissionDto1.getFkInstitutionProviderId()));
                }
            }
            String institutionName = institutionCenterClient.getInstitutionName(fkInstitutionId).getData();
            institutionProviderBingVo.setFkInstitutionName(institutionName);
            institutionProviderBingVo.setInstitutionProviderCommissionDtos(data);
            institutionProviderBingVo.setIsBing(true);
        }else {
            institutionProviderBingVo.setIsBing(false);
        }
        return institutionProviderBingVo;
    }

    @Override
    public InstitutionProviderMpsCommissionVo findCommissionById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionProviderMpsCommission institutionProviderMpsCommission = institutionProviderMpsCommissionMapper.selectById(id);
        if (Objects.isNull(institutionProviderMpsCommission)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionProviderMpsCommissionVo institutionProviderMpsCommissionVo = BeanCopyUtils.objClone(institutionProviderMpsCommission, InstitutionProviderMpsCommissionVo::new);

        setName(institutionProviderMpsCommissionVo);

        return institutionProviderMpsCommissionVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public InstitutionProviderMpsCommissionVo update(MpsCommissionDto mpsCommissionDto) {
        if (GeneralTool.isEmpty(mpsCommissionDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        InstitutionProviderMpsCommission institutionProviderMpsCommission = BeanCopyUtils.objClone(mpsCommissionDto, InstitutionProviderMpsCommission::new);

        utilService.setUpdateInfo(institutionProviderMpsCommission);
        institutionProviderMpsCommissionMapper.updateById(institutionProviderMpsCommission);

        setMpsSon(institutionProviderMpsCommission);


        //学校
        //全删全增
        institutionProviderMpsCommissionInstitutionMapper.delete(new LambdaQueryWrapper<InstitutionProviderMpsCommissionInstitution>()
                .eq(InstitutionProviderMpsCommissionInstitution::getFkInstitutionProviderMpsCommissionId, mpsCommissionDto.getId()));

        if(GeneralTool.isNotEmpty(mpsCommissionDto.getFkInstitutionIds())){
            for (Long fkInstitutionId : mpsCommissionDto.getFkInstitutionIds()) {
                InstitutionProviderMpsCommissionInstitution institutionProviderMpsCommissionInstitution = new InstitutionProviderMpsCommissionInstitution();
                institutionProviderMpsCommissionInstitution.setFkInstitutionProviderMpsCommissionId(institutionProviderMpsCommission.getId());
                institutionProviderMpsCommissionInstitution.setFkInstitutionId(fkInstitutionId);
                utilService.setCreateInfo(institutionProviderMpsCommissionInstitution);
                institutionProviderMpsCommissionInstitutionMapper.insert(institutionProviderMpsCommissionInstitution);
            }
        }

        //全删全增
        //合约覆盖国家/地区
        institutionProviderMpsCommissionAreaCountryMapper.delete(new LambdaQueryWrapper<InstitutionProviderMpsCommissionAreaCountry>()
                .eq(InstitutionProviderMpsCommissionAreaCountry::getFkInstitutionProviderMpsCommissionId, mpsCommissionDto.getId()));
        if (GeneralTool.isNotEmpty(mpsCommissionDto.getFkAreaCountryIds())){
            for (Long fkAreaCountryId : mpsCommissionDto.getFkAreaCountryIds()) {
                InstitutionProviderMpsCommissionAreaCountry institutionProviderMpsCommissionAreaCountry = new InstitutionProviderMpsCommissionAreaCountry();
                institutionProviderMpsCommissionAreaCountry.setFkInstitutionProviderMpsCommissionId(institutionProviderMpsCommission.getId());
                institutionProviderMpsCommissionAreaCountry.setIsInclude(mpsCommissionDto.getIsInclude());
                institutionProviderMpsCommissionAreaCountry.setFkAreaCountryId(fkAreaCountryId);
                utilService.setCreateInfo(institutionProviderMpsCommissionAreaCountry);
                institutionProviderMpsCommissionAreaCountryMapper.insert(institutionProviderMpsCommissionAreaCountry);

            }
        }

        //课程等级
        //全删全增
        institutionProviderMpsCommissionMajorLevelMapper.delete(new LambdaQueryWrapper<InstitutionProviderMpsCommissionMajorLevel>()
                .eq(InstitutionProviderMpsCommissionMajorLevel::getFkInstitutionProviderMpsCommissionId,institutionProviderMpsCommission.getId()));

        if (GeneralTool.isNotEmpty(mpsCommissionDto.getFkMajorLevelIds())){
            for (Long fkMajorLevelId : mpsCommissionDto.getFkMajorLevelIds()) {
                InstitutionProviderMpsCommissionMajorLevel institutionProviderMpsCommissionMajorLevel = new InstitutionProviderMpsCommissionMajorLevel();
                institutionProviderMpsCommissionMajorLevel.setFkInstitutionProviderMpsCommissionId(institutionProviderMpsCommission.getId());
                institutionProviderMpsCommissionMajorLevel.setFkMajorLevelId(fkMajorLevelId);
                utilService.setCreateInfo(institutionProviderMpsCommissionMajorLevel);
                institutionProviderMpsCommissionMajorLevelMapper.insert(institutionProviderMpsCommissionMajorLevel);
            }
        }


        institutionProviderMpsCommissionCompanyMapper.delete(new LambdaQueryWrapper<InstitutionProviderMpsCommissionCompany>()
                .eq(InstitutionProviderMpsCommissionCompany::getFkInstitutionProviderMpsCommissionId,institutionProviderMpsCommission.getId()));
        //公司添加
        if (GeneralTool.isNotEmpty(mpsCommissionDto.getFkCompanyIds())){
            for (Long fkCompanyId : mpsCommissionDto.getFkCompanyIds()) {
                InstitutionProviderMpsCommissionCompany institutionProviderMpsCommissionCompany = new InstitutionProviderMpsCommissionCompany();
                institutionProviderMpsCommissionCompany.setFkCompanyId(fkCompanyId);
                institutionProviderMpsCommissionCompany.setFkInstitutionProviderMpsCommissionId(institutionProviderMpsCommission.getId());
                utilService.setCreateInfo(institutionProviderMpsCommissionCompany);
                institutionProviderMpsCommissionCompanyMapper.insert(institutionProviderMpsCommissionCompany);
            }
        }

        return findCommissionById(mpsCommissionDto.getId());
    }

    /**
     * 修改子佣金,apple要求修改的子段
     * @param institutionProviderMpsCommission
     */
    private void setMpsSon(InstitutionProviderMpsCommission institutionProviderMpsCommission) {
        InstitutionProviderMpsCommission institutionProviderMpsCommissionSon = new InstitutionProviderMpsCommission();
        institutionProviderMpsCommissionSon.setYear(institutionProviderMpsCommission.getYear());
        institutionProviderMpsCommissionSon.setFkInstitutionProviderId(institutionProviderMpsCommission.getFkInstitutionProviderId());
        institutionProviderMpsCommissionSon.setTitle(institutionProviderMpsCommission.getTitle());
        institutionProviderMpsCommissionSon.setContractNum(institutionProviderMpsCommission.getContractNum());
        institutionProviderMpsCommissionSon.setCommission(institutionProviderMpsCommission.getCommission());
        institutionProviderMpsCommissionSon.setCommissionNote(institutionProviderMpsCommission.getCommissionNote());
        institutionProviderMpsCommissionSon.setFollowCommission(institutionProviderMpsCommission.getFollowCommission());
        institutionProviderMpsCommissionSon.setFollowCommissionNote(institutionProviderMpsCommission.getFollowCommissionNote());
        institutionProviderMpsCommissionSon.setProgrammesMode(institutionProviderMpsCommission.getProgrammesMode());
        institutionProviderMpsCommissionSon.setUseStatus(institutionProviderMpsCommission.getUseStatus());
        institutionProviderMpsCommissionSon.setRemark(institutionProviderMpsCommission.getRemark());
        utilService.setUpdateInfo(institutionProviderMpsCommissionSon);
        institutionProviderMpsCommissionMapper.update(institutionProviderMpsCommissionSon,new LambdaQueryWrapper<InstitutionProviderMpsCommission>()
                .eq(InstitutionProviderMpsCommission::getFkInstitutionProviderMpsCommissionIdFrom, institutionProviderMpsCommission.getId()));
    }

    @Override
    public List<KeyWordVo> getKeyWordList() {
        List<KeyWordVo> keyWordVos = new ArrayList<>();
        List<MpsKeyWord> mpsKeyWords = mpsKeyWordMapper.selectList(new LambdaQueryWrapper<MpsKeyWord>());
        if (GeneralTool.isEmpty(mpsKeyWords)){
            return keyWordVos;
        }

        HashMap<Long, Set<Long>> mpsKeyWordMajorLevelMap = new HashMap<>();
        List<Long> ids = mpsKeyWords.stream().map(MpsKeyWord::getId).collect(Collectors.toList());
        List<MpsKeyWordMajorLevel> mpsKeyWordMajorLevels = mpsKeyWordMajorLevelMapper.selectList(new LambdaQueryWrapper<MpsKeyWordMajorLevel>().in(MpsKeyWordMajorLevel::getFkMpsKeyWordId, ids));
        Set<Long> fkMajorLevelIds = mpsKeyWordMajorLevels.stream().map(MpsKeyWordMajorLevel::getFkMajorLevelId).collect(Collectors.toSet());
        Map<Long, String> levelNameMap = institutionCenterClient.getMajorLevelNamesByIds(fkMajorLevelIds).getData();
        if (GeneralTool.isNotEmpty(mpsKeyWordMajorLevels)){
            mpsKeyWordMajorLevelMap = mpsKeyWordMajorLevels.stream()
                    .collect(HashMap<Long, Set<Long>>::new, (m, v) -> m.computeIfAbsent(v.getFkMpsKeyWordId(),
                            k -> new HashSet<>()).add(v.getFkMajorLevelId()), HashMap::putAll);
        }
        for (MpsKeyWord mpsKeyWord : mpsKeyWords) {
            KeyWordVo keyWordVo = BeanCopyUtils.objClone(mpsKeyWord, KeyWordVo::new);
            if (GeneralTool.isNotEmpty(mpsKeyWordMajorLevelMap) && mpsKeyWordMajorLevelMap.containsKey(mpsKeyWord.getId())){
                keyWordVo.setMajorLevelIds(mpsKeyWordMajorLevelMap.get(keyWordVo.getId()));
                keyWordVo.setMajorLevelNames(mpsKeyWordMajorLevelMap.get(keyWordVo.getId()).stream().map(levelId -> levelNameMap.get(levelId)).collect(Collectors.toList()));
            }
            keyWordVos.add(keyWordVo);
        }
        return keyWordVos;
    }

    @Override
    public List<BaseSelectEntity> getYearList() {
        List<BaseSelectEntity> yearList = institutionProviderMpsCommissionMapper.getYearList();

        if (GeneralTool.isEmpty(yearList)) {
            List<BaseSelectEntity> list = new ArrayList<BaseSelectEntity>();
            BaseSelectEntity selectEntity = new BaseSelectEntity();
            selectEntity.setMode(Year.now().getValue());
            list.add(selectEntity);
            return list;
        }
        return yearList;
    }

    @Override
    public List<BaseSelectEntity> getInstitutionProviderList(Long fkInstitutionId) {
        return institutionProviderMpsCommissionMapper.getInstitutionProviderList(fkInstitutionId);
    }

    @Override
    public List<BaseSelectEntity> getInstitutionList(Long fkInstitutionProviderId) {
        Result<List<InstitutionVo>> providerList = institutionCenterClient.getMpsInstitutionProviderList(fkInstitutionProviderId);
        return  BeanCopyUtils.copyListProperties(providerList.getData(), BaseSelectEntity::new);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(Long fkProviderMpsCommissionId,Integer start, Integer end) {
        LambdaQueryWrapper<InstitutionProviderMpsCommission> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start){
            lambdaQueryWrapper.between(InstitutionProviderMpsCommission::getViewOrder,start,end).orderByDesc(InstitutionProviderMpsCommission::getViewOrder);
        }else {
            lambdaQueryWrapper.between(InstitutionProviderMpsCommission::getViewOrder,end,start).orderByDesc(InstitutionProviderMpsCommission::getViewOrder);

        }
        lambdaQueryWrapper.eq(InstitutionProviderMpsCommission::getId,fkProviderMpsCommissionId);
        List<InstitutionProviderMpsCommission> institutionProviderMpsCommissions = list(lambdaQueryWrapper);

        //从下上移 列表倒序排序
        List<InstitutionProviderMpsCommission> updateList = Lists.newArrayList();
        if (end>start){
            int finalEnd = end;
            List<InstitutionProviderMpsCommission> sortedList = Lists.newArrayList();
            InstitutionProviderMpsCommission online = institutionProviderMpsCommissions.get(institutionProviderMpsCommissions.size() - 1);
            sortedList.add(online);
            institutionProviderMpsCommissions.remove(institutionProviderMpsCommissions.size() - 1);
            sortedList.addAll(institutionProviderMpsCommissions);
            for (InstitutionProviderMpsCommission themeOnline : sortedList) {
                themeOnline.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<InstitutionProviderMpsCommission> sortedList = Lists.newArrayList();
            InstitutionProviderMpsCommission online = institutionProviderMpsCommissions.get(0);
            institutionProviderMpsCommissions.remove(0);
            sortedList.addAll(institutionProviderMpsCommissions);
            sortedList.add(online);
            for (InstitutionProviderMpsCommission themeOnline : sortedList) {
                themeOnline.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InstitutionProviderMpsCommissionVo copyMpsCommission(Long id) {

        //查询佣金
        InstitutionProviderMpsCommissionVo commissionCopy = findCommissionById(id);
        if (GeneralTool.isEmpty(commissionCopy)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionProviderMpsCommission institutionProviderMpsCommission = new InstitutionProviderMpsCommission();
        BeanUtils.copyProperties(commissionCopy, institutionProviderMpsCommission);
        institutionProviderMpsCommission.setId(null);
        institutionProviderMpsCommission.setFkInstitutionProviderMpsCommissionIdFrom(id);
        utilService.setCreateInfo(institutionProviderMpsCommission);
        institutionProviderMpsCommissionMapper.insert(institutionProviderMpsCommission);
        //设置课程等级
        if (GeneralTool.isNotEmpty(commissionCopy.getMajorLevels())){
            for (Long item : commissionCopy.getMajorLevels()) {
                InstitutionProviderMpsCommissionMajorLevel institutionProviderMpsCommissionMajorLevel = new InstitutionProviderMpsCommissionMajorLevel();
                institutionProviderMpsCommissionMajorLevel.setFkInstitutionProviderMpsCommissionId(institutionProviderMpsCommission.getId());
                institutionProviderMpsCommissionMajorLevel.setFkMajorLevelId(item);
                utilService.setCreateInfo(institutionProviderMpsCommissionMajorLevel);
                institutionProviderMpsCommissionMajorLevelMapper.insert(institutionProviderMpsCommissionMajorLevel);
            }
        }

        //设置公司
        if (GeneralTool.isNotEmpty(commissionCopy.getFkCompanyIds())){
            for (Long item : commissionCopy.getFkCompanyIds()) {
                InstitutionProviderMpsCommissionCompany institutionProviderMpsCommissionCompany = new InstitutionProviderMpsCommissionCompany();
                institutionProviderMpsCommissionCompany.setFkCompanyId(item);
                institutionProviderMpsCommissionCompany.setFkInstitutionProviderMpsCommissionId(institutionProviderMpsCommission.getId());
                utilService.setCreateInfo(institutionProviderMpsCommissionCompany);
                institutionProviderMpsCommissionCompanyMapper.insert(institutionProviderMpsCommissionCompany);
            }
        }


        //设置关联学校
        if (GeneralTool.isNotEmpty(commissionCopy.getFkInstitutionIds())){
            for (Long item : commissionCopy.getFkInstitutionIds()) {
                InstitutionProviderMpsCommissionInstitution institutionProviderMpsCommissionInstitution = new InstitutionProviderMpsCommissionInstitution();
                institutionProviderMpsCommissionInstitution.setFkInstitutionId(item);
                institutionProviderMpsCommissionInstitution.setFkInstitutionProviderMpsCommissionId(institutionProviderMpsCommission.getId());
                utilService.setCreateInfo(institutionProviderMpsCommissionInstitution);
                institutionProviderMpsCommissionInstitutionMapper.insert(institutionProviderMpsCommissionInstitution);
            }
        }
        //适用国家
        if (GeneralTool.isNotEmpty(commissionCopy.getFkAreaCountryIds())){
            for (Long item : commissionCopy.getFkAreaCountryIds()) {
                InstitutionProviderMpsCommissionAreaCountry institutionProviderMpsCommissionAreaCountry = new InstitutionProviderMpsCommissionAreaCountry();
                institutionProviderMpsCommissionAreaCountry.setFkInstitutionProviderMpsCommissionId(institutionProviderMpsCommission.getId());
                institutionProviderMpsCommissionAreaCountry.setFkAreaCountryId(item);
                institutionProviderMpsCommissionAreaCountry.setIsInclude(true);
                utilService.setCreateInfo(institutionProviderMpsCommissionAreaCountry);
                institutionProviderMpsCommissionAreaCountryMapper.insert(institutionProviderMpsCommissionAreaCountry);
            }
        }
        return findCommissionById(institutionProviderMpsCommission.getId());
    }

    @Override
    public void updateActive(Long id, Boolean status) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionProviderMpsCommission institutionProviderMpsCommission = new InstitutionProviderMpsCommission();
        institutionProviderMpsCommission.setId(id);
        institutionProviderMpsCommission.setIsActive(status);
        utilService.updateUserInfoToEntity(institutionProviderMpsCommission);
        institutionProviderMpsCommissionMapper.updateById(institutionProviderMpsCommission);
    }

    private void setName(InstitutionProviderMpsCommissionVo institutionProviderMpsCommissionVo) {
        //公司
        List<InstitutionProviderMpsCommissionCompany> institutionProviderMpsCommissionCompanies = institutionProviderMpsCommissionCompanyMapper.selectList(new LambdaQueryWrapper<InstitutionProviderMpsCommissionCompany>()
                .eq(InstitutionProviderMpsCommissionCompany::getFkInstitutionProviderMpsCommissionId, institutionProviderMpsCommissionVo.getId()));
        Set<Long> companyIds = institutionProviderMpsCommissionCompanies.stream().map(InstitutionProviderMpsCommissionCompany::getFkCompanyId).collect(Collectors.toSet());
        Map<Long, String> companyMap = permissionCenterClient.getCompanyNamesByIds(companyIds).getData();
        HashMap<Long, Set<Long>> mpsCommissionCompanyMap = institutionProviderMpsCommissionCompanies.stream()
                .collect(HashMap<Long, Set<Long>>::new, (m, v) -> m.computeIfAbsent(v.getFkInstitutionProviderMpsCommissionId(),
                        k -> new HashSet<>()).add(v.getFkCompanyId()), HashMap::putAll);
        if (GeneralTool.isNotEmpty(companyMap)){
            StringBuilder companyStringBuilder = new StringBuilder();

            Set<Long> companyList = mpsCommissionCompanyMap.get(institutionProviderMpsCommissionVo.getId());
            for (Long company : companyList) {
                String companyName = companyMap.get(company);
                if (GeneralTool.isEmpty(companyStringBuilder)){
                    companyStringBuilder.append(companyName);
                }else {
                    companyStringBuilder.append(",").append(companyName);
                }
            }
            institutionProviderMpsCommissionVo.setFkCompanyNames(companyStringBuilder.toString());
            institutionProviderMpsCommissionVo.setFkCompanyIds(companyList);
        }

        //学校
        List<InstitutionProviderMpsCommissionInstitution> institutionProviderMpsCommissionInstitutions = institutionProviderMpsCommissionInstitutionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderMpsCommissionInstitution>()
                .eq(InstitutionProviderMpsCommissionInstitution::getFkInstitutionProviderMpsCommissionId, institutionProviderMpsCommissionVo.getId()));
        if (GeneralTool.isNotEmpty(institutionProviderMpsCommissionInstitutions)){
            Set<Long> institutionIds = institutionProviderMpsCommissionInstitutions.stream().map(InstitutionProviderMpsCommissionInstitution::getFkInstitutionId).collect(Collectors.toSet());
            institutionProviderMpsCommissionVo.setFkInstitutionIds(institutionIds);
            Map<Long, String> institutionNameMap = institutionCenterClient.getInstitutionNamesByIds(institutionIds).getData();
            institutionProviderMpsCommissionVo.setFkInstitutionNames(institutionNameMap.values().stream().collect(Collectors.toList()));
        }

        //课程等级
        List<InstitutionProviderMpsCommissionMajorLevel> institutionProviderMpsCommissionMajorLevels = institutionProviderMpsCommissionMajorLevelMapper.selectList(new LambdaQueryWrapper<InstitutionProviderMpsCommissionMajorLevel>()
                .eq(InstitutionProviderMpsCommissionMajorLevel::getFkInstitutionProviderMpsCommissionId, institutionProviderMpsCommissionVo.getId()));
        if (GeneralTool.isNotEmpty(institutionProviderMpsCommissionMajorLevels)){
            Set<Long> majorLevelIds = institutionProviderMpsCommissionMajorLevels.stream().map(InstitutionProviderMpsCommissionMajorLevel::getFkMajorLevelId).collect(Collectors.toSet());
            institutionProviderMpsCommissionVo.setMajorLevels(majorLevelIds);
            Map<Long, String> majorLevelNameMap = institutionCenterClient.getMajorLevelNamesByIds(majorLevelIds).getData();
            institutionProviderMpsCommissionVo.setMajorLevelNames(majorLevelNameMap.values().stream().collect(Collectors.toList()));
        }

        //适用国家国家
        List<InstitutionProviderMpsCommissionAreaCountry> institutionProviderMpsCommissionAreaCountries = institutionProviderMpsCommissionAreaCountryMapper.selectList(new LambdaQueryWrapper<InstitutionProviderMpsCommissionAreaCountry>()
                .eq(InstitutionProviderMpsCommissionAreaCountry::getFkInstitutionProviderMpsCommissionId, institutionProviderMpsCommissionVo.getId())
                .eq(InstitutionProviderMpsCommissionAreaCountry::getIsInclude,true));
        if (GeneralTool.isNotEmpty(institutionProviderMpsCommissionAreaCountries)){
            Set<Long> areaCountryIds = institutionProviderMpsCommissionAreaCountries.stream().map(InstitutionProviderMpsCommissionAreaCountry::getFkAreaCountryId).collect(Collectors.toSet());
            institutionProviderMpsCommissionVo.setFkAreaCountryIds(areaCountryIds);
            Map<Long, String> areaCountry = institutionCenterClient.getCountryChnNameByIds(areaCountryIds).getData();
            if (GeneralTool.isNotEmpty(areaCountry)){
                institutionProviderMpsCommissionVo.setAreaCountryNames(areaCountry.values().stream().collect(Collectors.toList()));
            }
        }

        //不适用国家
        List<InstitutionProviderMpsCommissionAreaCountry> institutionProviderMpsCommissionAreaCountriesFalse = institutionProviderMpsCommissionAreaCountryMapper.selectList(new LambdaQueryWrapper<InstitutionProviderMpsCommissionAreaCountry>()
                .eq(InstitutionProviderMpsCommissionAreaCountry::getFkInstitutionProviderMpsCommissionId, institutionProviderMpsCommissionVo.getId())
                .eq(InstitutionProviderMpsCommissionAreaCountry::getIsInclude,false));
        if (GeneralTool.isNotEmpty(institutionProviderMpsCommissionAreaCountriesFalse)){
            Set<Long> areaCountryIds = institutionProviderMpsCommissionAreaCountriesFalse.stream().map(InstitutionProviderMpsCommissionAreaCountry::getFkAreaCountryId).collect(Collectors.toSet());
            institutionProviderMpsCommissionVo.setExcludeAreaCountryIds(areaCountryIds);
            Map<Long, String> areaCountry = institutionCenterClient.getCountryChnNameByIds(areaCountryIds).getData();
            if (GeneralTool.isNotEmpty(areaCountry)){
                institutionProviderMpsCommissionVo.setExcludeAreaCountryNames(areaCountry.values().stream().collect(Collectors.toList()));
            }
        }

        if (GeneralTool.isNotEmpty(institutionProviderMpsCommissionVo.getProgrammesMode())){
            institutionProviderMpsCommissionVo.setProgrammesModeName(ProjectExtraEnum.getValueByKey(institutionProviderMpsCommissionVo.getProgrammesMode(),ProjectExtraEnum.PROGRAMES_MODE));
        }
    }


    @Override
    public List<InstitutionProviderMpsCommissionVo>  getUnActiveCommissionList(MpsCommissionDto mpsCommissionVo, Page page) {
        if (GeneralTool.isEmpty(mpsCommissionVo))
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_search"));
        // 如果不传参，默认用当前登录人的所属公司list
        if (GeneralTool.isEmpty(mpsCommissionVo.getFkCompanyIds())) {
            mpsCommissionVo.setFkCompanyIds(SecureUtil.getCompanyIds().stream().collect(Collectors.toSet()));
        }
        IPage<InstitutionProviderMpsCommission> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<InstitutionProviderMpsCommissionVo> list = institutionProviderMpsCommissionMapper.unActiveCommissionList(iPage, mpsCommissionVo);
        page.setAll((int) iPage.getTotal());
        setName(list);
        return list;
    }
}
