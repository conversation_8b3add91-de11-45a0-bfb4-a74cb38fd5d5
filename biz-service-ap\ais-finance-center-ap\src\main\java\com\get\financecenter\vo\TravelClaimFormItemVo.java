package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 差旅报销单子项
 */
@Data
public class TravelClaimFormItemVo {

    @ApiModelProperty(value = "报销单费用类型名称")
    private String expenseClaimFeeTypeName;

    @ApiModelProperty(value = "关联活动名称")
    private String eventTableName;

    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;


    //=================实体类TravelClaimFormItem=====================
    @ApiModelProperty(value = "差旅报销申请单Id")
    private Long fkTravelClaimFormId;

    @ApiModelProperty(value = "差旅报销费用类型Id")
    private Long fkTravelClaimFeeTypeId;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "报销金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "出发地")
    private String fromPlace;

    @ApiModelProperty(value = "目的地")
    private String toPlace;

    @ApiModelProperty(value = "开始时间")
    private Date fromDate;

    @ApiModelProperty(value = "结束时间")
    private Date toDate;

    @ApiModelProperty(value = "出差天数")
    private BigDecimal tripDays;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "活动类型表名（目标类型表名）")
    private String fkEventTableName;

    @ApiModelProperty(value = "关联活动公司id")
    private Long eventTableCompanyId;

    @ApiModelProperty(value = "活动Id（目标类型表对应记录项Id）")
    private Long fkEventTableId;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @ApiModelProperty("创建时间")
    private Date gmtCreate;


}
