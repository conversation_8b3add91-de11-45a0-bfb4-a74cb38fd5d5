package com.get.insurancecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: VON
 * @Date: 2025/7/31
 * @Version 1.0
 * @apiNote: 结算订单查询条件DTO
 */
@Data
public class SettlementOrderQueryDto {

    @ApiModelProperty(value = "保险公司ID")
    private Long insuranceCompanyId;

    @ApiModelProperty(value = "订单状态（结算状态：0待确认/1已确认）")
    private Integer orderStatus;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "被保险人姓名")
    private String insurantName;

    @ApiModelProperty(value = "保单号")
    private String insuranceNum;

    @ApiModelProperty(value = "时间范围开始（下单时间）")
    private Date startTime;

    @ApiModelProperty(value = "时间范围结束（下单时间）")
    private Date endTime;
}
