package com.get.institutioncenter.controller;

import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.institutioncenter.vo.InstitutionRankingVo;
import com.get.institutioncenter.service.IInstitutionRankingService;
import com.get.institutioncenter.dto.InstitutionRankingDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/25 10:36
 */
@Api(tags = "排名信息管理")
@RestController
@RequestMapping("/institution/institutionRanking")
public class InstitutionRankingController {

    @Resource
    private IInstitutionRankingService rankingService;

    @PostMapping("getWcComprehensiveRanking")
    @ApiOperation("某个或专业综合排名")
    @VerifyLogin(IsVerify = false)
    public ResponseBo<InstitutionRankingVo> getWcComprehensiveRanking(@RequestBody SearchBean<InstitutionRankingDto> page) {
        return new ListResponseBo(rankingService.getWcComprehensiveRanking(page.getData(), page), BeanCopyUtils.objClone(page, Page::new));
    }


    @GetMapping("getWcComprehensiveRankingHome")
    @ApiOperation("综合排名首页")
    @VerifyLogin(IsVerify = false)
    public ResponseBo<InstitutionRankingVo> getWcComprehensiveRankingHome() {
        return new ListResponseBo(rankingService.getWcComprehensiveRankingHome());
    }



    @GetMapping("getWcCourseTypeKey")
    @ApiOperation("小程序获取专业类型")
    @VerifyLogin(IsVerify = false)
    public ResponseBo getWcCourseTypeKey(@RequestParam("typeKey") String typeKey) {
        return new ResponseBo(rankingService.getWcCourseTypeKey(typeKey));
    }

    @GetMapping("getWcMajorRanking")
    @ApiOperation("小程序qs专业排名")
    @VerifyLogin(IsVerify = false)
    public ResponseBo getWcMajorRanking(@RequestParam("typeKey") String typeKey) {
        return new ResponseBo(rankingService.getWcMajorRanking(typeKey));
    }



}
