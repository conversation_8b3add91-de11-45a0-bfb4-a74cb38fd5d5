package com.get.officecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.officecenter.vo.WorkScheduleDateConfigVo;
import com.get.officecenter.entity.WorkScheduleDateConfig;
import com.get.officecenter.mapper.WorkScheduleDateConfigMapper;
import com.get.officecenter.service.WorkScheduleDateConfigService;
import com.get.officecenter.dto.WorkScheduleDateConfigDto;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2022/1/13
 * @TIME: 11:28
 * @Description:
 **/
@Slf4j
@Service
public class WorkScheduleDateConfigServiceImpl implements WorkScheduleDateConfigService {
    @Resource
    private WorkScheduleDateConfigMapper workScheduleDateConfigMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    public WorkScheduleDateConfigVo findWorkScheduleDateConfigById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        WorkScheduleDateConfig workScheduleDateConfig = workScheduleDateConfigMapper.selectById(id);
        if (GeneralTool.isEmpty(workScheduleDateConfig)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        return BeanCopyUtils.objClone(workScheduleDateConfig, WorkScheduleDateConfigVo::new);


    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void add(WorkScheduleDateConfigDto workScheduleDateConfigDto) {
        if (GeneralTool.isEmpty(workScheduleDateConfigDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //查询当前公司的当前日期是否已存在排班
        if (GeneralTool.isNotEmpty(workScheduleDateConfigDto.getFkCompanyId()) && GeneralTool.isNotEmpty(workScheduleDateConfigDto.getScheduleDate())) {
            Integer count = workScheduleDateConfigMapper.checkWorkScheduleDateConfig(null, workScheduleDateConfigDto.getFkCompanyId(), workScheduleDateConfigDto.getScheduleDate());
            if (count > 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("workScheduleDateConfig_duplication"));
            }
        }
        WorkScheduleDateConfig workScheduleDateConfig = BeanCopyUtils.objClone(workScheduleDateConfigDto, WorkScheduleDateConfig::new);
        //设置年份、月份、天数
        Date date = workScheduleDateConfigDto.getScheduleDate();
        LocalDate scheduleDate = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        int year = scheduleDate.getYear();
        int monthValue = scheduleDate.getMonthValue(); // 1~12
        int dayOfMonth = scheduleDate.getDayOfMonth();

        workScheduleDateConfig.setYear(year);
        workScheduleDateConfig.setMonth(monthValue);
        workScheduleDateConfig.setDay(dayOfMonth);
        utilService.updateUserInfoToEntity(workScheduleDateConfig);
        workScheduleDateConfigMapper.insert(workScheduleDateConfig);
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (workScheduleDateConfigMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        workScheduleDateConfigMapper.deleteById(id);
    }

    @Override
    public WorkScheduleDateConfigVo updateWorkScheduleDateConfig(WorkScheduleDateConfigDto workScheduleDateConfigDto) {
        if (workScheduleDateConfigDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        WorkScheduleDateConfig result = workScheduleDateConfigMapper.selectById(workScheduleDateConfigDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        //查询当前公司的当前日期是否已存在排班
        if (GeneralTool.isNotEmpty(workScheduleDateConfigDto.getFkCompanyId()) && GeneralTool.isNotEmpty(workScheduleDateConfigDto.getScheduleDate())) {
            Integer count = workScheduleDateConfigMapper.checkWorkScheduleDateConfig(workScheduleDateConfigDto.getId(), workScheduleDateConfigDto.getFkCompanyId(), workScheduleDateConfigDto.getScheduleDate());
            if (count > 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("workScheduleDateConfig_duplication"));
            }
        }

        WorkScheduleDateConfig workScheduleDateConfig = BeanCopyUtils.objClone(workScheduleDateConfigDto, WorkScheduleDateConfig::new);

        //设置年份、月份、天数
        Date date = workScheduleDateConfigDto.getScheduleDate();
        LocalDate scheduleDate = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        int year = scheduleDate.getYear();
        int monthValue = scheduleDate.getMonthValue(); // 1~12
        int dayOfMonth = scheduleDate.getDayOfMonth();

        workScheduleDateConfig.setYear(year);
        workScheduleDateConfig.setMonth(monthValue);
        workScheduleDateConfig.setDay(dayOfMonth);
        utilService.updateUserInfoToEntity(workScheduleDateConfig);
        workScheduleDateConfigMapper.updateByIdWithNull(workScheduleDateConfig);
        return findWorkScheduleDateConfigById(workScheduleDateConfigDto.getId());
    }

    @Override
    public List<WorkScheduleDateConfigVo> getWorkScheduleDateConfigs(WorkScheduleDateConfigDto workScheduleDateConfigDto, Page page) {
        LambdaQueryWrapper<WorkScheduleDateConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(workScheduleDateConfigDto)) {
            if (GeneralTool.isNotEmpty(workScheduleDateConfigDto.getFkCompanyId())) {
                lambdaQueryWrapper.eq(WorkScheduleDateConfig::getFkCompanyId, workScheduleDateConfigDto.getFkCompanyId());
            }
            if (GeneralTool.isNotEmpty(workScheduleDateConfigDto.getScheduleType())) {
                lambdaQueryWrapper.eq(WorkScheduleDateConfig::getScheduleType, workScheduleDateConfigDto.getScheduleType());
            }
            if (GeneralTool.isNotEmpty(workScheduleDateConfigDto.getYear())) {
                lambdaQueryWrapper.eq(WorkScheduleDateConfig::getYear, workScheduleDateConfigDto.getYear());
            }
            if (GeneralTool.isNotEmpty(workScheduleDateConfigDto.getScheduleDate())) {
                lambdaQueryWrapper.eq(WorkScheduleDateConfig::getScheduleDate, workScheduleDateConfigDto.getScheduleDate());
            }
        }
        lambdaQueryWrapper.orderByAsc(WorkScheduleDateConfig::getScheduleDate);

        IPage<WorkScheduleDateConfig> iPage = workScheduleDateConfigMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<WorkScheduleDateConfig> workScheduleDateConfigs = iPage.getRecords();
        page.setAll((int) iPage.getTotal());

        List<WorkScheduleDateConfigVo> datas = workScheduleDateConfigs.stream().map(workScheduleDateConfig -> BeanCopyUtils.objClone(workScheduleDateConfig, WorkScheduleDateConfigVo::new)).collect(Collectors.toList());
        //公司名称
        Set<Long> fkCompanyIds = datas.stream().map(WorkScheduleDateConfigVo::getFkCompanyId).collect(Collectors.toSet());
        //部门名称
        Set<Long> fFkDepartmentId = datas.stream().map(WorkScheduleDateConfigVo::getFkDepartmentId).collect(Collectors.toSet());

        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkCompanyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkCompanyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }
        Map<Long, String> departmentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkCompanyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getDepartmentNamesByIds(fFkDepartmentId);
            if (result.isSuccess() && result.getData() != null) {
                departmentNamesByIds = result.getData();
            }
        }
        for (WorkScheduleDateConfigVo workScheduleDateConfigVo : datas) {
            workScheduleDateConfigVo.setFkCompanyName(companyNamesByIds.get(workScheduleDateConfigVo.getFkCompanyId()));
            workScheduleDateConfigVo.setFkDepartmentName(departmentNamesByIds.get(workScheduleDateConfigVo.getFkDepartmentId()));

            //排班类型
            switch (workScheduleDateConfigVo.getScheduleType()) {
                case "0":
                    workScheduleDateConfigVo.setScheduleTypeName("节假日");
                    break;
                case "1":
                    workScheduleDateConfigVo.setScheduleTypeName("工作日");
                    break;
                default:
                    break;
            }
        }
        return datas;
    }

    @Override
    public List<BaseSelectEntity> getYearSelect(Long fkCompanyId) {
        return workScheduleDateConfigMapper.getYearSelect(fkCompanyId);
    }

    @Override
    public List<WorkScheduleDateConfigVo> getAllWorkScheduleDateConfigs(WorkScheduleDateConfigDto workScheduleDateConfigDto) {
        LambdaQueryWrapper<WorkScheduleDateConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(workScheduleDateConfigDto)) {
            if (GeneralTool.isNotEmpty(workScheduleDateConfigDto.getFkCompanyId())) {
                lambdaQueryWrapper.eq(WorkScheduleDateConfig::getFkCompanyId, workScheduleDateConfigDto.getFkCompanyId());
            }
            if (GeneralTool.isNotEmpty(workScheduleDateConfigDto.getScheduleType())) {
                lambdaQueryWrapper.eq(WorkScheduleDateConfig::getScheduleType, workScheduleDateConfigDto.getScheduleType());
            }
            if (GeneralTool.isNotEmpty(workScheduleDateConfigDto.getScheduleDate())) {
                lambdaQueryWrapper.eq(WorkScheduleDateConfig::getScheduleDate, workScheduleDateConfigDto.getScheduleDate());
            }
        }
        lambdaQueryWrapper.orderByAsc(WorkScheduleDateConfig::getScheduleDate);
        List<WorkScheduleDateConfig> workScheduleDateConfigs = workScheduleDateConfigMapper.selectList(lambdaQueryWrapper);
        List<WorkScheduleDateConfigVo> datas = workScheduleDateConfigs.stream().map(workScheduleDateConfig -> BeanCopyUtils.objClone(workScheduleDateConfig, WorkScheduleDateConfigVo::new)).collect(Collectors.toList());
        //公司名称
        Set<Long> fkCompanyIds = datas.stream().map(WorkScheduleDateConfigVo::getFkCompanyId).collect(Collectors.toSet());
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkCompanyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkCompanyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }
        for (WorkScheduleDateConfigVo workScheduleDateConfigVo : datas) {
            workScheduleDateConfigVo.setFkCompanyName(companyNamesByIds.get(workScheduleDateConfigVo.getFkCompanyId()));
            //排班类型
            switch (workScheduleDateConfigVo.getScheduleType()) {
                case "0":
                    workScheduleDateConfigVo.setScheduleTypeName("节假日");
                    break;
                case "1":
                    workScheduleDateConfigVo.setScheduleTypeName("工作日");
                    break;
                default:
                    break;
            }
        }
        return datas;
    }

    @Override
    public List<WorkScheduleDateConfig> getWorkScheduleDateConfigByDate(Date startTime,Date endTime,Long fkCompanyId,Long fkDepartmentId) {
        //根据日期获取对应月份的所有排班
        return workScheduleDateConfigMapper.getWorkScheduleDateConfigByDate(startTime,endTime,fkCompanyId,fkDepartmentId );
    }

}
