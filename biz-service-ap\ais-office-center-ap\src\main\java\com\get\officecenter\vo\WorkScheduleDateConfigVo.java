package com.get.officecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.officecenter.entity.WorkScheduleDateConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2022/1/11
 * @TIME: 10:34
 * @Description:
 **/
@Data
public class WorkScheduleDateConfigVo extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    @UpdateWithNull
    private Long fkDepartmentId;
    /**
     * 排班类型：0节假日/1工作日
     */
    @ApiModelProperty(value = "排班类型：0节假日/1工作日")
    private String scheduleType;
    /**
     * 排班日期
     */
    @ApiModelProperty(value = "排班日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date scheduleDate;
    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private Integer year;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private Integer month;
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private Integer day;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @UpdateWithNull
    private String remark;
    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    private String fkCompanyName;
    /**
     * 部门名称
     */
    @ApiModelProperty("部门名称")
    private String fkDepartmentName;
    /**
     * 排班类型名
     */
    @ApiModelProperty("排班类型名")
    private String scheduleTypeName;
}
