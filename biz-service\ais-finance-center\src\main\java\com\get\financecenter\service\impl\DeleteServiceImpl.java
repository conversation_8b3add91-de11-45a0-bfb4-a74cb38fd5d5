package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.financecenter.dao.MediaAndAttachedMapper;
import com.get.financecenter.dao.ProviderAccountMapper;
import com.get.financecenter.dao.ProviderContactPersonMapper;
import com.get.financecenter.entity.FMediaAndAttached;
import com.get.financecenter.service.IDeleteService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE: 2020/11/26
 * @TIME: 17:03
 * @Description:
 **/
@Service
public class DeleteServiceImpl implements IDeleteService {
    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private ProviderAccountMapper providerAccountMapper;
    @Resource
    private ProviderContactPersonMapper providerContactPersonMapper;

    @Override
    public Boolean deleteValidateProvider(Long providerId) {
        if (providerAccountMapper.providerAccountIsEmpty(providerId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("provider_account_data_association"));
        }
        if (providerContactPersonMapper.providerContactPersonIsEmpty(providerId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("provider_contactPerson_data_association"));
        }
        deleteMedia(providerId, TableEnum.FINANCE_PROVIDER.key);
        return true;
    }

    @Override
    public void deleteMedia(Long id, String tableName) {
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTableName", tableName);
//        criteria.andEqualTo("fkTableId", id);
//        mediaAndAttachedMapper.deleteByExample(example);
        this.mediaAndAttachedMapper.delete(Wrappers.<FMediaAndAttached>query().lambda()
                .eq(FMediaAndAttached::getFkTableName, tableName)
                .eq(FMediaAndAttached::getFkTableId, id));
    }
}
