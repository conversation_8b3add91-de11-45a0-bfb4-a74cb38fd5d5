package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.permissioncenter.entity.RInstitutionPermissionGroupStaff;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

/**
 * 学校权限组别员工关系Mapper层
 */
@Mapper
public interface RInstitutionPermissionGroupStaffMapper extends BaseMapper<RInstitutionPermissionGroupStaff> {

    Set<Long> selectByFkInstitutionPermissionGroupId(@Param("fkInstitutionPermissionGroupId") Long fkInstitutionPermissionGroupId);
}

