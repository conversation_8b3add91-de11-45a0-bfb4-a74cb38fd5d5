package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/5/8 11:21
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionSponsorDto extends BaseVoEntity {

    @ApiModelProperty(value = "状态名称")
    private String statusName;

    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    private Long fkConventionId;

    /**
     * 学校提供商Id（费用归口）
     */
    @ApiModelProperty(value = "学校提供商Id（费用归口）")
    private Long fkInstitutionProviderId;

    /**
     * 活动费用绑定Id
     */
    @ApiModelProperty(value = "活动费用绑定Id")
    private Long fkEventCostId;

    /**
     * 赞助商名称
     */
    @ApiModelProperty(value = "赞助商名称")
    private String sponsorName;

    /**
     * 回执码，8位数字随机数(和报名一致)
     */
    @ApiModelProperty(value = "回执码，8位数字随机数(和报名一致)")
    private String receiptCode;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 费用金额
     */
    @ApiModelProperty(value = "费用金额")
    private BigDecimal feeOther;

    /**
     * 费用金额（折合人民币）
     */
    @ApiModelProperty(value = "费用金额（折合人民币）")
    private BigDecimal feeOtherCny;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 状态：0新建/1已确认/2已收款
     */
    @ApiModelProperty(value = "状态：0新建/1已确认/2已收款")
    private Integer status;

    //自定义内容
    /**
     * 所选赞助类型对象id集合
     */
    @ApiModelProperty(value = "所选赞助对象集合")
    private List<ConventionSponsorSponsorFeeDto> conventionSponsorFeeVoList;

    /**
     * 学校提供商名称
     */
    @ApiModelProperty(value = "学校提供商名称")
    private String institutionProviderName;

    
}
