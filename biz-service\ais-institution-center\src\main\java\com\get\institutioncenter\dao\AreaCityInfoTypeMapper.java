package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.AreaCityInfoType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AreaCityInfoTypeMapper extends BaseMapper<AreaCityInfoType> {
    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(AreaCityInfoType record);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值+1
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

    /**
     * @return java.lang.String
     * @Description :通过id查找对应类型名称
     * @Param [areaCityInfoTypeId]
     * <AUTHOR>
     */
    String getAreaCityInfoTypeNameById(@Param("areaCityInfoTypeId") Long areaCityInfoTypeId);
}